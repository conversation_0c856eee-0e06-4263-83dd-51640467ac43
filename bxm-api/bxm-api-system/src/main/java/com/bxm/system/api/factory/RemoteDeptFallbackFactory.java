package com.bxm.system.api.factory;

import com.bxm.common.core.domain.R;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.*;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 部门服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteDeptFallbackFactory implements FallbackFactory<RemoteDeptService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteDeptFallbackFactory.class);

    @Override
    public RemoteDeptService create(Throwable throwable)
    {
        log.error("部门服务调用失败:{}", throwable.getMessage());
        return new RemoteDeptService()
        {
            @Override
            public R<SysDept> getDeptInfo(Long deptId) {
                return R.fail("获取部门失败:" + throwable.getMessage());
            }

            @Override
            public R<UserDeptDTO> userDeptList(Long userId, Long deptId) {
                return R.fail("获取用户可查看部门失败:" + throwable.getMessage());
            }

            @Override
            public R<UserDeptDTO> workOrderUserDeptList(Long userId, Long deptId) {
                return R.fail("获取工单列表用户可查看部门失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysDept>> getDeptByUserDepts(UserDeptDTO data) {
                return R.fail("获取用户可查看部门信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysEmployee>> getEmployeesBySecondDeptIdAndUserId(Long deptId, Long userId) {
                return R.fail("获取用户所有部门信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysDept>> getAllDeptBySecondDeptId(Long deptId) {
                return R.fail("获取二级部门下所有部门信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysDept>> getByDeptIds(List<Long> deptIds) {
                return R.fail("批量获取部门信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<Long>> getAllChildrenIdByTopDeptId(Long deptId) {
                return R.fail("根据部门id获取所有子部门id失败:" + throwable.getMessage());
            }

            @Override
            public R<List<Long>> selectDeptIdsByDeptEmployeeName(String deptEmployeeName) {
                return R.fail("根据部门或员工名获取所有部门id失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysDept>> getAllDept() {
                return R.fail("获取所有部门失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysRole>> getAllRole() {
                return R.fail("获取所有角色失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysDept>> getByParentId(Long deptId) {
                return R.fail("获取子部门失败:" + throwable.getMessage());
            }

            @Override
            public R remoteAddAccountUser(RemoteAccountUserAddVO vo, String source) {
                return R.fail("创建员工账号失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteTreeSelect>> remoteCommonDeptEmployeeTreeSelect(Long deptId, Integer deptType, Integer selectType, Integer showType, Integer level, Long businessDeptId, Long topDeptId, Integer isFilterHead, String source) {
                return R.fail("获取组织架构失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> checkDateIsWorkDay(Integer year, Integer month, Integer day, String source) {
                return R.fail("校验日期是否为工作日失败:" + throwable.getMessage());
            }

            @Override
            public R<List<String>> getWeekDays(String source) {
                return R.fail("获取休息日失败:" + throwable.getMessage());
            }
        };
    }
}
