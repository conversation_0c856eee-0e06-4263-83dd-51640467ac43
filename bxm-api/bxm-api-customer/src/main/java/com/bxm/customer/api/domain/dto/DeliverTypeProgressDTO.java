package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliverTypeProgressDTO {

    @ApiModelProperty("总数量（分母）")
    private Long totalCount;

    @ApiModelProperty("待提交数量（分子）")
    private Long waitSubmitCount;

    @ApiModelProperty("待交付数量（分子）")
    private Long waitDeliverCount;

    @ApiModelProperty("待确认数量（分子）")
    private Long waitConfirmCount;

    @ApiModelProperty("已完成数量（分子）")
    private Long completedCount;

    @ApiModelProperty("待提交百分比")
    private BigDecimal waitSubmitPercent;

    @ApiModelProperty("待交付百分比")
    private BigDecimal waitDeliverPercent;

    @ApiModelProperty("待确认百分比")
    private BigDecimal waitConfirmPercent;

    @ApiModelProperty("已完成百分比")
    private BigDecimal completedPercent;
}
