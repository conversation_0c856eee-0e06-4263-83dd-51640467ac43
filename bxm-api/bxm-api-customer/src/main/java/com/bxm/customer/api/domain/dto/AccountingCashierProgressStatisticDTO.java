package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierProgressStatisticDTO {

    @ApiModelProperty("今日流水录入数（实际）")
    private Long bankCompleteRealCountDaily;

    @ApiModelProperty("今日入账交付数（实际）")
    private Long inAccountCompleteRealCountDaily;

    @ApiModelProperty("今日结账完成数（实际）")
    private Long inAccountEndRealCountDaily;

    @ApiModelProperty("今日新提交数（实际）")
    private Long newCreateRealCountDaily;

    @ApiModelProperty("今日流水录入数（展示）")
    private Long bankCompleteShowCountDaily;

    @ApiModelProperty("今日入账交付数（展示）")
    private Long inAccountCompleteShowCountDaily;

    @ApiModelProperty("今日结账完成数（展示）")
    private Long inAccountEndShowCountDaily;

    @ApiModelProperty("今日新提交数（展示）")
    private Long newCreateShowCountDaily;

    @ApiModelProperty("当前展示值的加成比例")
    private Integer rate;
}
