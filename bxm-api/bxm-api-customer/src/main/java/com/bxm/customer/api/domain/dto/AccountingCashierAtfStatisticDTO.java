package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierAtfStatisticDTO {

    @ApiModelProperty("今日账务交付数（实际）")
    private Long accountingCashierOperLogRealCount;

    @ApiModelProperty("今日账务交付数-从其他系统来的（实际）")
    private Long accountingCashierOperLogOtherRealCount;

    @ApiModelProperty("ATF协作百分比")
    private BigDecimal accountingCashierAtfPercent;
}
