package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliverAtfStatisticDTO {

    @ApiModelProperty("今日交付任务数（实际）")
    private Long deliverOperLogRealCount;

    @ApiModelProperty("今日交付任务数-从其他系统来的（实际）")
    private Long deliverOperLogOtherRealCount;

    @ApiModelProperty("ATF协作百分比")
    private BigDecimal deliverAtfPercent;

    @ApiModelProperty("今日交付任务数（展示）")
    private Long deliverOperLogShowCount;

    @ApiModelProperty("当前展示值的加成比例")
    private Integer rate;
}
