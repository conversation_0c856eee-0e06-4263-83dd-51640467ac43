package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierMonthProgressDTO {

    @ApiModelProperty("流水完成率")
    private BigDecimal bankCompleteRate;

    @ApiModelProperty("入账率")
    private BigDecimal inAccountCompleteRate;

    @ApiModelProperty("结账率")
    private BigDecimal inAccountEndRate;
}
