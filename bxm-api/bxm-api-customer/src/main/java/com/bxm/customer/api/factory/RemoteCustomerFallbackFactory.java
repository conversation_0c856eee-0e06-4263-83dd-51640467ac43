package com.bxm.customer.api.factory;

import com.bxm.common.core.domain.PageResult;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.api.RemoteCustomerService;
import com.bxm.customer.api.domain.dto.*;
import com.bxm.customer.api.domain.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class RemoteCustomerFallbackFactory implements FallbackFactory<RemoteCustomerService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteCustomerFallbackFactory.class);

    @Override
    public RemoteCustomerService create(Throwable throwable) {
        log.error("客户服务调用失败:{}", throwable.getMessage());
        return new RemoteCustomerService() {
            @Override
            public R<Boolean> checkHasCustomerService(Long deptId) {
                return R.fail("获取部门是否关联服务失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerDTO>> getCreditCodeByListCreditCode(List<String> creditCodes) {
                return R.fail("查询已存在信用代码失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerDTO>> getByTaxNumberList(List<String> taxNumberList) {
                return R.fail("根据税号批量查询失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerPeriodDTO>> getCustomerPeriodByCreditCodeAndPeriod(RemoteCustomerPeriodVO vo) {
                return R.fail("根据信用代码和账期批量查询账期失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerPeriodDTO>> getCustomerPeriodByListCreditCodeAndPeriod(List<RemoteCreditCodePeriodVO> voList) {
                return R.fail("根据信用代码和账期批量查询账期失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerPeriodDTO>> getCustomerPeriodByPeriodRange(Integer periodMin, Integer periodMax, Long deptId, Long userId) {
                return R.fail("根据账期范围查询账期失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerServiceIncomeDTO>> getCustomerServiceIncomeByCustomerServiceIdAndPeriod(List<RemoteCustomerServiceIncomeSearchVO> voList, String source) {
                return R.fail("批量查询收入失败:" + throwable.getMessage());
            }

            @Override
            public R remoteUpdateOrCreateIncome(RemoteCustomerServiceIncomeVO vo, String source) {
                return R.fail("远程创建或更新收入失败:" + throwable.getMessage());
            }

            @Override
            public R remoteUpdateCustomerIncome(List<Long> customerServiceIds, String source) {
                return R.fail("更新客户收入失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerServicePeriodYearDTO>> getCustomerServicePeriodYearByCustomerServiceIdsAndPeriod(RemoteCustomerServicePeriodYearSearchVO vo, String source) {
                return R.fail("获取年度汇总失败:" + throwable.getMessage());
            }

            @Override
            public R remoteUpdateCustomerServicePeriodYear(RemoteCustomerServicePeriodYearVO vo, String source) {
                return R.fail("编辑年度汇总失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerInAccountMaxPeriodDTO>> getCustomerInAccountMaxPeriod(List<Long> customerServiceIds, String source) {
                return R.fail("获取客户入账最大账期失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerInAccountMaxPeriodDTO>> getCustomerInAccountGroupRelation(List<Long> customerServiceIds, String source) {
                return R.fail("获取客户组织关系失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerBankAccountDTO>> getCustomerBankAccountNumberByBankNumbers(RemoteCustomerBankAccountNumberSearchVO vo, String source) {
                return R.fail("获取总部下的银行账号信息失败:" + throwable.getMessage());
            }

            @Override
            public R<RemoteCustomerBankAccountDTO> getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(String bankAccountNumber, Long businessTopDeptId, String source) {
                return R.fail("根据银行账号获取同总部下信息失败:" + throwable.getMessage());
            }

            @Override
            public R createCustomerBankAccount(RemoteCustomerBankAccountVO vo, String source) {
                 return R.fail("创建银行账号信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<String>> getAllBankNames() {
                return R.fail("获取所有银行名称失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteAccountingCashierDTO>> remoteAccountingCashierList(RemoteAccountingCashierSearchVO vo, String source) {
                return R.fail("查询账务列表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteAccountingCashierSimpleDTO>> getAccountingCashierByPeriodIdsAndAccountingCashierType(RemoteAccountingCashierPeriodTypeVO vo, String source) {
                return R.fail("根据账期id列表，账务类型查询账务数据失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerBankAccountDTO>> getCustomerBankListByCustomerServiceIds(List<Long> customerServiceIds, String source) {
                return R.fail("查询客户银行信息失败:" + throwable.getMessage());
            }

            @Override
            public R remoteCreateAccountingCashier(RemoteAccountingCashierCreateVO vo, String source) {
                return R.fail("创建账务交付单失败:" + throwable.getMessage());
            }

            @Override
            public R remoteDeliver(RemoteAccountingCashierOperateVO vo, String source) {
                return R.fail("交付账务交付单失败:" + throwable.getMessage());
            }

            @Override
            public R remoteDealException(RemoteAccountingCashierDealExceptionVO vo, String source) {
                return R.fail("账务交付单处理异常失败:" + throwable.getMessage());
            }

            @Override
            public R remoteRpaUpdate(RemoteAccountingCashierRpaUpdateVO vo, String source) {
                return R.fail("更新RPA失败:" + throwable.getMessage());
            }

            @Override
            public R remoteUpdateProfit(RemoteAccountingCashierUpdateProfitVO vo, String source) {
                return R.fail("更新利润失败:" + throwable.getMessage());
            }

            @Override
            public R remoteSupplementFiles(RemoteAccountingCashierSupplementFileVO vo, String source) {
                return R.fail("补充附件失败:" + throwable.getMessage());
            }

            @Override
            public R remoteCheckFiles(RemoteAccountingCashierCheckFileVO vo, String source) {
                return R.fail("核对材料失败:" + throwable.getMessage());
            }

            @Override
            public R remoteUpdateBankPaymentResultSettleAccountStatus(RemoteUpdateBankPaymentResultSettleAccountStatusVO vo, String source) {
                return R.fail("更新银行流水结果和结账状态失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteMaterialFileSimpleErrorVO>> getMaterialFilesByIds(List<Long> ids, String source) {
                return R.fail("查询材料文件失败:" + throwable.getMessage());
            }

            @Override
            public R<List<String>> getBankAccountNumberByListBankAccountNumber(List<String> bankAccountNumbers, String source) {
                return R.fail("获取已存在的银行流水交付单失败:" + throwable.getMessage());
            }

            @Override
            public R updateAccountingCashierStatusByCustomerServiceIds(List<Long> customerServiceIds, String source) {
                return R.fail("更新银行流水结果和结账状态失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteMaterialPushPreviewListDTO>> getPushReviewErrorList(String batchNo, String source) {
                return R.fail("获取推送异常数据失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerPeriodBankDTO>> getCustomerPeriodBankList(List<String> bankAccountNumbers, String source) {
                return R.fail("获取银行账号关联客户账期失败:" + throwable.getMessage());
            }

            @Override
            public R remoteCreateMaterialDeliver(RemoteMaterialDeliver vo, String source) {
                return R.fail("创建材料交接单失败:" + throwable.getMessage());
            }

            @Override
            public R remoteAddSysAccount(RemoteCustomerServiceSysAccountVO vo, String source) {
                return R.fail("创建系统账号失败:" + throwable.getMessage());
            }

            @Override
            public R remoteUpdateCustomerTag(RemoteUpdateCustomerTagVO vo, String source) {
                return R.fail("更新服务标签失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerTagDTO>> getCustomerTagsByCustomerServiceIds(List<Long> customerServiceIds, String source) {
                return R.fail("获取服务标签失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteWorkOrderDTO>> remoteWorkOrderList(RemoteWorkOrderSearchVO vo, String source) {
                return R.fail("获取工单列表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteWorkOrderDTO>> remoteWorkOrderListForXm(RemoteWorkOrderSearchVO vo, String source) {
                return R.fail("获取工单列表失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> remoteCommentWorkOrder(RemoteWorkOrderCommentVO vo, String source) {
                return R.fail("评论工单失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> remoteFollowUp(RemoteWorkOrderFollowUpVO vo, String source) {
                return R.fail("跟进工单失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> remoteTransmit(RemoteWorkOrderTransmitVO vo, String source) {
                return R.fail("转交工单失败:" + throwable.getMessage());
            }

            @Override
            public R<List<String>> taxCheckList() {
                return R.fail("获取所有税种失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteQualityCheckingRecordDTO>> getCheckingRecordList(List<Long> customerServicePeriodMonthIds, String source) {
                return R.fail("查询质检中记录失败:" + throwable.getMessage());
            }

            @Override
            public R<RemoteCustomerPeriodDTO> remoteCreateQualityChecking(RemoteQualityCheckingCreateVO vo, String source) {
                return R.fail("创建质检记录失败:" + throwable.getMessage());
            }

            @Override
            public R remoteSendQualityCheckingTask(RemoteSendQualityCheckingTaskVO vo, String source) {
                return R.fail("发送质检任务失败:" + throwable.getMessage());
            }

            @Override
            public R<RemoteWorkOrderDetailDTO> remoteWorkOrderDetail(Long id, Long userId, String source) {
                return R.fail("获取工单详情失败:" + throwable.getMessage());
            }

            @Override
            public R<PageResult<CustomerServiceXmDTO>> customerServiceXmList(UserCustomerSearchVO vo, String source) {
                return R.fail("获取客户列表失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> remoteConfirmWorkOrder(RemoteWorkOrderConfirmVO vo, String source) {
                return R.fail("确认工单失败:" + throwable.getMessage());
            }

            @Override
            public R<CustomerServiceXmDTO> userCustomerById(CommonIdVO vo, String source) {
                return R.fail("获取客户信息失败:" + throwable.getMessage());
            }

            @Override
            public R<DeliverAtfStatisticDTO> getDeliverAtfStatistic(String source) {
                return R.fail("获取税务atf协作数据失败:" + throwable.getMessage());
            }

            @Override
            public R<DeliverProgressStatisticDTO> getDeliverProgressStatistic(String source) {
                return R.fail("获取税务交付进度数据失败:" + throwable.getMessage());
            }

            @Override
            public R<AccountingCashierProgressStatisticDTO> getAccountingCashierProgressStatistic(String source) {
                return R.fail("获取账务进度数据失败:" + throwable.getMessage());
            }

            @Override
            public R<AccountingCashierAtfStatisticDTO> getAccountingCashierAtfStatistic(String source) {
                return R.fail("获取账务atf协作数据失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<String, AccountingCashierMonthProgressDTO>> getAccountingCashierMonthProgressStatistic(String source) {
                return R.fail("获取账务年进度数据失败:" + throwable.getMessage());
            }

            @Override
            public R<AtfStatisticDTO> getAtfStatistic(String source) {
                return R.fail("获取atf统计数据失败:" + throwable.getMessage());
            }
        };
    }
}
