package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AtfTypeStatisticDTO {

    @ApiModelProperty("atf类型")
    private String atfType;

    @ApiModelProperty("今日任务量")
    private Long taskCount;

    @ApiModelProperty("平均耗时（秒），展示规则前端自行处理")
    private Long averageSecond;
}
