package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliverProgressStatisticDTO {

    @ApiModelProperty("个税税务进度数据信息")
    private DeliverTypeProgressDTO personTaxProgress;

    @ApiModelProperty("国税税务进度数据信息")
    private DeliverTypeProgressDTO nationalTaxProgress;

    @ApiModelProperty("医社保税务进度数据信息")
    private DeliverTypeProgressDTO medicalSocialTaxProgress;
}
