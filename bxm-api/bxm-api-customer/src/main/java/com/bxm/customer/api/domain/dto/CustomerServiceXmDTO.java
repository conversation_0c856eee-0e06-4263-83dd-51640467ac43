package com.bxm.customer.api.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceXmDTO {

    private Long id;

    private String customerName;

    private String creditCode;

    private String taxNumber;

    private String startDate;

    private String endDate;

    private Integer serviceStatus;

    private List<String> tagList;
}
