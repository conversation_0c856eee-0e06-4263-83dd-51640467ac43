package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteAccountingCashierOperateVO {

    @ApiModelProperty("批量操作的id列表")
    private List<Long> ids;

    @ApiModelProperty("单个操作的id")
    private Long id;

    @ApiModelProperty("账务类型，1-入账，2-流水，3-改账")
    private Integer type;

    @ApiModelProperty("入账交付相关信息（仅交付操作且类型为入账需要传）")
    private RemoteInAccountDeliverVO inAccountDeliverInfo;

    @ApiModelProperty("交付结果，1-正常，2-无账务，3-无需交付，4-异常（仅交付操作时需要传）")
    private Integer deliverResult;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    private Integer materialIntegrity;

    @ApiModelProperty("对账单余额")
    private BigDecimal statementBalance;

    @ApiModelProperty("备注（仅交付和退回操作时需要传）")
    private String remark;

    @ApiModelProperty("附件列表（仅交付和退回操作时需要传）")
    private List<CommonFileVO> files;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private String operName;

    @ApiModelProperty(hidden = true)
    private Boolean isCoverFiles;
}
