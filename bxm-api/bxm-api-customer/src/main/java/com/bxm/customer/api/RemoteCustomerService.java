package com.bxm.customer.api;

import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.constant.ServiceNameConstants;
import com.bxm.common.core.domain.PageResult;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.api.domain.dto.*;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.customer.api.factory.RemoteCustomerFallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 客户服务
 */
@FeignClient(contextId = "remoteCustomerService", value = ServiceNameConstants.CUSTOMER_SERVICE, fallbackFactory = RemoteCustomerFallbackFactory.class)
public interface RemoteCustomerService {

    @GetMapping("/bxmCustomer/customerService/checkHasCustomerService")
    R<Boolean> checkHasCustomerService(@RequestParam("deptId") Long deptId);

    @PostMapping("/bxmCustomer/customerService/getCreditCodeByListCreditCode")
    R<List<RemoteCustomerDTO>> getCreditCodeByListCreditCode(@RequestBody List<String> creditCodes);

    @PostMapping("/bxmCustomer/customerService/getByTaxNumberList")
    R<List<RemoteCustomerDTO>> getByTaxNumberList(@RequestBody List<String> taxNumberList);

    @PostMapping("/bxmCustomer/customerService/getCustomerPeriodByCreditCodeAndPeriod")
    R<List<RemoteCustomerPeriodDTO>> getCustomerPeriodByCreditCodeAndPeriod(@RequestBody RemoteCustomerPeriodVO vo);

    @PostMapping("/bxmCustomer/customerService/getCustomerPeriodByListCreditCodeAndPeriod")
    R<List<RemoteCustomerPeriodDTO>> getCustomerPeriodByListCreditCodeAndPeriod(@RequestBody List<RemoteCreditCodePeriodVO> voList);

    @GetMapping("/bxmCustomer/customerService/getCustomerPeriodByPeriodRange")
    R<List<RemoteCustomerPeriodDTO>> getCustomerPeriodByPeriodRange(@RequestParam("periodMin") Integer periodMin,
                                                                    @RequestParam("periodMax") Integer periodMax,
                                                                    @RequestParam("deptId") Long deptId,
                                                                    @RequestParam("userId") Long userId);

    @PostMapping("/bxmCustomer/income/getCustomerServiceIncomeByCustomerServiceIdAndPeriod")
    R<List<RemoteCustomerServiceIncomeDTO>> getCustomerServiceIncomeByCustomerServiceIdAndPeriod(@RequestBody List<RemoteCustomerServiceIncomeSearchVO> voList, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/income/remoteUpdateOrCreateIncome")
    R remoteUpdateOrCreateIncome(@RequestBody RemoteCustomerServiceIncomeVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/income/remoteUpdateCustomerIncome")
    R remoteUpdateCustomerIncome(@RequestBody List<Long> customerServiceIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/getCustomerServicePeriodYearByCustomerServiceIdsAndPeriod")
    R<List<RemoteCustomerServicePeriodYearDTO>> getCustomerServicePeriodYearByCustomerServiceIdsAndPeriod(@RequestBody RemoteCustomerServicePeriodYearSearchVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/remoteUpdateCustomerServicePeriodYear")
    R remoteUpdateCustomerServicePeriodYear(@RequestBody RemoteCustomerServicePeriodYearVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/getCustomerInAccountMaxPeriod")
    R<List<RemoteCustomerInAccountMaxPeriodDTO>> getCustomerInAccountMaxPeriod(@RequestBody List<Long> customerServiceIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/getCustomerInAccountGroupRelation")
    R<List<RemoteCustomerInAccountMaxPeriodDTO>> getCustomerInAccountGroupRelation(@RequestBody List<Long> customerServiceIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/getCustomerBankAccountNumberByBankNumbers")
    R<List<RemoteCustomerBankAccountDTO>> getCustomerBankAccountNumberByBankNumbers(@RequestBody RemoteCustomerBankAccountNumberSearchVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/customerService/getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId")
    R<RemoteCustomerBankAccountDTO> getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(@RequestParam("bankAccountNumber") String bankAccountNumber,
                                                                                                 @RequestParam("businessTopDeptId") Long businessTopDeptId,
                                                                                                 @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/createCustomerBankAccount")
    R createCustomerBankAccount(@RequestBody RemoteCustomerBankAccountVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/select/getAllBankNames")
    R <List<String>> getAllBankNames();

    @PostMapping("/bxmCustomer/accountingCashier/remoteAccountingCashierList")
    R<List<RemoteAccountingCashierDTO>> remoteAccountingCashierList(@RequestBody RemoteAccountingCashierSearchVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/getAccountingCashierByPeriodIdsAndAccountingCashierType")
    R<List<RemoteAccountingCashierSimpleDTO>> getAccountingCashierByPeriodIdsAndAccountingCashierType(@RequestBody RemoteAccountingCashierPeriodTypeVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/getCustomerBankListByCustomerServiceIds")
    R<List<RemoteCustomerBankAccountDTO>> getCustomerBankListByCustomerServiceIds(@RequestBody List<Long> customerServiceIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/remoteCreateAccountingCashier")
    R remoteCreateAccountingCashier(@RequestBody RemoteAccountingCashierCreateVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/remoteDeliver")
    R remoteDeliver(@RequestBody RemoteAccountingCashierOperateVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/remoteDealException")
    R remoteDealException(@RequestBody RemoteAccountingCashierDealExceptionVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/remoteRpaUpdate")
    R remoteRpaUpdate(@RequestBody RemoteAccountingCashierRpaUpdateVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/remoteUpdateProfit")
    R remoteUpdateProfit(@RequestBody RemoteAccountingCashierUpdateProfitVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/remoteSupplementFiles")
    R remoteSupplementFiles(@RequestBody RemoteAccountingCashierSupplementFileVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/remoteCheckFiles")
    R remoteCheckFiles(@RequestBody RemoteAccountingCashierCheckFileVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/remoteUpdateBankPaymentResultSettleAccountStatus")
    R remoteUpdateBankPaymentResultSettleAccountStatus(@RequestBody RemoteUpdateBankPaymentResultSettleAccountStatusVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/getMaterialFilesByIds")
    R<List<RemoteMaterialFileSimpleErrorVO>> getMaterialFilesByIds(@RequestBody List<Long> ids, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/getBankAccountNumberByListBankAccountNumber")
    R<List<String>> getBankAccountNumberByListBankAccountNumber(@RequestBody List<String> bankAccountNumbers, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/accountingCashier/updateAccountingCashierStatusByCustomerServiceIds")
    R updateAccountingCashierStatusByCustomerServiceIds(@RequestBody List<Long> customerServiceIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/material/deliver/getPushReviewErrorList")
    R<List<RemoteMaterialPushPreviewListDTO>> getPushReviewErrorList(@RequestBody String batchNo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/getCustomerPeriodBankList")
    R<List<RemoteCustomerPeriodBankDTO>> getCustomerPeriodBankList(@Param("bankAccountNumbers") List<String> bankAccountNumbers, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/material/deliver/remoteCreateMaterialDeliver")
    R remoteCreateMaterialDeliver(@RequestBody RemoteMaterialDeliver vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping(value = "/bxmCustomer/customerService/sysAccount/remoteAdd")
    R remoteAddSysAccount(@RequestBody RemoteCustomerServiceSysAccountVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping(value = "/bxmCustomer/customerService/remoteUpdateCustomerTag")
    R remoteUpdateCustomerTag(@RequestBody RemoteUpdateCustomerTagVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/getCustomerTagsByCustomerServiceIds")
    R<List<RemoteCustomerTagDTO>> getCustomerTagsByCustomerServiceIds(@RequestBody List<Long> customerServiceIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/workOrder/remoteWorkOrderList")
    R<List<RemoteWorkOrderDTO>> remoteWorkOrderList(@RequestBody RemoteWorkOrderSearchVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/workOrder/remoteWorkOrderListForXm")
    R<List<RemoteWorkOrderDTO>> remoteWorkOrderListForXm(@RequestBody RemoteWorkOrderSearchVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/workOrder/remoteCommentWorkOrder")
    R<Boolean> remoteCommentWorkOrder(@RequestBody RemoteWorkOrderCommentVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/workOrder/remoteFollowUp")
    R<Boolean> remoteFollowUp(@RequestBody RemoteWorkOrderFollowUpVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/workOrder/remoteTransmit")
    R<Boolean> remoteTransmit(@RequestBody RemoteWorkOrderTransmitVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/select/taxCheckList")
    R<List<String>> taxCheckList();

    @PostMapping("/bxmCustomer/quality/checking/getCheckingRecordList")
    R<List<RemoteQualityCheckingRecordDTO>> getCheckingRecordList(@RequestBody List<Long> customerServicePeriodMonthIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/quality/checking/remoteCreateQualityChecking")
    R<RemoteCustomerPeriodDTO> remoteCreateQualityChecking(@RequestBody RemoteQualityCheckingCreateVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/quality/checking/remoteSendQualityCheckingTask")
    R remoteSendQualityCheckingTask(@RequestBody RemoteSendQualityCheckingTaskVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/workOrder/remoteWorkOrderDetail")
    R<RemoteWorkOrderDetailDTO> remoteWorkOrderDetail(@RequestParam("id") Long id,
                                                            @RequestParam("userId") Long userId,
                                                            @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/customerServiceXmList")
    R<PageResult<CustomerServiceXmDTO>> customerServiceXmList(@RequestBody UserCustomerSearchVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/workOrder/remoteConfirmWorkOrder")
    R<Boolean> remoteConfirmWorkOrder(@RequestBody RemoteWorkOrderConfirmVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/customerService/userCustomerById")
    R<CustomerServiceXmDTO> userCustomerById(@RequestBody CommonIdVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/statistic/getDeliverAtfStatistic")
    R<DeliverAtfStatisticDTO> getDeliverAtfStatistic(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/statistic/getDeliverProgressStatistic")
    R<DeliverProgressStatisticDTO> getDeliverProgressStatistic(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/statistic/getAccountingCashierProgressStatistic")
    R<AccountingCashierProgressStatisticDTO> getAccountingCashierProgressStatistic(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/statistic/getAccountingCashierAtfStatistic")
    R<AccountingCashierAtfStatisticDTO> getAccountingCashierAtfStatistic(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/statistic/getAccountingCashierMonthProgressStatistic")
    R<Map<String, AccountingCashierMonthProgressDTO>> getAccountingCashierMonthProgressStatistic(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmCustomer/statistic/getAtfStatistic")
    R<AtfStatisticDTO> getAtfStatistic(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
