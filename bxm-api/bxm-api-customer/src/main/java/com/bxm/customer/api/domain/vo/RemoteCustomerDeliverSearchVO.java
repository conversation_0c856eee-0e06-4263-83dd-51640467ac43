package com.bxm.customer.api.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerDeliverSearchVO {

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("标签名称搜索")
    private String tagName;

    @ApiModelProperty("标签是否包含，1-包含，0-不包含")
    private Integer tagIncludeFlag;

    @ApiModelProperty("账期开始时间，yyyyMM")
    private Integer periodStart;

    @ApiModelProperty("账期结束时间，yyyyMM")
    private Integer periodEnd;

    @ApiModelProperty("提交人")
    private String employeeName;

    @ApiModelProperty("提交时间-开始，yyyy-MM-dd")
    private String submitDateStart;

    @ApiModelProperty("提交时间-结束，yyyy-MM-dd")
    private String submitDateEnd;

    @ApiModelProperty("交付tab类型，2-医社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报")
    private Integer tabType;

    @ApiModelProperty("交付类型搜索，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报")
    private Integer deliverType;

    @ApiModelProperty("是否有人员变更，1-有，0-无")
    private Integer hasPersonChange;

    @ApiModelProperty("状态，0-已提交待申报，1-已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，101-已提交待补充，102-已补充待认证，103-已认证待确认，104-已确认，105-待重提，106-认证异常，107-暂不认证")
    private Integer status;

    @ApiModelProperty("状态列表，多个逗号隔开")
    private String statusList;

    @ApiModelProperty("金额筛选最小值")
    private BigDecimal reportAmountMin;

    @ApiModelProperty("金额筛选最大值")
    private BigDecimal reportAmountMax;

    @ApiModelProperty("导出附件类型，1-创建附件，2-申报附件，3-扣款附件，5-提报附件，6-认证附件，9-检查附件，13-交付附件，多个逗号隔开")
    private String downloadFileTypes;

    @ApiModelProperty("顾问小组id")
    private Long advisorDeptId;

    @ApiModelProperty("会计小组id")
    private Long accountingDeptId;

    @ApiModelProperty("是否交付变更，0-否，1-是")
    private Integer hasChanged;

    @ApiModelProperty("最后操作人")
    private String lastOperName;

    @ApiModelProperty("最后操作时间-开始，yyyy-MM-dd")
    private String lastOperTimeStart;

    @ApiModelProperty("最后操作时间-结束，yyyy-MM-dd")
    private String lastOperTimeEnd;

    @ApiModelProperty("批量查询批次号")
    private String batchNo;

    @ApiModelProperty("是否有事项备忘，0-否，1-是")
    private Integer hasMattersNotes;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("是否有交付要求，0-否，1-是")
    private Integer hasDeliverRequire;

    @ApiModelProperty("服务标签是否包含，0-不包含，1-包含")
    private Integer customerServiceTagIncludeFlag;

    @ApiModelProperty("服务标签关键字")
    private String customerServiceTagName;

    @ApiModelProperty("服务纳税人性质，1-小规模，2-一般纳税人")
    private Integer customerServiceTaxType;

    @ApiModelProperty("服务顾问小组id")
    private Long customerServiceAdvisorDeptId;

    @ApiModelProperty("服务会计小组id")
    private Long customerServiceAccountingDeptId;

    @ApiModelProperty("账期标签是否包含，0-不包含，1-包含")
    private Integer periodTagIncludeFlag;

    @ApiModelProperty("账期标签关键字")
    private String periodTagName;

    @ApiModelProperty("账期纳税人性质，1-小规模，2-一般纳税人")
    private Integer periodTaxType;

    @ApiModelProperty("账期顾问小组id")
    private Long periodAdvisorDeptId;

    @ApiModelProperty("账期会计小组id")
    private Long periodAccountingDeptId;

    @ApiModelProperty("税种")
    private String taxCheckType;

    @ApiModelProperty("ddl开始时间，yyyy-MM-dd")
    private String ddlStart;

    @ApiModelProperty("ddl结束时间，yyyy-MM-dd")
    private String ddlEnd;

    private Long deptId;

    private Long userId;

    private String downloadRecordTitle;

    private Long downloadRecordId;

    private String deptIds;
}
