package com.bxm.thirdpart.api.factory;

import com.alibaba.fastjson2.JSONObject;
import com.bxm.common.core.domain.R;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.bxm.thirdpart.api.domain.*;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@Component
public class RemoteThirdpartFallbackFactory implements FallbackFactory<RemoteThirdpartService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteThirdpartFallbackFactory.class);

    @Override
    public RemoteThirdpartService create(Throwable cause) {
        log.error("远程调用失败:{}", cause.getMessage());
        return new RemoteThirdpartService() {
            @Override
            public R<List<RemoteCompanyInfoDTO>> getCompanyInfoByKeyWord(String keyWord) {
                log.error("远程调用失败:{}", cause.getMessage());
                return R.ok(Lists.newArrayList());
            }

            @Override
            public R<WechatUserInfo> getCompanyWechatUserInfo(String code, String corpId, String corpSecret) {
                log.error("授权登陆获取用户信息失败:{}", cause.getMessage());
                return R.fail("授权登陆获取用户信息失败");
            }

            @Override
            public R<Void> sendCompanyWechatMessage(WechatSendMessageVO vo) {
                log.error("发送推送消息失败:{}", cause.getMessage());
                return R.fail("发送推送消息失败");
            }

            @Override
            public R<String> uploadFileToCompanyWechat(MultipartFile file, String corpId, String corpSecret) {
                log.error("企业微信上传素材失败:{}", cause.getMessage());
                return R.fail("企业微信上传素材失败");
            }

            @Override
            public R<List<Map<String, Object>>> xqyQueryTaxDeclarationData(String taxNumber, String declareMonth) {
                log.error("鑫启易查询申报情况失败:{},taxNumber:{},declareMonth:{}", cause.getMessage(), taxNumber, declareMonth);
                return R.fail("鑫启易查询申报情况失败");
            }

            @Override
            public R<List<Map<String, Object>>> xqySocialSecurityStatementDetailsQuery(String taxNumber, String belongMonth) {
                log.error("鑫启易社保对账单查询失败:{},taxNumber:{},declareMonth:{}", cause.getMessage(), taxNumber, belongMonth);
                return R.fail("鑫启易社保对账单查询失败");
            }

            @Override
            public R<List<Map<String, Object>>> xqyTaxItemConfirmQuery(String taxNumber) {
                log.error("鑫启易税种认定查询失败:{},taxNumber:{}", cause.getMessage(), taxNumber);
                return R.fail("鑫启易税种认定查询失败");
            }

            @Override
            public R<Map<String, Object>> xqyQueryVATData(String taxNumber, String belongMonth, String purchaseInvoiceScope) {
                log.error("鑫启易增值税涉税分析查询失败:{},taxNumber:{},declareMonth:{}, purchaseInvoiceScope:{}", cause.getMessage(), taxNumber, belongMonth, purchaseInvoiceScope);
                return R.fail("鑫启易增值税涉税分析查询失败");
            }

            @Override
            public R<Map<String, Object>> xqyInvoiceStatistic(String taxNumber, String startDate, String endDate) {
                log.error("鑫启易发票统计查询失败:{},taxNumber:{},startDate:{},endDate:{}", cause.getMessage(), taxNumber, startDate, endDate);
                return R.fail("鑫启易发票统计查询失败");
            }

            @Override
            public R<Boolean> xqySetCustomerStatus(XqySetCustomerStatusVO vo, String source) {
                log.error("鑫启易客户代理状态设置失败:{},taxNumber:{},agentStatus:{}", cause.getMessage(), vo.getTaxNumber(), vo.getAgentStatus());
                return R.fail("鑫启易客户代理状态设置失败");
            }

            @Override
            public R<Boolean> xqySetCustomerServiceUser(XqySetCustomerServiceUser vo, String source) {
                log.error("鑫启易客户服务人员设置失败:{},taxNumber:{},jobFunctionName:{},userName:{},operationType:{}", cause.getMessage(), vo.getTaxNumber(), vo.getJobFunctionName(), vo.getUserName(), vo.getOperationType());
                return R.fail("鑫启易客户服务人员设置失败");
            }

            @Override
            public R<Boolean> rpaInAccount(RpaInAccountVO vo, String source) {
                log.error("调用rpa取数失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用rpa取数失败");
            }

            @Override
            public R<Boolean> accountingCashierGetProfit(RpaInAccountVO vo, String source) {
                log.error("调用rpa取数（新）失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用rpa取数（新）失败");
            }

            @Override
            public R<Boolean> accountingCashierAutoSettlement(RpaInAccountVO vo, String source) {
                log.error("调用rpa自动结账失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用rpa自动结账失败");
            }

            @Override
            public R<Boolean> incomeOutput(IncomeOutputVO vo, String source) {
                log.error("调用发票更新完成失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用发票更新完成失败");
            }

            @Override
            public R<BanksEnterprisesExtractDTO> banksEnterprisesExtract(BanksEnterprisesExtractVO vo, String source) {
                log.error("调用银企-提取失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用银企-提取失败");
            }

            @Override
            public R<CheckFilesDTO> checkBankReceiptFile(CheckFilesVO vo, String source) {
                log.error("调用检验文件失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用检验文件失败");
            }

            @Override
            public R<GenerateVoucherDTO> generateVoucher(GenerateVoucherVO vo, String source) {
                log.error("调用凭证生成失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用凭证生成失败");
            }

            @Override
            public R<BankReceiptPaperFileUploadDTO> bankReceiptPaperFileUpload(BankReceiptPaperFileUploadVO vo, String source) {
                log.error("调用纸质回单上传失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用纸质回单上传失败");
            }

            @Override
            public R<String> searchTaskStatus(String uuid, String source) {
                log.error("查询任务状态失败:{},uuid:{}", cause.getMessage(), uuid);
                return R.fail("查询任务状态失败");
            }

            @Override
            public R<Boolean> personTaxReport(PersonTaxRpaVO vo, String source) {
                log.error("调用个税申报失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用个税申报失败");
            }

            @Override
            public R<Boolean> personTaxDeduction(PersonTaxRpaVO vo, String source) {
                log.error("调用个税扣款失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用个税扣款失败");
            }

            @Override
            public R<Boolean> personTaxCheck(PersonTaxRpaVO vo, String source) {
                log.error("调用个税检查失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用个税检查失败");
            }

            @Override
            public R<Boolean> personTaxReportDownload(PersonTaxRpaVO vo, String source) {
                log.error("调用个税申报表下载失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用个税申报表下载失败");
            }

            @Override
            public R<Boolean> personTaxStatusSearch(PersonTaxRpaVO vo, String source) {
                log.error("调用个税状态查询失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("调用个税状态查询失败");
            }

            @Override
            public R<Boolean> rePush(String noticeContent, String source) {
                log.error("重推失败:{},params:{}", cause.getMessage(), noticeContent);
                return R.fail("重推失败");
            }

            @Override
            public R<Boolean> personalIncomeTtaxDownloadCheck(ReportTableDownloadSearchVO vo, String source) {
                log.error("申报表下载查询失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("申报表下载查询失败");
            }

            @Override
            public R<YsbCompanyInfoDTO> getCompanyInfoByTaxNumberYsb(String taxNumber, String source) {
                log.error("查询企业信息失败:{},params:{}", cause.getMessage(), taxNumber);
                return R.fail("查询企业信息失败");
            }

            @Override
            public R<ReportDeductionGetDTO> getReportDeductionList(ReportDeductionGetVO vo, String source) {
                log.error("查询已申报已扣款列表失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("查询已申报已扣款列表失败");
            }

            @Override
            public R<Boolean> qualityChecking(QualityCheckingVO vo, String source) {
                log.error("发起质检失败:{},params:{}", cause.getMessage(), JSONObject.toJSONString(vo));
                return R.fail("发起质检失败");
            }

            @Override
            public R ysbNotice(YsbNoticeVO vo, String source) {
                log.error("通知医社保失败:{},params:{}", cause.getMessage(), vo);
                return R.fail("通知医社保失败");
            }
        };
    }
}
