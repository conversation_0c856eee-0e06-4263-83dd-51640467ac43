package com.bxm.thirdpart.api;

import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.constant.ServiceNameConstants;
import com.bxm.common.core.domain.R;
import com.bxm.thirdpart.api.domain.*;
import com.bxm.thirdpart.api.factory.RemoteThirdpartFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 客户服务
 */
@FeignClient(contextId = "remoteThirdpartService", value = ServiceNameConstants.THIRDPART_SERVICE, fallbackFactory = RemoteThirdpartFallbackFactory.class)
public interface RemoteThirdpartService {

    @GetMapping("/bxmThirdpart/thirdpart/getCompanyInfoByKeyWord")
    R<List<RemoteCompanyInfoDTO>> getCompanyInfoByKeyWord(@RequestParam("keyWord") String keyWord);

    @GetMapping("/bxmThirdpart/thirdpart/getCompanyWechatUserInfo")
    R<WechatUserInfo> getCompanyWechatUserInfo(@RequestParam("code") String code,
                                               @RequestParam("corpId") String corpId,
                                               @RequestParam("corpSecret") String corpSecret);

    @PostMapping("/bxmThirdpart/thirdpart/sendCompanyWechatMessage")
    R sendCompanyWechatMessage(@RequestBody WechatSendMessageVO vo);

    @PostMapping(value = "/bxmThirdpart/thirdpart/uploadFileToCompanyWechat", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    R<String> uploadFileToCompanyWechat(@RequestPart("file") MultipartFile file,
                                                    @RequestParam("corpId") String corpId,
                                                    @RequestParam("corpSecret") String corpSecret);

    @GetMapping("/bxmThirdpart/thirdpart/xqyQueryTaxDeclarationData")
    R<List<Map<String, Object>>> xqyQueryTaxDeclarationData(@RequestParam("taxNumber") String taxNumber, @RequestParam("declareMonth") String declareMonth);

    @GetMapping("/bxmThirdpart/thirdpart/xqySocialSecurityStatementDetailsQuery")
    R<List<Map<String, Object>>> xqySocialSecurityStatementDetailsQuery(@RequestParam("taxNumber") String taxNumber, @RequestParam("belongMonth") String belongMonth);

    @GetMapping("/bxmThirdpart/thirdpart/xqyTaxItemConfirmQuery")
    R<List<Map<String, Object>>> xqyTaxItemConfirmQuery(@RequestParam("taxNumber") String taxNumber);

    @GetMapping("/bxmThirdpart/thirdpart/xqyQueryVATData")
    R<Map<String, Object>> xqyQueryVATData(@RequestParam("taxNumber") String taxNumber, @RequestParam("belongMonth") String belongMonth, @RequestParam(value = "purchaseInvoiceScope", required = false) String purchaseInvoiceScope);

    @GetMapping("/bxmThirdpart/thirdpart/xqyInvoiceStatistic")
    R<Map<String, Object>> xqyInvoiceStatistic(@RequestParam("taxNumber") String taxNumber, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate);

    @PostMapping("/bxmThirdpart/thirdpart/xqySetCustomerStatus")
    R<Boolean> xqySetCustomerStatus(@RequestBody XqySetCustomerStatusVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/xqySetCustomerServiceUser")
    R<Boolean> xqySetCustomerServiceUser(@RequestBody XqySetCustomerServiceUser vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/rpaInAccount")
    R<Boolean> rpaInAccount(@RequestBody RpaInAccountVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/accountingCashierGetProfit")
    R<Boolean> accountingCashierGetProfit(@RequestBody RpaInAccountVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/accountingCashierAutoSettlement")
    R<Boolean> accountingCashierAutoSettlement(@RequestBody RpaInAccountVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/incomeOutput")
    R<Boolean> incomeOutput(@RequestBody IncomeOutputVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/banksEnterprisesExtract")
    R<BanksEnterprisesExtractDTO> banksEnterprisesExtract(@RequestBody BanksEnterprisesExtractVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/checkBankReceiptFile")
    R<CheckFilesDTO> checkBankReceiptFile(@RequestBody CheckFilesVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/generateVoucher")
    R<GenerateVoucherDTO> generateVoucher(@RequestBody GenerateVoucherVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/bankReceiptPaperFileUpload")
    R<BankReceiptPaperFileUploadDTO> bankReceiptPaperFileUpload(@RequestBody BankReceiptPaperFileUploadVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmThirdpart/thirdpart/searchTaskStatus")
    R<String> searchTaskStatus(@RequestParam("uuid") String uuid, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/personTaxReport")
    R<Boolean> personTaxReport(@RequestBody PersonTaxRpaVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/personTaxDeduction")
    R<Boolean> personTaxDeduction(@RequestBody PersonTaxRpaVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/personTaxCheck")
    R<Boolean> personTaxCheck(@RequestBody PersonTaxRpaVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/personTaxReportDownload")
    R<Boolean> personTaxReportDownload(@RequestBody PersonTaxRpaVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/personTaxStatusSearch")
    R<Boolean> personTaxStatusSearch(@RequestBody PersonTaxRpaVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/rePush")
    R<Boolean> rePush(@RequestBody String noticeContent, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/personalIncomeTtaxDownloadCheck")
    R<Boolean> personalIncomeTtaxDownloadCheck(@RequestBody ReportTableDownloadSearchVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmThirdpart/thirdpart/getCompanyInfoByTaxNumberYsb")
    R<YsbCompanyInfoDTO> getCompanyInfoByTaxNumberYsb(@RequestParam("taxNumber") String taxNumber, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/getReportDeductionList")
    R<ReportDeductionGetDTO> getReportDeductionList(@RequestBody ReportDeductionGetVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/qualityChecking")
    R<Boolean> qualityChecking(@RequestBody QualityCheckingVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmThirdpart/thirdpart/ysbNotice")
    R ysbNotice(@RequestBody YsbNoticeVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
