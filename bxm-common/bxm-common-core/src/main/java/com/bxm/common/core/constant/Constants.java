package com.bxm.common.core.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * www主域
     */
    public static final String WWW = "www.";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 成功标记
     */
    public static final Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    public static final Integer FAIL = 500;

    /**
     * 请求超时
     */
    public static final Integer REQUEST_TIMEOUT = 501;

    /**
     * 签名错误
     */
    public static final Integer SIGN_ERROR = 504;

    /**
     * 登录成功状态
     */
    public static final String LOGIN_SUCCESS_STATUS = "0";

    /**
     * 登录失败状态
     */
    public static final String LOGIN_FAIL_STATUS = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 验证码有效期（分钟）
     */
    public static final long CAPTCHA_EXPIRATION = 2;

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = {"com.ruoyi"};

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = {"java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.ruoyi.common.core.utils.file"};

    /**
     * 默认的开始年份
     */
    public static final Integer DEFAULT_START_YEAR = 2024;

    //对应账期下未创建国税交付单，且是1、4、7、10月，且未创建国税交付单，显示为“待创建”
    //最新文总：应该是3、6、9、12月。
    public static final List<Integer> NATIONAL_TAX_NEED_CREATE_MONTH = Arrays.asList(3, 6, 9, 12);

    public static final List<Integer> PERSON_TAX_NEED_CREATE_MONTH = Arrays.asList(3, 6, 9, 12);

    public static final List<Integer> OPERATING_TAX_NEED_CREATE_MONTH = Arrays.asList(3, 6, 9, 12);

    public static final List<Integer> MONTH_PERIOD = Arrays.asList(1, 2, 4, 5, 7, 8, 10, 11);

    public static final List<Integer> MONTH_AND_SEASON_PERIOD = Arrays.asList(3, 9);

    public static final List<Integer> MONTH_AND_SEASON_AND_HALF_YEAR_PERIOD = Arrays.asList(6);

    public static final List<Integer> MONTH_AND_SEASON_AND_HALF_YEAR_AND_YEAR_PERIOD = Arrays.asList(12);

    public static final String PERSON_TAX_CHECK_TYPE = "个人所得税";

    public static final String ZENGZHISHUI = "增值税";

    public static final List<String> PRE_AUTH_OPERATOR = Arrays.asList("新建", "认证");
    public static final List<String> OTHER_OPERATOR = Arrays.asList("申报", "扣款");

    public static final List<String> NATIONAL_TAX_ITEM_CATEGORIES = Arrays.asList("增值税", "文化事业建设费", "消费税", "企业所得税", "城市维护建设税", "教育费附加",
            "地方教育附加", "其他收入", "资源税", "营业税", "印花税", "水利建设专项收入", "环境保护税", "房产税", "城镇土地使用税",
            "车船税", "残疾人就业保障金", "财务报表月季报", "财务报表年报");
    public static final List<String> PERSON_TAX_ITEM_CATEGORIES = Arrays.asList("个人所得税");
    public static final List<String> PERSON_TAX_ITEMS = Arrays.asList("工资薪金所得");
    public static final List<String> OPERATING_TAX_ITEM_CATEGORIES = Arrays.asList("个人所得税");
    public static final List<String> OPERATING_TAX_ITEMS = Arrays.asList("经营所得");
    public static final List<String> SOCIAL_SECURITY_ITEM_CATEGORIES = Arrays.asList("社保");
    public static final List<String> SOCIAL_SECURITY_ITEMS = Arrays.asList("工伤保险", "失业保险", "养老保险", "社保", "滞纳金");
    public static final List<String> MEDICAL_SECURITY_ITEM_CATEGORIES = Arrays.asList("社保");
    public static final List<String> MEDICAL_SECURITY_ITEMS = Arrays.asList("医疗保险", "生育保险");
    public static final List<String> SETTLE_ACCOUNTS = Arrays.asList("企业所得税(年)");
    public static final List<String> SOCIAL_SECURITY_FILE_NAMES = Arrays.asList("社会保险费缴费申报表", "社保申报记录截图");

    public static final String BILL_NO_PREFIX = "B";
    public static final String SETTLEMENT_NO_PREFIX = "S";

    // =====================================CustomerDeliverOperType START ===========================//

    public static final String CUSTOMER_DELIVER_OPER_TYPE_CREATE = "新建提交";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_CREATE_AND_CLOSE = "新建且关闭交付";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_CREATE_SAVE = "新建保存";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_SYSTEM_CREATE = "系统创建";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_EDIT = "编辑交付单并提交";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_EDIT_AND_CLOSE = "编辑交付单且关闭交付";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_EDIT_PRE_AUTH = "编辑预认证交付单";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_EDIT_PRE_AUTH_AND_CLOSE = "编辑预认证交付单且关闭交付";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DELETE = "删除交付单";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_CONFIRM = "确认交付单";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_CONFIRM_AUTH = "确认认证信息";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_SUBMIT = "提交交付单";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_SAVE = "保存交付单";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_SAVE_REPORT = "保存申报信息";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_REPORT = "提交申报";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_AUTO_CONFIRM = "自动确认交付单";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_XQY_UPDATE_REPORT = "鑫启易更新申报结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_XQY_UPDATE_DEDUCTION = "更新扣款结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DEDUCTION = "提交扣款结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DEDUCTION_SAVE = "保存扣款信息";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DEAL_EXCEPTION = "处理异常";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_REPORT_EXCEPTION_DEAL = "解除申报异常";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_REPORT_EXCEPTION_CLOSE = "申报异常关闭交付";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DEDUCTION_EXCEPTION_DEAL = "解除扣款异常";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DEDUCTION_EXCEPTION_CLOSE = "扣款异常关闭交付";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DEAL_AUTH_EXCEPTION = "处理认证异常";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DEAL_AUTH_EXCEPTION_DEAL = "解除认证异常";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DEAL_AUTH_EXCEPTION_CLOSE = "认证异常关闭交付";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_PRE_AUTH_AUTH = "提交认证结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_PRE_AUTH_AUTH_SAVE = "保存认证结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_XQY_PRE_AUTH_AUTH = "鑫启易同步认证结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_PRE_AUTH_CONFIRM = "确认认证信息";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_CORRECTIONS_REPORT = "发起重新申报";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_CORRECTIONS_REPORT_CLOSE = "发起重新申报且关闭交付";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_CORRECTIONS_REPORT_RESULT = "更正申报结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_SUPPLEMENT_REPORT_FILES = "补充申报附件";
    public static final String UPDATE_TAX_REPORT_TOTAL_AMOUNT = "更新个税总额";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_SUPPLEMENT_FILES = "补充附件";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_SUPPLEMENT_AUTH_FILES = "补充认证附件";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_XQY_SUPPLEMENT_REPORT_FILES = "鑫启易补充附件";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_RPA_CHECK = "RPA检查";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_RECEIVE_CHANGED = "接收变更";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_SUBMIT_NEW_DELIVER = "新建已保存提交";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_PRE_AUTH_CONFIRMED = "确认认证结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_PRE_AUTH_REJECT = "驳回认证结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_DELIVER_REJECT = "驳回申报结果";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_PRE_AUTH_RE_AUTH = "重新认证";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_PRE_AUTH_RE_AUTH_CLOSE = "重新认证且关闭交付";
    public static final String CUSTOMER_DELIVER_OPER_TYPE_CHECK = "检查交付";
    public static final String CUSTOMER_DELIVER_OPER_OVER_DELIVER = "完结";
    public static final String CUSTOMER_DELIVER_OPER_DEAL_OVER_EXCEPTION = "处理完结异常";

    // =====================================CustomerDeliverOperType END ===========================//

    // =====================================MaterialDeliverOperType START ===========================//
    public static final String MATERIAL_DELIVER_STOP_ANALYSIS = "中止解析";
    public static final String MATERIAL_DELIVER_DELETE = "删除交接单";
    // =====================================MaterialDeliverOperType END ===========================//

    public static final String XQY_CREATE_BY = "鑫启易";
    public static final String YSB_CREATE_BY = "医社保";
    public static final String RPA_CREATE_BY = "RPA";

    public static final Integer QUALITY_CHECKING_RESULT_TYPE = 1;
    public static final Integer QUALITY_CHECKING_RECORD_TYPE = 2;

    // 市场化改造指定集团id
    public static final List<Long> MARKET_DEPT_ID_LIST = Arrays.asList(59L, 266L);
}
