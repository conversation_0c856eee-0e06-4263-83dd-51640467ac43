package com.bxm.openapi.mapper;

import java.util.List;

import com.bxm.openapi.domain.CityInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.openapi.domain.OpenApiNoticeRecord;

/**
 * 第三方通知/被通知记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-16
 */
@Mapper
public interface OpenApiNoticeRecordMapper extends BaseMapper<OpenApiNoticeRecord>
{
    /**
     * 查询第三方通知/被通知记录
     * 
     * @param id 第三方通知/被通知记录主键
     * @return 第三方通知/被通知记录
     */
    public OpenApiNoticeRecord selectOpenApiNoticeRecordById(Long id);

    /**
     * 查询第三方通知/被通知记录列表
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 第三方通知/被通知记录集合
     */
    public List<OpenApiNoticeRecord> selectOpenApiNoticeRecordList(OpenApiNoticeRecord openApiNoticeRecord);

    /**
     * 新增第三方通知/被通知记录
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 结果
     */
    public int insertOpenApiNoticeRecord(OpenApiNoticeRecord openApiNoticeRecord);

    /**
     * 修改第三方通知/被通知记录
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 结果
     */
    public int updateOpenApiNoticeRecord(OpenApiNoticeRecord openApiNoticeRecord);

    /**
     * 删除第三方通知/被通知记录
     * 
     * @param id 第三方通知/被通知记录主键
     * @return 结果
     */
    public int deleteOpenApiNoticeRecordById(Long id);

    /**
     * 批量删除第三方通知/被通知记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiNoticeRecordByIds(Long[] ids);

    List<CityInfoDTO> selectCityInfo();
}
