package com.bxm.openapi.controller;

import com.alibaba.fastjson.JSONObject;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.domain.PageResult;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.api.RemoteCustomerDeliverService;
import com.bxm.customer.api.RemoteCustomerService;
import com.bxm.customer.api.domain.dto.CustomerServiceXmDTO;
import com.bxm.customer.api.domain.dto.RemoteWorkOrderDTO;
import com.bxm.customer.api.domain.dto.RemoteWorkOrderDetailDTO;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.openapi.domain.CustomerDeliverFileVO;
import com.bxm.openapi.domain.UserIdVO;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.RemoteLogService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.*;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/open/api")
@Api(tags = "开放接口")
@Slf4j
public class OpenApiXmController extends BaseController {

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteLogService remoteLogService;

    @Autowired
    private RemoteCustomerDeliverService remoteCustomerDeliverService;

    @Autowired
    private RedisService redisService;

    @PostMapping("/login")
    @ApiOperation("登录")
    public Result<SysUser> login(@RequestBody Map<String, Object> params,
                                 @RequestHeader(value = "timestamp", required = false) String timestamp,
                                 @RequestHeader(value = "signature", required = false) String signature,
                                 @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteUserService.login(JSONObject.parseObject(JSONObject.toJSONString(params), LoginVO.class), SecurityConstants.INNER));
    }

    @PostMapping("/workOrderDetail")
    @ApiOperation("工单详情")
    public Result<RemoteWorkOrderDetailDTO> workOrderDetail(@RequestBody Map<String, Object> params,
                                                            @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                            @RequestHeader(value = "signature", required = false) String signature,
                                                            @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        RemoteWorkOrderDetailVO vo = JSONObject.parseObject(JSONObject.toJSONString(params), RemoteWorkOrderDetailVO.class);
        return convert(remoteCustomerService.remoteWorkOrderDetail(vo.getId(), vo.getUserId(), SecurityConstants.INNER));
    }

    @PostMapping("/workOrderOperLogList")
    @ApiOperation("工单操作记录")
    public Result<List<BusinessLogDTO>> workOrderOperLogList(@RequestBody Map<String, Object> params,
                                                            @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                            @RequestHeader(value = "signature", required = false) String signature,
                                                            @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        BatchGetBusinessLogVO vo = new BatchGetBusinessLogVO();
        vo.setBusinessIds(Collections.singletonList(Long.parseLong(params.get("id").toString())));
        vo.setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode());
        return convert(remoteLogService.getByBatchBusinessIdAndBusinessType(vo));
    }

    @PostMapping("/workOrderList")
    @ApiOperation("工单列表")
    public Result<List<RemoteWorkOrderDTO>> workOrderList(@RequestBody Map<String, Object> params,
                                                            @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                            @RequestHeader(value = "signature", required = false) String signature,
                                                            @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.remoteWorkOrderListForXm(JSONObject.parseObject(JSONObject.toJSONString(params), RemoteWorkOrderSearchVO.class), SecurityConstants.INNER));
    }

    @PostMapping("/commentWorkOrder")
    @ApiOperation("评论工单")
    public Result<Boolean> commentWorkOrder(@RequestBody Map<String, Object> params,
                                                            @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                            @RequestHeader(value = "signature", required = false) String signature,
                                                            @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.remoteCommentWorkOrder(JSONObject.parseObject(JSONObject.toJSONString(params), RemoteWorkOrderCommentVO.class), SecurityConstants.INNER));
    }

    @PostMapping("/transmitWorkOrder")
    @ApiOperation("转交工单")
    public Result<Boolean> transmitWorkOrder(@RequestBody Map<String, Object> params,
                                            @RequestHeader(value = "timestamp", required = false) String timestamp,
                                            @RequestHeader(value = "signature", required = false) String signature,
                                            @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.remoteTransmit(JSONObject.parseObject(JSONObject.toJSONString(params), RemoteWorkOrderTransmitVO.class), SecurityConstants.INNER));
    }

    @PostMapping("/followUpWorkOrder")
    @ApiOperation("跟进工单")
    public Result<Boolean> followUpWorkOrder(@RequestBody Map<String, Object> params,
                                             @RequestHeader(value = "timestamp", required = false) String timestamp,
                                             @RequestHeader(value = "signature", required = false) String signature,
                                             @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.remoteFollowUp(JSONObject.parseObject(JSONObject.toJSONString(params), RemoteWorkOrderFollowUpVO.class), SecurityConstants.INNER));
    }

    @PostMapping("/confirmWorkOrder")
    @ApiOperation("确认工单")
    public Result<Boolean> confirmWorkOrder(@RequestBody Map<String, Object> params,
                                             @RequestHeader(value = "timestamp", required = false) String timestamp,
                                             @RequestHeader(value = "signature", required = false) String signature,
                                             @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.remoteConfirmWorkOrder(JSONObject.parseObject(JSONObject.toJSONString(params), RemoteWorkOrderConfirmVO.class), SecurityConstants.INNER));
    }

    @PostMapping("/userCustomerList")
    @ApiOperation("获取客户列表")
    public Result<PageResult<CustomerServiceXmDTO>> userCustomerList(@RequestBody Map<String, Object> params,
                                                                      @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                      @RequestHeader(value = "signature", required = false) String signature,
                                                                      @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.customerServiceXmList(JSONObject.parseObject(JSONObject.toJSONString(params), UserCustomerSearchVO.class), SecurityConstants.INNER));
    }

    @PostMapping("/userCustomerById")
    @ApiOperation("获取客户信息")
    public Result<CustomerServiceXmDTO> userCustomerById(@RequestBody Map<String, Object> params,
                                                                     @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                     @RequestHeader(value = "signature", required = false) String signature,
                                                                     @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.userCustomerById(JSONObject.parseObject(JSONObject.toJSONString(params), CommonIdVO.class), SecurityConstants.INNER));
    }

    @PostMapping("/deptEmployeeTreeSelect")
    @ApiOperation("获取组织架构选择")
    public Result<List<RemoteTreeSelect>> deptEmployeeTreeSelect(@RequestBody Map<String, Object> params,
                                                                 @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                 @RequestHeader(value = "signature", required = false) String signature,
                                                                 @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        UserIdVO vo = JSONObject.parseObject(JSONObject.toJSONString(params), UserIdVO.class);
        List<SysEmployee> employeeList = remoteEmployeeService.getBatchByUserIds(Collections.singletonList(vo.getUserId())).getDataThrowException(false);
        if (ObjectUtils.isEmpty(employeeList)) {
            return Result.ok(Lists.newArrayList());
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(employeeList.get(0).getDeptId()).getDataThrowException(false);
        if (Objects.isNull(sysDept) || !"0".equals(sysDept.getDelFlag())) {
            return Result.ok(Lists.newArrayList());
        }
        return convert(remoteDeptService.remoteCommonDeptEmployeeTreeSelect(Long.parseLong(sysDept.getAncestors().split(",")[2]), sysDept.getDeptType(), 4, 1, 4, null,
                Long.parseLong(sysDept.getAncestors().split(",")[1]), null, SecurityConstants.INNER));
    }

    @GetMapping("/setNoticeUrl")
    public Result setNoticeUrl(@RequestParam("url") String url) {
        redisService.setCacheObject(CacheConstants.NOTICE_URL, url);
        return Result.ok();
    }

    @PostMapping("/getWeekDays")
    @ApiOperation("获取当月和本月的所有休息日")
    public Result<List<String>> getWeekDays(@RequestBody Map<String, Object> params,
                                                                 @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                 @RequestHeader(value = "signature", required = false) String signature,
                                                                 @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteDeptService.getWeekDays(SecurityConstants.INNER));
    }

    @PostMapping("/getDeliverFiles")
    @ApiOperation("获取个税交付单附件")
    public Result<List<CommonFileVO>> getDeliverFiles(@RequestBody Map<String, Object> params,
                                                  @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                  @RequestHeader(value = "signature", required = false) String signature,
                                                  @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        CustomerDeliverFileVO vo = JSONObject.parseObject(JSONObject.toJSONString(params), CustomerDeliverFileVO.class);
        return convert(remoteCustomerDeliverService.getPersonTaxDeliverFiles(vo.getTaxNumber(), vo.getPeriod(), vo.getDeptId(), SecurityConstants.INNER));
    }
}
