package com.bxm.openapi.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.OpenApiAppRelations;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.api.RemoteCustomerService;
import com.bxm.customer.api.domain.dto.*;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.openapi.domain.CityInfoDTO;
import com.bxm.openapi.domain.ExportDTO;
import com.bxm.openapi.domain.OpenApiNoticeRecord;
import com.bxm.openapi.domain.SysClientVersion;
import com.bxm.openapi.mapper.OpenApiNoticeRecordMapper;
import com.bxm.openapi.service.ISysClientVersionService;
import com.bxm.openapi.service.OpenApiService;
import com.bxm.openapi.service.QyWxService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.RemoteUserMessagePageResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@RequestMapping("/openapi")
@RestController
@Slf4j
public class OpenApiController extends BaseController{

    @Autowired
    private OpenApiService openApiService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private ISysClientVersionService clientVersionService;

    @Autowired
    private QyWxService qyWxService;

    @Autowired
    private OpenApiNoticeRecordMapper openApiNoticeRecordMapper;

    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @GetMapping("/reportTest")
    public Result reportTest(String taxNumberList,
                             String reportPeriod,
                             String operateName,
                             String batchNo,
                             Integer operateType) {
        Map<String, Object> params = new HashMap<>();
        params.put("taxNumberList", Arrays.asList(taxNumberList.split(",")));
        params.put("reportPeriod", reportPeriod);
        params.put("operateName", operateName);
        params.put("batchNo", batchNo);
        params.put("operateType", operateType);

        openApiService.report(params, OpenApiAppRelations.XQY.getAppId());
        return Result.ok();
    }

    @GetMapping("/supplementTest")
    public Result supplementTest(String taxNumber,
                                 String customerName,
                                 String reportPeriod,
                                 String operateName,
                                 String batchNo,
                                 String officalFilename,
                                 String taxPeriodStart,
                                 String taxPeriodEnd,
                                 String fileId,
                                 String fileName) {
        Map<String, Object> params = new HashMap<>();
        params.put("taxNumber", taxNumber);
        params.put("customerName", customerName);
        params.put("reportPeriod", reportPeriod);
        params.put("operateName", operateName);
        params.put("batchNo", batchNo);
        params.put("officalFilename", officalFilename);
        params.put("taxPeriodStart", taxPeriodStart);
        params.put("taxPeriodEnd", taxPeriodEnd);
        params.put("fileId", fileId);
        params.put("fileName", fileName);
        openApiService.supplementFiles(params, OpenApiAppRelations.XQY.getAppId());
        return Result.ok();
    }

    @PostMapping("/qualityTest")
    public Result qualityTest(@RequestBody Map<String, Object> params) {
        Object uuidObj = params.get("uuid");
        if (Objects.isNull(uuidObj)) {
            return Result.fail("uuid不能为空");
        }
        Object noticeParameterObj = params.get("noticeParameter");
        if (Objects.isNull(noticeParameterObj)) {
            return Result.fail("noticeParameter不能为空");
        }
        String uuid = String.valueOf(uuidObj);
        OpenApiNoticeRecord openApiNoticeRecord = openApiNoticeRecordMapper.selectOne(new LambdaQueryWrapper<OpenApiNoticeRecord>()
                .eq(OpenApiNoticeRecord::getUuid, uuid));
        if (Objects.isNull(openApiNoticeRecord)) {
            return Result.ok("未查询到uuid对应的记录");
        }
        Map<String, Object> noticeContent = JSONObject.parseObject(openApiNoticeRecord.getNoticeContent());
        String noticeJson = (String) noticeContent.get("noticeJson");
        Map<String, Object> map = JSONObject.parseObject(noticeJson);
        Map<String, Object> noticeParameter = (Map<String, Object>) noticeParameterObj;
        map.put("noticeParameter", noticeParameter);
        openApiService.commonNotice(map, OpenApiAppRelations.COMMON_2.getAppId());
        return Result.ok();
    }

    @PostMapping("/report")
    public Result report(@RequestBody Map<String, Object> params,
                         @RequestHeader(value = "timestamp", required = false) String timestamp,
                         @RequestHeader(value = "signature", required = false) String signature,
                         @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        log.info("收到鑫启易申报上报请求");
        openApiService.report(params, appid);
        return Result.ok();
    }

    @PostMapping("/commonReport")
    public Result commonReport(@RequestBody Map<String, Object> params,
                         @RequestHeader(value = "timestamp", required = false) String timestamp,
                         @RequestHeader(value = "signature", required = false) String signature,
                         @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        log.info("收到公共申报上报请求,params:{}", params);
        openApiService.commonReport(params, appid);
        return Result.ok();
    }

    @PostMapping("/supplementFiles")
    public Result supplementFiles(@RequestBody Map<String, Object> params,
                         @RequestHeader(value = "timestamp", required = false) String timestamp,
                         @RequestHeader(value = "signature", required = false) String signature,
                         @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        log.info("收到附件补充请求");
        openApiService.supplementFiles(params, appid);
        return Result.ok();
    }

    @PostMapping("/commonSupplementFiles")
    public Result commonSupplementFiles(@RequestBody Map<String, Object> params,
                                  @RequestHeader(value = "timestamp", required = false) String timestamp,
                                  @RequestHeader(value = "signature", required = false) String signature,
                                  @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        log.info("收到附件补充请求");
        openApiService.commonSupplementFiles(params, appid);
        return Result.ok();
    }

    @PostMapping("/commonDeduction")
    public Result commonDeduction(@RequestBody Map<String, Object> params,
                                        @RequestHeader(value = "timestamp", required = false) String timestamp,
                                        @RequestHeader(value = "signature", required = false) String signature,
                                        @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        log.info("收到扣款请求");
        openApiService.commonDeduction(params, appid);
        return Result.ok();
    }

    @PostMapping("/commonNotice")
    public Result commonNotice(@RequestBody Map<String, Object> params,
                                  @RequestHeader(value = "timestamp", required = false) String timestamp,
                                  @RequestHeader(value = "signature", required = false) String signature,
                                  @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        log.info("收到通知请求:{}", params);
        openApiService.commonNotice(params, appid);
        return Result.ok();
    }

    @PostMapping("/uploadFile")
    public Result<Map<String, String>> uploadFile(MultipartFile file,
                                               @RequestHeader(value = "timestamp", required = false) String timestamp,
                                               @RequestHeader(value = "signature", required = false) String signature,
                                               @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, null);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        RemoteAliFileDTO fileDTO = remoteFileService.uploadFile(file).getDataThrowException();
        Map<String, String> result = new HashMap<>();
        result.put("fileId", fileDTO.getUrl());
        return Result.ok(result);
    }

//    @RequestMapping(value = "/companyWechatReceiveMessage", method = {RequestMethod.GET, RequestMethod.POST})
    public Object companyWechatReceiveMessage(HttpServletRequest request, @RequestBody(required = false) String body) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        String jsonString = JSONObject.toJSONString(parameterMap);
        log.info("企业微信回调参数：{},  解析参数：{}", jsonString, body);

        if (body == null) {
            Object result = qyWxService.verificationUrl(request);
            return result;
        }
        Map<String, String> resultMap = qyWxService.getRequestParameter(request, body);
        System.err.println(resultMap);

        String infoType = resultMap.get("InfoType");
        if (Objects.equals("suite_ticket", infoType)) {
            String suiteTicket = resultMap.get("SuiteTicket");
            log.info("企业微信回调参数：{}", suiteTicket);
            qyWxService.saveSuiteTicket(suiteTicket);
        } else if (Objects.equals("reset_permanent_code", infoType)) {
            String authCode = resultMap.get("AuthCode");
            log.info("企业微信回调参数：{}", authCode);
            qyWxService.savePermanentCode(authCode);
        }
        return "success";
    }

    @GetMapping("/getNotReadMessageCount")
    public Result<Map<String, Integer>> getNotReadMessageCountByUserId(@RequestParam("userId") Long userId) {
        return Result.ok(remoteUserService.getNotReadMessageCountByUserId(userId, SecurityConstants.INNER).getDataThrowException());
    }

    @GetMapping("/getUserMessageList")
    public Result<RemoteUserMessagePageResult> getUserMessageListByUserId(@RequestParam("pageNum") Integer pageNum,
                                                                          @RequestParam("pageSize") Integer pageSize,
                                                                          @RequestParam("userId") Long userId) {
        return Result.ok(remoteUserService.getUserMessageListByUserId(pageNum, pageSize, userId, SecurityConstants.INNER).getDataThrowException());
    }

    @PostMapping("/readMessage")
    public Result readMessageInner(@RequestBody CommonIdVO vo) {
        remoteUserService.readMessageInner(vo, SecurityConstants.INNER).getDataThrowException();
        return Result.ok();
    }

    @PostMapping("/allRead")
    public Result allReadInner(@RequestBody Map<String, Object> params) {
        remoteUserService.allReadInner(params, SecurityConstants.INNER).getDataThrowException();
        return Result.ok();
    }

    @GetMapping("/getNewestClientVersion")
    public Result<SysClientVersion> getNewestClientVersion(@RequestParam("clientType") @ApiParam("第三方客户端类型") Integer clientType) {
        return Result.ok(clientVersionService.getNewestClientVersion(clientType));
    }

    @PostMapping("/xqyNotice")
    public Map<String, Object> xqyNotice(@RequestBody Map<String, Object> params,
                            @RequestHeader(value = "timestamp", required = false) String timestamp,
                            @RequestHeader(value = "signature", required = false) String signature,
                            @RequestHeader(value = "appid", required = false) String appid) {
        Map<String, Object> result = new HashMap<>();
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                result.put("Success", false);
                result.put("Msg", checkSignResult.getMsg());
                result.put("Value", null);
                return result;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            result.put("Success", false);
            result.put("Msg", "签名验证失败，请稍后重试");
            result.put("Value", null);
            return result;
        }
        log.info("收到鑫启易上报请求:{}", params);
        return openApiService.xqyNotice(params, appid);
    }

    @GetMapping("/xqyNoticeTest")
    public Result xqyNoticeTest(String noticeCode,
                                 String taxNumber,
                                 String endMonth,
                                String operator) {
        Map<String, Object> params = new HashMap<>();
        params.put("noticeCode", noticeCode);
        Map<String, Object> param = new HashMap<>();
        param.put("taxNumber", taxNumber);
        param.put("operator", operator);
        param.put("endMonth", endMonth);
        params.put("noticeParameter", param);
        openApiService.xqyNotice(params, OpenApiAppRelations.XQY_SELF.getAppId());
        return Result.ok();
    }

    @GetMapping("/exportAccountingNotComplete")
    public void exportAccountingNotComplete(HttpServletResponse response, Integer type) {
        ExcelUtil<ExportDTO> util = new ExcelUtil<>(ExportDTO.class);
        String sheetName;
        if (type == 1) {
            sheetName = "账务未完数-按甲方-总";
        } else if (type == 2) {
            sheetName = "账务未完数-按甲方-0申报";
        } else if (type == 3) {
            sheetName = "账务未完数-按甲方-小规模";
        } else {
            sheetName = "账务未完数-按甲方-一般纳税人";
        }
        util.exportExcel(response, openApiService.exportAccountingNotComplete(type), sheetName);
    }

    @PostMapping("/statistic/getDeliverAtfStatistic")
    @ApiOperation("税务atf统计数据")
    public Result<DeliverAtfStatisticDTO> getDeliverAtfStatistic(@RequestBody Map<String, Object> params,
                                                          @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                          @RequestHeader(value = "signature", required = false) String signature,
                                                          @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.getDeliverAtfStatistic(SecurityConstants.INNER));
    }

    @PostMapping("/statistic/getDeliverProgressStatistic")
    @ApiOperation("税务进度统计数据")
    public Result<DeliverProgressStatisticDTO> getDeliverProgressStatistic(@RequestBody Map<String, Object> params,
                                                                           @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                           @RequestHeader(value = "signature", required = false) String signature,
                                                                           @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.getDeliverProgressStatistic(SecurityConstants.INNER));
    }

    @PostMapping("/statistic/getAccountingCashierProgressStatistic")
    @ApiOperation("账务进度统计数据")
    public Result<AccountingCashierProgressStatisticDTO> getAccountingCashierProgressStatistic(@RequestBody Map<String, Object> params,
                                                                                               @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                                               @RequestHeader(value = "signature", required = false) String signature,
                                                                                               @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.getAccountingCashierProgressStatistic(SecurityConstants.INNER));
    }

    @PostMapping("/statistic/getAccountingCashierAtfStatistic")
    @ApiOperation("账务atf统计数据")
    public Result<AccountingCashierAtfStatisticDTO> getAccountingCashierAtfStatistic(@RequestBody Map<String, Object> params,
                                                                                     @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                                     @RequestHeader(value = "signature", required = false) String signature,
                                                                                     @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.getAccountingCashierAtfStatistic(SecurityConstants.INNER));
    }

    @PostMapping("/statistic/getAccountingCashierMonthProgressStatistic")
    @ApiOperation("账务年进度统计数据")
    public Result<Map<String, AccountingCashierMonthProgressDTO>> getAccountingCashierMonthProgressStatistic(@RequestBody Map<String, Object> params,
                                                                 @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                 @RequestHeader(value = "signature", required = false) String signature,
                                                                 @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.getAccountingCashierMonthProgressStatistic(SecurityConstants.INNER));
    }

    @PostMapping("/statistic/getAtfStatistic")
    @ApiOperation("atf处理量统计数据")
    public Result<AtfStatisticDTO> getAtfStatistic(@RequestBody Map<String, Object> params,
                                                                 @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                                 @RequestHeader(value = "signature", required = false) String signature,
                                                                 @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        return convert(remoteCustomerService.getAtfStatistic(SecurityConstants.INNER));
    }

    @PostMapping("/statistic/cityInfo")
    @ApiOperation("获取城市信息")
    public Result<Map<String, Integer>> getCityInfo(@RequestBody Map<String, Object> params,
                                                   @RequestHeader(value = "timestamp", required = false) String timestamp,
                                                   @RequestHeader(value = "signature", required = false) String signature,
                                                   @RequestHeader(value = "appid", required = false) String appid) {
        try {
            Result checkSignResult = checkSign(timestamp, signature, appid, params);
            if (!Objects.isNull(checkSignResult)) {
                return checkSignResult;
            }
        } catch (Exception e) {
            log.error("签名验证失败:{}", e.getMessage());
            return Result.fail("签名验证失败，请稍后重试");
        }
        List<CityInfoDTO> cityInfoList = openApiNoticeRecordMapper.selectCityInfo();
        return Result.ok(cityInfoList.stream().collect(Collectors.toMap(CityInfoDTO::getCityName, CityInfoDTO::getCount)));
    }
}
