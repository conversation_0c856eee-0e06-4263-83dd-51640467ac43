package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 业务任务对象 c_business_task
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Data
@ApiModel("业务任务对象")
@Accessors(chain = true)
@TableName("c_business_task")
public class BusinessTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    /** 任务类型，1-账期任务 */
    @Excel(name = "任务类型，1-账期任务")
    @TableField("type")
    @ApiModelProperty(value = "任务类型，1-账期任务")
    private Integer type;

    /** 任务对应的业务的业务ID，账期任务就是账期ID */
    @Excel(name = "任务对应的业务的业务ID，账期任务就是账期ID")
    @TableField("biz_id")
    @ApiModelProperty(value = "任务对应的业务的业务ID，账期任务就是账期ID")
    private Long bizId;

    /** 事项，1-银行流水 */
    @Excel(name = "事项，1-银行流水")
    @TableField("item_type")
    @ApiModelProperty(value = "事项，1-银行流水")
    private Integer itemType;

    /** 任务状态 */
    @Excel(name = "任务状态")
    @TableField("status")
    @ApiModelProperty(value = "任务状态")
    private Integer status;

    /** 标题 */
    @Excel(name = "标题")
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    @Excel(name = "银行名称")
    @TableField("bank_name")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /** 银行账号 */
    @Excel(name = "银行账号")
    @TableField("bank_account_number")
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNumber;

    @Excel(name = "是否银行流水，0-否，1-是")
    @TableField("has_bank_payment")
    @ApiModelProperty(value = "是否银行流水，0-否，1-是")
    private Boolean hasBankPayment;

    @Excel(name = "银行流水交付单id")
    @TableField("bank_accounting_cashier_id")
    @ApiModelProperty(value = "银行流水交付单id")
    private Long bankAccountingCashierId;

    /** 截止日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("deadline")
    @ApiModelProperty(value = "截止日期")
    private LocalDate deadline;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 监管人 */
    @Excel(name = "监管人")
    @TableField("admin_user_id")
    @ApiModelProperty(value = "监管人")
    private Long adminUserId;

    /** 监管人名称 */
    @Excel(name = "监管人名称")
    @TableField("admin_user_name")
    @ApiModelProperty(value = "监管人名称")
    private String adminUserName;

    @Excel(name = "监管组")
    @TableField("admin_dept_id")
    @ApiModelProperty(value = "监管组")
    private Long adminDeptId;

    /** 是否分派，0-否，1-是 */
    @Excel(name = "是否分派，0-否，1-是")
    @TableField("is_assign")
    @ApiModelProperty(value = "是否分派，0-否，1-是")
    private Boolean isAssign;

    /** 分派时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分派时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("assign_time")
    @ApiModelProperty(value = "分派时间")
    private LocalDateTime assignTime;

    /** 执行人 */
    @Excel(name = "执行人")
    @TableField("execute_user_id")
    @ApiModelProperty(value = "执行人")
    private Long executeUserId;

    /** 执行人名称 */
    @Excel(name = "执行人名称")
    @TableField("execute_user_name")
    @ApiModelProperty(value = "执行人名称")
    private String executeUserName;

    /** 执行时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "执行时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("execute_time")
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime executeTime;

    /** 完成人 */
    @Excel(name = "完成人")
    @TableField("finish_user_id")
    @ApiModelProperty(value = "完成人")
    private Long finishUserId;

    /** 完成时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("finish_time")
    @ApiModelProperty(value = "完成时间")
    private LocalDateTime finishTime;

    /** 完成结果 */
    @Excel(name = "完成结果")
    @TableField("finish_result")
    @ApiModelProperty(value = "完成结果")
    private Integer finishResult;

    /** 完结时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完结时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("end_time")
    @ApiModelProperty(value = "完结时间")
    private LocalDateTime endTime;

    /** 最后操作人 */
    @Excel(name = "最后操作人")
    @TableField("last_operate_user_id")
    @ApiModelProperty(value = "最后操作人")
    private Long lastOperateUserId;

    /** 最后操作人名称 */
    @Excel(name = "最后操作人名称")
    @TableField("last_operate_user_name")
    @ApiModelProperty(value = "最后操作人名称")
    private String lastOperateUserName;

    /** 最后操作类型 */
    @Excel(name = "最后操作类型")
    @TableField("last_operate_type")
    @ApiModelProperty(value = "最后操作类型")
    private Integer lastOperateType;

    /** 最后操作时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_operate_time")
    @ApiModelProperty(value = "最后操作时间")
    private LocalDateTime lastOperateTime;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_company_name")
    @ApiModelProperty(value = "客户名")
    private String customerCompanyName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 纳税人性质，1-小规模，2-一般纳税人 */
    @Excel(name = "纳税人性质，1-小规模，2-一般纳税人")
    @TableField("tax_type")
    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 账期 顾问部门id */
    @Excel(name = "账期 顾问部门id")
    @TableField("period_advisor_dept_id")
    @ApiModelProperty(value = "账期 顾问部门id")
    private Long periodAdvisorDeptId;

    /** 账期 会计部门id */
    @Excel(name = "账期 会计部门id")
    @TableField("period_accounting_dept_id")
    @ApiModelProperty(value = "账期 会计部门id")
    private Long periodAccountingDeptId;

    @ApiModelProperty("介质-纸质，0-无，1-有")
    @TableField("medium_paper")
    private Integer mediumPaper;

    @ApiModelProperty("介质-电子，0-无，1-有")
    @TableField("medium_electric")
    private Integer mediumElectric;

    @ApiModelProperty("介质-银行，0-无，1-有")
    @TableField("medium_bank")
    private Integer mediumBank;
    @TableField("first_complete_time")
    @ApiModelProperty(value = "首次完成时间")
    private LocalDateTime firstCompleteTime;

    @TableField("reject_times")
    @ApiModelProperty(value = "驳回次数")
    private Integer rejectTimes;

    @TableField("close_time")
    @ApiModelProperty(value = "完结时间")
    private LocalDateTime closeTime;

    @TableField("create_remark")
    @ApiModelProperty(value = "创建备注")
    private String createRemark;

    @TableField("modify_remark")
    @ApiModelProperty(value = "修改备注")
    private String modifyRemark;

    @TableField("finish_remark")
    @ApiModelProperty(value = "完成备注")
    private String finishRemark;

    @TableField("check_remark")
    @ApiModelProperty(value = "审核备注")
    private String checkRemark;

    @TableField("deal_exception_remark")
    @ApiModelProperty(value = "处理异常备注")
    private String dealExceptionRemark;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    @TableField("material_integrity")
    private Integer materialIntegrity;

    @ApiModelProperty("变更为待入账处理的时间")
    @TableField("wait_in_account_time")
    private LocalDateTime waitInAccountTime;

    /** 对账单余额 */
    @Excel(name = "对账单余额")
    @TableField("statement_balance")
    @ApiModelProperty(value = "对账单余额")
    private BigDecimal statementBalance;
}
