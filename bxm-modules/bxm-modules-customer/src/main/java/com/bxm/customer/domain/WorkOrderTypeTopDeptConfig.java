package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工单类型集团配置表 c_work_order_type_top_dept_config
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@ApiModel("工单类型集团配置表")
@Accessors(chain = true)
@TableName("c_work_order_type_top_dept_config")
public class WorkOrderTypeTopDeptConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 工单类型id */
    @Excel(name = "工单类型id")
    @TableField("work_order_type_id")
    @ApiModelProperty(value = "工单类型id")
    private Long workOrderTypeId;

    /** 工单类型 */
    @Excel(name = "工单类型")
    @TableField("work_order_type")
    @ApiModelProperty(value = "工单类型")
    private Integer workOrderType;

    /** 可见的组织类型，1-业务，2-工厂 */
    @Excel(name = "可见的集团id")
    @TableField("dept_id")
    @ApiModelProperty(value = "可见的集团id")
    private Long deptId;

    /** 控制类型，1-创建，2-筛选 **/
    @Excel(name = "控制类型，1-创建，2-筛选")
    @TableField("show_type")
    @ApiModelProperty(value = "控制类型，1-创建，2-筛选")
    private Integer showType;
}
