package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.YesNo;
import com.bxm.common.core.enums.docHandover.*;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.AccountingInfoSourceDTO;
import com.bxm.customer.domain.dto.AccountingTopInfoSourceDTO;
import com.bxm.customer.domain.dto.AdvisorInfoSourceDTO;
import com.bxm.customer.domain.dto.docHandover.*;
import com.bxm.customer.domain.dto.workBench.DocHandoverWorkBenchDTO;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.docHandover.*;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CustomerServiceDocHandoverMapper;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 材料、资料交接Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Slf4j
@Service
public class CustomerServiceDocHandoverServiceImpl extends ServiceImpl<CustomerServiceDocHandoverMapper, CustomerServiceDocHandover> implements ICustomerServiceDocHandoverService {
    @Autowired
    private CustomerServiceDocHandoverMapper customerServiceDocHandoverMapper;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private ICBusinessTagRelationService icBusinessTagRelationService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private CCustomerServiceMapper cCustomerServiceMapper;

    @Autowired
    private ICustomerServiceDocHandoverFileService iCustomerServiceDocHandoverFileService;

    @Autowired
    private ICustomerServiceBankAccountService iCustomerServiceBankAccountService;

    @Autowired
    private ICustomerServiceDocHandoverBankInstrumentService iCustomerServiceDocHandoverBankInstrumentService;

    @Autowired
    private ICustomerServiceDocHandoverTaxInstrumentService iCustomerServiceDocHandoverTaxInstrumentService;

    /**
     * 查询材料、资料交接
     *
     * @param id 材料、资料交接主键
     * @return 材料、资料交接
     */
    @Override
    public CustomerServiceDocHandover selectCustomerServiceDocHandoverById(Long id) {
        return customerServiceDocHandoverMapper.selectCustomerServiceDocHandoverById(id);
    }

    /**
     * 查询材料、资料交接列表
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 材料、资料交接
     */
    @Override
    public List<CustomerServiceDocHandover> selectCustomerServiceDocHandoverList(CustomerServiceDocHandover customerServiceDocHandover) {
        return customerServiceDocHandoverMapper.selectCustomerServiceDocHandoverList(customerServiceDocHandover);
    }

    /**
     * 新增材料、资料交接
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 结果
     */
    @Override
    public int insertCustomerServiceDocHandover(CustomerServiceDocHandover customerServiceDocHandover) {
        customerServiceDocHandover.setCreateTime(DateUtils.getNowDate());
        return customerServiceDocHandoverMapper.insertCustomerServiceDocHandover(customerServiceDocHandover);
    }

    /**
     * 修改材料、资料交接
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 结果
     */
    @Override
    public int updateCustomerServiceDocHandover(CustomerServiceDocHandover customerServiceDocHandover) {
        customerServiceDocHandover.setUpdateTime(DateUtils.getNowDate());
        return customerServiceDocHandoverMapper.updateCustomerServiceDocHandover(customerServiceDocHandover);
    }

    /**
     * 批量删除材料、资料交接
     *
     * @param ids 需要删除的材料、资料交接主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceDocHandoverByIds(Long[] ids) {
        return customerServiceDocHandoverMapper.deleteCustomerServiceDocHandoverByIds(ids);
    }

    /**
     * 删除材料、资料交接信息
     *
     * @param id 材料、资料交接主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceDocHandoverById(Long id) {
        return customerServiceDocHandoverMapper.deleteCustomerServiceDocHandoverById(id);
    }

    @Override
    public IPage<DocHandoverDTO> docHandoverList(Long deptId, CustomerServiceDocHandoverVO vo) {
        IPage<DocHandoverDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());

        //参考了 CCustomerServiceServiceImpl customerServicePeriodMonthList
        //参考了 CustomerServiceInAccountServiceImpl inAccountList
        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        log.info("inAccountList userDept={}", new Gson().toJson(userDept));
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }

        //处理，标签搜索
        TagSearchVO tagSearchVO = tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
            return result;
        }

        //处理，会计搜索
        CommonIdsSearchVO accountingSearchVO = accountingSearch(vo.getAccountingEmployee());
        if (accountingSearchVO.getNeedSearch() && accountingSearchVO.getFail()) {
            return result;
        }

        //处理，顾问搜索
        CommonIdsSearchVO advisorSearchVO = advisorSearch(vo.getAdvisorEmployee());
        if (advisorSearchVO.getNeedSearch() && advisorSearchVO.getFail()) {
            return result;
        }

        CommonIdsSearchVO deptSearchVO = deptSearch(vo.getQueryDeptId());
        if (deptSearchVO.getNeedSearch() && deptSearchVO.getFail()) {
            return result;
        }

        //合并
        CommonIdsSearchVO commonIdsSearchVO = CustomerServiceInAccountServiceImpl.mergeCommonIdsSearchData(Lists.newArrayList(accountingSearchVO, advisorSearchVO, deptSearchVO));
        if (commonIdsSearchVO.getNeedSearch() && commonIdsSearchVO.getFail()) {
            return result;
        }

        if (!StringUtils.isEmpty(vo.getSubmitTimeEnd())) {
            vo.setSubmitTimeEnd(vo.getSubmitTimeEnd() + " 23:59:59");
        }

        //原始数据
        //List<DocHandoverDTO> source = customerServiceDocHandoverMapper.selectDocHandoverList(result, vo, tagSearchVO, commonIdsSearchVO);
        List<DocHandoverDTO> source = customerServiceDocHandoverMapper.selectDocHandoverListV2(result, vo, tagSearchVO, commonIdsSearchVO, userDept);

        //处理数据
        if (!ObjectUtils.isEmpty(source)) {
            Map<Long, List<AdvisorInfoSourceDTO>> advisorInfoSourceMap = getAdvisorInfoSource(
                    source.stream().map(DocHandoverDTO::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList())
            );

            //批量获取会计
            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = getAccountingInfoSource(
                    source.stream().map(DocHandoverDTO::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList())
            );

            source.forEach(row -> {
                String submitEmployeeNameFull = null;
                if (!StringUtils.isEmpty(row.getSubmitEmployeeDeptName()) || !StringUtils.isEmpty(row.getSubmitEmployeeName())) {
                    submitEmployeeNameFull = (StringUtils.isEmpty(row.getSubmitEmployeeDeptName()) ? "" : row.getSubmitEmployeeDeptName()) + "（" + row.getSubmitEmployeeName() + "）";
                }

                row.setPeriodStr(new StringBuilder(row.getPeriod().toString()).insert(4, "-").toString());
                row.setStatusStr(DocHandoverStatus.getByCode(row.getStatus()).getName());
                row.setIsVoucherEntryStr(YesNo.getByCode(row.getIsVoucherEntry()).getName());
                row.setAdvisorEmployeeNameFull(handleAdvisorEmployeeNameFull(advisorInfoSourceMap.get(row.getCustomerServicePeriodMonthId())));
                row.setAccountingEmployeeNameFull(handleAccountingEmployeeNameFull(accountingInfoSourceMap.get(row.getCustomerServicePeriodMonthId())));
                row.setSubmitEmployeeNameFull(submitEmployeeNameFull);
                row.setVerificationEmployeeTypeStr(Objects.equals(row.getVerificationEmployeeType(), 2) ? "区域" : "总部");//总部、区域
                row.setWholeLevelStr(row.getWholeLevel() == null ? null : WholeLevel.getByCode(row.getWholeLevel()).getName());
            });
        }

        //返回数据
        result.setRecords(source);

        return result;
    }

    @Override
    public Integer getVoucherEntry(Long customerServicePeriodMonthId) {
        //获取材料交接单的标签中是否有 凭票入账 的标签
        List<CBusinessTagRelation> cBusinessTagRelations = icBusinessTagRelationService.selectByBusinessIdAndBusinessType(customerServicePeriodMonthId, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD.getCode());
        boolean isVoucherEntrySource = cBusinessTagRelations.stream().map(CBusinessTagRelation::getTagId).collect(Collectors.toList()).contains(6L);

        return isVoucherEntrySource ? YesNo.YES.getCode() : YesNo.NO.getCode();
    }

    @Transactional
    @Override
    public Long addDocHandoverBase(Long deptId, AddDocHandoverBaseVO vo) {
        CCustomerService cCustomerService = cCustomerServiceMapper.selectById(vo.getCustomerServiceId());
        if (cCustomerService == null) {
            throw new ServiceException("客户服务不存在");
        }

        Integer maxBatchNum = customerServiceDocHandoverMapper.selectMaxBatchNum(vo.getCustomerServiceId(), vo.getPeriod());

        //获取材料交接单的标签中是否有 凭票入账 的标签
        //Integer isVoucherEntry = getVoucherEntry(vo.getCustomerServicePeriodMonthId());
        Integer isVoucherEntry = vo.getIsVoucherEntry();

        LocalDateTime now = LocalDateTime.now();
        CustomerServiceDocHandover newEntry = new CustomerServiceDocHandover();
        //newEntry.setBatchNum(1);//在数据库里面设置正确的值，彳亍只是给一个信号值
        newEntry.setBatchNum(maxBatchNum == null ? 1 : (maxBatchNum + 1));//在数据库里面设置正确的值，彳亍只是给一个信号值
        newEntry.setCreditCode(cCustomerService.getCreditCode());
        newEntry.setTaxType(cCustomerService.getTaxType());
        newEntry.setCustomerName(cCustomerService.getCustomerName());
        newEntry.setCustomerServiceId(vo.getCustomerServiceId());
        newEntry.setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId());//有可能没有这个值
        newEntry.setIsDel(Boolean.FALSE);
        newEntry.setIsVoucherEntry(isVoucherEntry);
        newEntry.setPeriod(vo.getPeriod());
        newEntry.setRemark(vo.getRemark());
        newEntry.setStatus(DocHandoverStatus.NEED_PERFECT.getCode());
        newEntry.setTitle(String.format("材料交接单【%s】", vo.getPeriod()));//在数据库里面设置
        newEntry.setCreateTime(now);
        newEntry.setUpdateTime(now);

        if (vo.getAddFromType() == null) {
            newEntry.setIsEffect(Boolean.TRUE);
        } else {
            //从其他地方创建的
            newEntry.setIsEffect(Boolean.FALSE);
            newEntry.setAddFromType(vo.getAddFromType());
            newEntry.setAddFromId(vo.getAddFromId());
        }

        customerServiceDocHandoverMapper.insert(newEntry);

        //存附件
        iCustomerServiceDocHandoverFileService.saveFile(newEntry.getId(), vo.getFiles(), DocHandoverFileType.BASE, String.valueOf(newEntry.getId()));

        //凭票入账、备注、附件
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("凭票入账", YesNo.getByCode(isVoucherEntry).getName());
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                            .setDeptId(deptId)
                            .setOperType("新建交接单")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperContent(operContent)
                            .setOperRemark("新建交接单")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }

        return newEntry.getId();
    }

    @Override
    public DocHandoverBaseDTO getDocHandoverBase(Long id) {
        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }

        return getDocHandoverBaseResult(customerServiceDocHandover);
    }

    @Transactional
    @Override
    public void updateDocHandoverBase(Long deptId, UpdateDocHandoverBaseVO vo) {
        CustomerServiceDocHandover customerServiceDocHandover = getById(vo.getId());
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }

        if (!DocHandoverStatus.canUpdate(customerServiceDocHandover.getStatus())) {
            throw new ServiceException("当前状态的材料交接不可编辑");
        }

        CustomerServiceDocHandover updateEntry = new CustomerServiceDocHandover();
        updateEntry.setId(vo.getId());
        updateEntry.setRemark(vo.getRemark());
        updateEntry.setUpdateTime(LocalDateTime.now());

        //customerServiceDocHandoverMapper.updateCustomerServiceDocHandover(updateEntry);
        customerServiceDocHandoverMapper.updateById(updateEntry);

        //先删除原来的文件
        iCustomerServiceDocHandoverFileService.deleteByDocHandoverId(vo.getId(), Lists.newArrayList(DocHandoverFileType.BASE));
        //再存附件
        iCustomerServiceDocHandoverFileService.saveFile(vo.getId(), vo.getFiles(), DocHandoverFileType.BASE, String.valueOf(vo.getId()));

        //凭票入账、备注、附件
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("备注", vo.getRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(updateEntry.getId())
                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                            .setDeptId(deptId)
                            .setOperType("编辑交接单")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperContent(operContent)
                            .setOperRemark("编辑交接单-基础信息")
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public DocHandoverInstrumentDTO getDocHandoverInstrument(Long id) {
        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }

        return getDocHandoverInstrumentResult(customerServiceDocHandover);
    }


//    @Override
//    public Boolean checkCanSubmit(DocHandoverInstrumentVO vo) {
//        /*
//         * 校验可提交规则，符合可提交条件时，提交按钮可用，否则不可用，提交后状态变更为待核验；
//         *
//         * 可提交的校验条件：
//         *
//         * 1、至少一个模块的“有无票据”选项是“有”
//         *
//         * 2、有数据的模块中按以下规则验证是否填写完整：
//         *
//         * 银行模块：
//         * 对账单是有时，材料介质有勾选至少一项或附件数量大于0
//         * 回单是有时，材料介质有勾选至少一项或附件数量大于0
//         *
//         * 税号发票：
//         * 纸质数量有填写数字，大于等于0
//         * 纸质数量大于0时，票号说明必填
//         *
//         * 其他票据：
//         * 至少有一行数据
//         * 类型名必填且不可重复
//         * 单行的纸质数量和附件数量不可都为0
//         * 纸质数量为0时，票据说明必填
//         * */
//
//        boolean result = true;//false 校验不通过
//
//        if ((ObjectUtils.isEmpty(vo.getBankInstruments()) || vo.getBankInstruments().stream().allMatch(row -> Objects.equals(row.getHas(), YesNo.NO.getCode())))
//                && ObjectUtils.isEmpty(vo.getTaxInstruments())
//                && ObjectUtils.isEmpty(vo.getOtherInstruments())
//        ) {
//            result = false;
//        } else {
//
//
//            if (!ObjectUtils.isEmpty(vo.getBankInstruments())) {
//                for (DocHandoverInstrumentBankDTO row : vo.getBankInstruments()) {
//                    if (Objects.equals(row.getHas(), YesNo.NO.getCode())) {
//                        //无票据不用校验
//                        continue;
//                    }
//
//                    if (Objects.equals(row.getHasCheckTicket(), YesNo.YES.getCode())) {
//                        if (ObjectUtils.isEmpty(row.getCheckTicketContents()) && ObjectUtils.isEmpty(row.getCheckTicketFiles())) {
//                            result = false;
//                            break;
//                        }
//                    }
//
//                    if (Objects.equals(row.getHasBackTicket(), YesNo.YES.getCode())) {
//                        if (ObjectUtils.isEmpty(row.getBackTicketContents()) && ObjectUtils.isEmpty(row.getBackTicketFiles())) {
//                            result = false;
//                            break;
//                        }
//                    }
//
//                    /*if (!result) {
//                        break;
//                    }*/
//                }
//            }
//
//
//            if (!ObjectUtils.isEmpty(vo.getTaxInstruments())) {
//                for (DocHandoverInstrumentTaxItemDTO row : vo.getTaxInstruments()) {
//                    if (row.getPaperCount() == null || row.getPaperCount() <= 0) {
//                        result = false;
//                    } else {
//                        if (ObjectUtils.isEmpty(row.getRemark())) {
//                            result = false;
//                        }
//                    }
//
//                    if (!result) {
//                        break;
//                    }
//                }
//            }
//
//            if (ObjectUtils.isEmpty(vo.getOtherInstruments())) {
//                result = false;
//            } else {
//                Set<String> names = Sets.newHashSet();
//                for (DocHandoverInstrumentTaxItemDTO row : vo.getOtherInstruments()) {
//                    String name = row.getName();
//
//                    if (StringUtils.isEmpty(name) || names.contains(name)) {
//                        result = false;
//                        break;
//                    }
//
//                    if ((row.getPaperCount() == null || row.getPaperCount() <= 0) && ObjectUtils.isEmpty(row.getFiles())) {
//                        result = false;
//                        break;
//                    }
//
//                    if (row.getPaperCount() != null && row.getPaperCount() > 0) {
//                        if (ObjectUtils.isEmpty(row.getRemark())) {
//                            result = false;
//                            break;
//                        }
//                    }
//
//                    names.add(name);
//
//                /*if (!result) {
//                    break;
//                }*/
//                }
//            }
//        }
//
//        return result;
//    }

    @Override
    public Boolean checkCanSubmit(DocHandoverInstrumentDTO vo) {
        /*
         * 校验可提交规则，符合可提交条件时，提交按钮可用，否则不可用，提交后状态变更为待核验；
         *
         * 可提交的校验条件：
         *
         * 1、至少一个模块的“有无票据”选项是“有”
         *
         * 2、有数据的模块中按以下规则验证是否填写完整：
         *
         * 银行模块：
         * 对账单是有时，材料介质有勾选至少一项或附件数量大于0
         * 回单是有时，材料介质有勾选至少一项或附件数量大于0
         *
         * 税号发票：
         * 纸质数量有填写数字，大于等于0
         * 纸质数量大于0时，票号说明必填
         *
         * 其他票据：
         * 至少有一行数据
         * 类型名必填且不可重复
         * 单行的纸质数量和附件数量不可都为0
         * 纸质数量为0时，票据说明必填
         * */

        boolean result = true;//false 校验不通过

        if ((ObjectUtils.isEmpty(vo.getBankInstruments()) || vo.getBankInstruments().stream().allMatch(row -> Objects.equals(row.getHas(), YesNo.NO.getCode())))
                && (vo.getHasTaxTicket() == null || Objects.equals(vo.getHasTaxTicket(), YesNo.NO.getCode()))
                && (vo.getHasOtherTicket() == null || Objects.equals(vo.getHasOtherTicket(), YesNo.NO.getCode()))
        ) {
            result = false;
        } else {
            if (!ObjectUtils.isEmpty(vo.getBankInstruments())) {
                for (DocHandoverInstrumentBankDTO row : vo.getBankInstruments()) {
                    if (Objects.equals(row.getHas(), YesNo.NO.getCode())) {
                        //无票据不用校验
                        continue;
                    }

                    if (Objects.equals(row.getHasPayment(), YesNo.NO.getCode())
                            && Objects.equals(row.getHasCheckTicket(), YesNo.NO.getCode())
                            && Objects.equals(row.getHasBackTicket(), YesNo.NO.getCode())) {
                        result = false;
                        break;
                    }

                    if (Objects.equals(row.getHasCheckTicket(), YesNo.YES.getCode())) {
                        if (ObjectUtils.isEmpty(row.getCheckTicketContents()) && ObjectUtils.isEmpty(row.getCheckTicketFiles())) {
                            result = false;
                            break;
                        }
                    }

                    if (Objects.equals(row.getHasBackTicket(), YesNo.YES.getCode())) {
                        if (ObjectUtils.isEmpty(row.getBackTicketContents()) && ObjectUtils.isEmpty(row.getBackTicketFiles())) {
                            result = false;
                            break;
                        }
                    }
                }
            }

            if (Objects.equals(vo.getHasTaxTicket(), YesNo.YES.getCode())) {
                if (ObjectUtils.isEmpty(vo.getTaxInstruments())) {
                    result = false;
                } else {
                    for (DocHandoverInstrumentTaxItemDTO row : vo.getTaxInstruments()) {
                        if (row.getPaperCount() == null || row.getPaperCount() < 0) {
                            result = false;
                            break;
                        } else {
                            if (row.getPaperCount() > 0) {
                                if (ObjectUtils.isEmpty(row.getRemark())) {
                                    result = false;
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            if (!ObjectUtils.isEmpty(vo.getOtherInstruments())) {
                Set<String> names = Sets.newHashSet();
                for (DocHandoverInstrumentTaxItemDTO row : vo.getOtherInstruments()) {
                    String name = row.getName();

                    if (StringUtils.isEmpty(name) || names.contains(name)) {
                        result = false;
                        break;
                    }

                    if ((row.getPaperCount() == null || row.getPaperCount() <= 0) && ObjectUtils.isEmpty(row.getFiles())) {
                        result = false;
                        break;
                    }

                    if (row.getPaperCount() != null && row.getPaperCount() > 0) {
                        if (ObjectUtils.isEmpty(row.getRemark())) {
                            result = false;
                            break;
                        }
                    }

                    names.add(name);
                }
            }
        }

        return result;
    }

    @Override
    public Boolean checkCanSubmitV2(DocHandoverInstrumentDTO vo) {
        /*
         * 最新：全部是无，也可以调教
         */

        boolean result = true;//false 校验不通过

        if ((ObjectUtils.isEmpty(vo.getBankInstruments()) || vo.getBankInstruments().stream().allMatch(row -> Objects.equals(row.getHas(), YesNo.NO.getCode())))
                && (vo.getHasTaxTicket() == null || Objects.equals(vo.getHasTaxTicket(), YesNo.NO.getCode()))
                && (vo.getHasOtherTicket() == null || Objects.equals(vo.getHasOtherTicket(), YesNo.NO.getCode()))
        ) {
            //result = false;
            log.info("checkCanSubmitV2");
        } else {
            if (!ObjectUtils.isEmpty(vo.getBankInstruments())) {
                for (DocHandoverInstrumentBankDTO row : vo.getBankInstruments()) {
                    if (Objects.equals(row.getHas(), YesNo.NO.getCode())) {
                        //无票据不用校验
                        continue;
                    }

                    if (Objects.equals(row.getHasPayment(), YesNo.NO.getCode())
                            && Objects.equals(row.getHasCheckTicket(), YesNo.NO.getCode())
                            && Objects.equals(row.getHasBackTicket(), YesNo.NO.getCode())) {
                        result = false;
                        break;
                    }

                    if (Objects.equals(row.getHasCheckTicket(), YesNo.YES.getCode())) {
                        if (ObjectUtils.isEmpty(row.getCheckTicketContents()) && ObjectUtils.isEmpty(row.getCheckTicketFiles())) {
                            result = false;
                            break;
                        }
                    }

                    if (Objects.equals(row.getHasBackTicket(), YesNo.YES.getCode())) {
                        if (ObjectUtils.isEmpty(row.getBackTicketContents()) && ObjectUtils.isEmpty(row.getBackTicketFiles())) {
                            result = false;
                            break;
                        }
                    }
                }
            }

            if (Objects.equals(vo.getHasTaxTicket(), YesNo.YES.getCode())) {
                if (ObjectUtils.isEmpty(vo.getTaxInstruments())) {
                    result = false;
                } else {
                    for (DocHandoverInstrumentTaxItemDTO row : vo.getTaxInstruments()) {
                        if (row.getPaperCount() == null || row.getPaperCount() < 0) {
                            result = false;
                            break;
                        } else {
                            if (row.getPaperCount() > 0) {
                                if (ObjectUtils.isEmpty(row.getRemark())) {
                                    result = false;
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            if (Objects.equals(vo.getHasOtherTicket(), YesNo.YES.getCode())) {
                if (ObjectUtils.isEmpty(vo.getOtherInstruments())) {
                    result = false;
                } else {
                    Set<String> names = Sets.newHashSet();
                    for (DocHandoverInstrumentTaxItemDTO row : vo.getOtherInstruments()) {
                        String name = row.getName();

                        if (StringUtils.isEmpty(name) || names.contains(name)) {
                            result = false;
                            break;
                        }

                        if ((row.getPaperCount() == null || row.getPaperCount() <= 0) && ObjectUtils.isEmpty(row.getFiles())) {
                            result = false;
                            break;
                        }

                        if (row.getPaperCount() != null && row.getPaperCount() > 0) {
                            if (ObjectUtils.isEmpty(row.getRemark())) {
                                result = false;
                                break;
                            }
                        }

                        names.add(name);
                    }
                }
            }
        }

        return result;
    }

    @Transactional
    @Override
    public void saveDocHandoverInstrument(Long deptId, DocHandoverInstrumentDTO vo, Boolean isSubmit) {
        //校验可提交规则，符合可提交条件时，保存后变成“待提交”状态；
        //不符合可提交条件时，保存后变成“待完善”状态。条件判断见13
        //13=checkCanSubmit

        Long id = vo.getId();

        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }
        if (!DocHandoverStatus.canUpdate(customerServiceDocHandover.getStatus())) {
            throw new ServiceException("当前状态的材料交接不可编辑");
        }

        //删除原来的票据数据，然后再新增
        deleteInstrument(id);

        //存 银行票据
        saveBankInstruments(id, vo.getBankInstruments());
        saveTaxInstruments(id, vo.getTaxInstruments(), vo.getOtherInstruments());

        Boolean checkCanSubmitFlag = checkCanSubmitV2(vo);
        Integer status = getStatus(isSubmit, checkCanSubmitFlag);

        CustomerServiceDocHandover updateEntry = new CustomerServiceDocHandover();

        if (isSubmit) {
            //提交的时候  需要记录提交人

            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
            Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
            //SysEmployee sysEmployee = remoteEmployeeService.getEmployeeByUserIdAndDeptId(deptId, userId).getDataThrowException();
            SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

            //更新提交信息
            updateEntry.setSubmitEmployeeDeptId(deptId);
            updateEntry.setSubmitEmployeeDeptName(sysDept.getDeptName());
            /*updateEntry.setSubmitEmployeeId(sysEmployee.getEmployeeId());
            updateEntry.setSubmitEmployeeName(sysEmployee.getEmployeeName());*/
            updateEntry.setSubmitEmployeeId(employeeId);
            updateEntry.setSubmitEmployeeName(setOperName);
            updateEntry.setSubmitTime(LocalDateTime.now());
        }

        updateEntry.setId(vo.getId());
        updateEntry.setUpdateTime(LocalDateTime.now());
        //updateEntry.setStatus(checkCanSubmit(vo) ? DocHandoverStatus.NEED_SUBMIT.getCode() : DocHandoverStatus.NEED_PERFECT.getCode());
        updateEntry.setStatus(status);
        updateEntry.setBankInstrumentCount(ObjectUtils.isEmpty(vo.getBankInstruments()) ? 0 : vo.getBankInstruments().size());
        updateEntry.setBankInstrumentUnHasCount(ObjectUtils.isEmpty(vo.getBankInstruments()) ? 0 : (int) vo.getBankInstruments().stream().filter(r -> Objects.equals(r.getHas(), YesNo.NO.getCode())).count());
        updateEntry.setHasTaxTicket(vo.getHasTaxTicket());
        updateEntry.setHasOtherTicket(vo.getHasOtherTicket());

        //更新完整度，无材料
        if (checkAllContentUnHas(updateEntry)) {
            updateEntry.setWholeLevel(WholeLevel.SUBMIT_NO_DATA.getCode());
            updateEntry.setStatus(DocHandoverStatus.ALREADY_VERIFICATION.getCode());
        }

        //customerServiceDocHandoverMapper.updateCustomerServiceDocHandover(updateEntry);
        customerServiceDocHandoverMapper.updateById(updateEntry);

        Map<String, Object> map = Maps.newLinkedHashMap();
        String operContent = JSONObject.toJSONString(map);
        try {
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(id)
                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                            .setDeptId(deptId)
                            .setOperType(isSubmit ? "提交交接单" : "保存交接单")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperContent(operContent)
                            .setOperRemark(isSubmit ? "提交交接单" : "保存交接单")
                            .setOperImages(null)
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public void submitDocHandoverInstrument(Long deptId, DocHandoverInstrumentDTO vo) {
        /*
         * 校验可提交规则，符合可提交条件时，提交按钮可用，否则不可用，提交后状态变更为待核验；
         *
         * 可提交的校验条件：
         *
         * 1、至少一个模块的“有无票据”选项是“有”
         *
         * 2、有数据的模块中按以下规则验证是否填写完整：
         *
         * 银行模块：
         * 对账单是有时，材料介质有勾选至少一项或附件数量大于0
         * 回单是有时，材料介质有勾选至少一项或附件数量大于0
         * 税号发票：
         * 纸质数量有填写数字，大于等于0
         * 纸质数量大于0时，票号说明必填
         * 其他票据：
         * 至少有一行数据
         * 类型名必填且不可重复
         * 单行的纸质数量和附件数量不可都为0
         * 纸质数量为0时，票据说明必填
         *
         *
         * 提交后记操作记录：
         *
         * 操作时间：完成操作的时间
         * 操作人：发起操作的用户当时的员工名
         * 操作动作：提交交接单
         */

        //校验可提交规则，符合可提交条件时，提交按钮可用，否则不可用，提交后状态变更为待核验；
        if (!checkCanSubmitV2(vo)) {
            throw new ServiceException("不符合可提交条件，不可提交");
        }

        saveDocHandoverInstrument(deptId, vo, true);
    }

    @Override
    public DocHandoverFullDTO getDocHandoverFull(Long id) {
        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }

        //获取基础信息
        DocHandoverBaseDTO docHandoverBaseDTO = getDocHandoverBaseResult(customerServiceDocHandover);

        //获取票据信息
        DocHandoverInstrumentDTO docHandoverInstrumentDTO = getDocHandoverInstrumentResult(customerServiceDocHandover);

        //获取核验文件
        List<CustomerServiceDocHandoverFile> CustomerServiceDocHandoverFiles = iCustomerServiceDocHandoverFileService.selectByDocHandover(
                id, Lists.newArrayList(DocHandoverFileType.OPERATE_VERIFICATION)
        );

        //获取核验信息
        DocHandoverVerificationDTO verification = DocHandoverVerificationDTO.builder()
                .files(iCustomerServiceDocHandoverFileService.covToCommonFileVO(CustomerServiceDocHandoverFiles))
                .verificationRemark(StringUtils.isEmpty(customerServiceDocHandover.getVerificationRemark()) ? "-" : customerServiceDocHandover.getVerificationRemark())
                .verificationTime(customerServiceDocHandover.getVerificationTime())
                .wholeLevel(customerServiceDocHandover.getWholeLevel())
                .wholeLevelStr(WholeLevel.getByCode(customerServiceDocHandover.getWholeLevel()).getName())
                .build();

        return DocHandoverFullDTO.builder()
                .id(id)
                .status(customerServiceDocHandover.getStatus())
                .statusStr(DocHandoverStatus.getByCode(customerServiceDocHandover.getStatus()).getName())

                .bankInstruments(docHandoverInstrumentDTO == null ? null : docHandoverInstrumentDTO.getBankInstruments())
                .docHandoverBase(docHandoverBaseDTO)
                .otherInstruments(docHandoverInstrumentDTO == null ? null : docHandoverInstrumentDTO.getOtherInstruments())
                .taxInstruments(docHandoverInstrumentDTO == null ? null : docHandoverInstrumentDTO.getTaxInstruments())
                .verification(verification)

                .hasTaxTicket(docHandoverInstrumentDTO == null ? null : docHandoverInstrumentDTO.getHasTaxTicket())
                .hasOtherTicket(docHandoverInstrumentDTO == null ? null : docHandoverInstrumentDTO.getHasOtherTicket())
                .build();
    }

    @Override
    public List<DocHandoverSimpleDTO> getDocHandoverOfPeriodByCustomerServiceId(Long customerServiceId, Integer period) {
        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        .eq(CustomerServiceDocHandover::getIsDel, Boolean.FALSE)
                        .eq(CustomerServiceDocHandover::getCustomerServiceId, customerServiceId)
                        .eq(CustomerServiceDocHandover::getPeriod, period)
        );
        /*List<DocHandoverSimpleDTO> result = Lists.newArrayList();

        if (ObjectUtils.isEmpty(customerServiceDocHandovers)) {
            return result;
        }

        return customerServiceDocHandovers.stream()
                .map(row -> DocHandoverSimpleDTO.builder()
                        .batchNum(row.getBatchNum())
                        .id(row.getId())
                        .title(row.getTitle())
                        .titleFull(row.getTitle() + "-" + row.getBatchNum())
                        .build())
                .collect(Collectors.toList());*/

        return covDocHandoverSimpleDTO(customerServiceDocHandovers);
    }

    @Override
    @Transactional
    public void operateSubmit(Long deptId, Long id) {
        //提交是个列表操作，没有表单，退回时不删除原来提交的内容，如果没有编辑直接提交等于原来的内容直接提交

        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }
        if (!DocHandoverStatus.canOperateSubmit(customerServiceDocHandover.getStatus())) {
            throw new ServiceException("当前状态的材料交接不可提交");
        }

        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        //SysEmployee sysEmployee = remoteEmployeeService.getEmployeeByUserIdAndDeptId(deptId, userId).getDataThrowException();
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

        LocalDateTime now = LocalDateTime.now();
        CustomerServiceDocHandover updateEntry = new CustomerServiceDocHandover();
        updateEntry.setId(id);
        updateEntry.setStatus(DocHandoverStatus.NEED_VERIFICATION.getCode());
        updateEntry.setUpdateTime(now);
        //更新提交信息
        updateEntry.setSubmitEmployeeDeptId(deptId);
        updateEntry.setSubmitEmployeeDeptName(sysDept.getDeptName());
        /*updateEntry.setSubmitEmployeeId(sysEmployee.getEmployeeId());
        updateEntry.setSubmitEmployeeName(sysEmployee.getEmployeeName());*/
        updateEntry.setSubmitEmployeeId(employeeId);
        updateEntry.setSubmitEmployeeName(setOperName);
        updateEntry.setSubmitTime(now);

        //customerServiceDocHandoverMapper.updateCustomerServiceDocHandover(updateEntry);
        customerServiceDocHandoverMapper.updateById(updateEntry);

        Map<String, Object> map = Maps.newLinkedHashMap();
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(id)
                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                            .setDeptId(deptId)
                            .setOperType("提交交接单")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("提交交接单")
                            .setOperImages(null)
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public Integer operateSubmitBatch(Long deptId, List<Long> ids) {
        //提交是个列表操作，没有表单，退回时不删除原来提交的内容，如果没有编辑直接提交等于原来的内容直接提交

        int count = 0;

        for (Long id : ids) {
            try {
                operateSubmit(deptId, id);

                count++;
            } catch (Exception e) {
                log.error("operateSubmitBatch 失败 id:{} e:{}", id, e.getMessage());
            }
        }

        return count;
    }

    @Override
    @Transactional
    public void operateVerification(Long deptId, OperateVerificationVO vo) {
        //待核验，且承验人是当前组织的数据权限范围内

        Long id = vo.getId();

        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }
        //log.info("待核验的材料交接的状态：{}", customerServiceDocHandover.getStatus());
        if (!DocHandoverStatus.canOperateVerification(customerServiceDocHandover.getStatus())) {
            throw new ServiceException("当前状态的材料交接不可核验");
        }

        /*Integer verificationEmployeeType = customerServiceDocHandover.getVerificationEmployeeType();
        if (verificationEmployeeType > 0) {
            //总部核验

            //怎么判断是总部
            //deptId
        } else {
            //会计区域核验，就是会计部门

            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = getAccountingInfoSource(Lists.newArrayList(customerServiceDocHandover.getCustomerServicePeriodMonthId()));
            List<AccountingInfoSourceDTO> hisAccountingInfoSource = accountingInfoSourceMap.getOrDefault(customerServiceDocHandover.getCustomerServicePeriodMonthId(), Lists.newArrayList());
            if (!hisAccountingInfoSource.stream().map(AccountingInfoSourceDTO::getAccountingDeptId).collect(Collectors.toList()).contains(deptId)) {
                throw new ServiceException("没有权限核验");
            }
        }*/

        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

        LocalDateTime now = LocalDateTime.now();
        CustomerServiceDocHandover updateEntry = new CustomerServiceDocHandover();
        updateEntry.setId(id);
        updateEntry.setStatus(DocHandoverStatus.ALREADY_VERIFICATION.getCode());
        updateEntry.setUpdateTime(now);
        //核验信息
        updateEntry.setVerificationEmployeeDeptId(deptId);
        updateEntry.setVerificationEmployeeDeptName(sysDept.getDeptName());
        updateEntry.setVerificationEmployeeId(employeeId);
        updateEntry.setVerificationEmployeeName(setOperName);
        updateEntry.setVerificationRemark(vo.getVerificationRemark());
        updateEntry.setVerificationTime(now);
        updateEntry.setWholeLevel(vo.getWholeLevel());

        //customerServiceDocHandoverMapper.updateCustomerServiceDocHandover(updateEntry);
        customerServiceDocHandoverMapper.updateById(updateEntry);

        //删除原来的核验附件
        iCustomerServiceDocHandoverFileService.deleteByDocHandoverId(id, Lists.newArrayList(DocHandoverFileType.OPERATE_VERIFICATION));
        //再存附件
        iCustomerServiceDocHandoverFileService.saveFile(vo.getId(), vo.getFiles(), DocHandoverFileType.OPERATE_VERIFICATION, String.valueOf(vo.getId()));

        //操作内容：完整度、核验说明、附件，如果有则显示，没有则整行不显示
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("完整度", WholeLevel.getByCode(vo.getWholeLevel()).getName());
        map.put("核验说明", vo.getVerificationRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(id)
                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                            .setDeptId(deptId)
                            .setOperType("完成核验")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("完成核验")
                            .setOperImages(null)
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public Integer operateVerificationBatch(Long deptId, OperateVerificationBatchVO vo) {
        Integer wholeLevel = vo.getWholeLevel();
        String verificationRemark = vo.getVerificationRemark();
        List<CommonFileVO> files = vo.getFiles();

        int count = 0;

        for (Long id : vo.getIds()) {
            try {
                operateVerification(deptId,
                        OperateVerificationVO.builder()
                                .files(files)
                                .id(id)
                                .verificationRemark(verificationRemark)
                                .wholeLevel(wholeLevel)
                                .build()
                );

                count++;
            } catch (Exception e) {
                log.error("operateVerificationBatch 失败 id:{} e:{}", id, e.getMessage());
            }
        }

        return count;
    }

    @Override
    @Transactional
    public void operateBack(Long deptId, OperateBackVO vo) {
        Long id = vo.getId();

        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }
        if (!DocHandoverStatus.canOperateBack(customerServiceDocHandover.getStatus())) {
            throw new ServiceException("当前状态的材料交接不可退回");
        }

        DocHandoverStatus status;
        if (Objects.equals(customerServiceDocHandover.getStatus(), DocHandoverStatus.NEED_VERIFICATION.getCode())) {
            status = DocHandoverStatus.NEED_RE_SUBMIT;
        } else if (Objects.equals(customerServiceDocHandover.getStatus(), DocHandoverStatus.ALREADY_VERIFICATION.getCode())) {
            status = DocHandoverStatus.NEED_VERIFICATION;
        } else {
            throw new ServiceException("当前状态的材料交接不可退回");
        }

        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

        LocalDateTime now = LocalDateTime.now();
        CustomerServiceDocHandover updateEntry = new CustomerServiceDocHandover();
        updateEntry.setId(id);
        updateEntry.setStatus(status.getCode());
        updateEntry.setUpdateTime(now);
        //核心信息
        updateEntry.setBackEmployeeDeptId(deptId);
        updateEntry.setBackEmployeeDeptName(sysDept.getDeptName());
        updateEntry.setBackEmployeeId(employeeId);
        updateEntry.setBackEmployeeName(setOperName);
        updateEntry.setBackRemark(vo.getBackRemark());
        updateEntry.setBackTime(now);

        //customerServiceDocHandoverMapper.updateCustomerServiceDocHandover(updateEntry);
        customerServiceDocHandoverMapper.updateById(updateEntry);

        //删除原来的附件
        iCustomerServiceDocHandoverFileService.deleteByDocHandoverId(id, Lists.newArrayList(DocHandoverFileType.OPERATE_BACK));
        //再存附件
        iCustomerServiceDocHandoverFileService.saveFile(vo.getId(), vo.getFiles(), DocHandoverFileType.OPERATE_BACK, String.valueOf(vo.getId()));

        //操作内容：备注、附件
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("备注", vo.getBackRemark());
        String operContent = JSONObject.toJSONString(map);
        try {
            //Long userId = SecurityUtils.getUserId();
            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(id)
                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                            .setDeptId(deptId)
                            .setOperType("退回材料交接")
                            .setOperName(setOperName)
                            .setOperContent(operContent)
                            .setOperRemark("退回材料交接")
                            .setOperImages(null)
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public Integer operateBackBatch(Long deptId, OperateBackBatchVO vo) {
        String rackRemark = vo.getBackRemark();
        List<CommonFileVO> files = vo.getFiles();

        int count = 0;

        for (Long id : vo.getIds()) {
            try {
                operateBack(deptId,
                        OperateBackVO.builder()
                                .backRemark(rackRemark)
                                .files(files)
                                .id(id)
                                .build()
                );

                count++;
            } catch (Exception e) {
                log.error("operateBackBatch 失败 id:{} e:{}", id, e.getMessage());
            }
        }

        return count;
    }

    @Override
    public ChangeVerificationInfoDTO getChangeVerificationInfo(Long id) {
        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }

        String accountingEmployeeNameFull = "";
        if (customerServiceDocHandover.getVerificationEmployeeType() > 0) {
            //原本是总部
            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = getAccountingInfoSource(Lists.newArrayList(customerServiceDocHandover.getCustomerServicePeriodMonthId()));
            accountingEmployeeNameFull = handleAccountingEmployeeNameFull(accountingInfoSourceMap.get(customerServiceDocHandover.getCustomerServicePeriodMonthId()));
        } else {
            accountingEmployeeNameFull = "总部";
        }

        return ChangeVerificationInfoDTO.builder()
                .accountingEmployeeNameFull(accountingEmployeeNameFull)
                .id(id)
                .build();
    }

    @Override
    @Transactional
    public void operateChangeVerification(Long deptId, Long id) {
        /*
         * 转交对象
         * 当承验组织是总部时，提示：确认由A（B）核验吗？
         * A：客户账期会计小组名
         * B：客户账期会计人员员工名，多个用逗号隔开
         * 当承验组织不是总部时，提示：确认要交由总部核验吗？
         */
        //这个应该不属于权限吧，这个应该是说判断他当前是总部的，那你转交就只能转交给会计小组，你判断他当前是会计小组的，转交就只能转交给总部

        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }
        if (!DocHandoverStatus.canOperateChangeVerification(customerServiceDocHandover.getStatus())) {
            throw new ServiceException("当前状态的材料交接不可承验转交");
        }

        //原来的承验类型
        Integer verificationEmployeeType = customerServiceDocHandover.getVerificationEmployeeType();

        CustomerServiceDocHandover updateEntry = new CustomerServiceDocHandover();
        updateEntry.setId(id);
        updateEntry.setUpdateTime(LocalDateTime.now());
        //更新其他信息
        updateEntry.setVerificationEmployeeType(-verificationEmployeeType);

        //customerServiceDocHandoverMapper.updateCustomerServiceDocHandover(updateEntry);
        customerServiceDocHandoverMapper.updateById(updateEntry);

        String name = "";
        if (verificationEmployeeType > 0) {
            //原本是总部
            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = getAccountingInfoSource(Lists.newArrayList(customerServiceDocHandover.getCustomerServicePeriodMonthId()));
            name = handleAccountingEmployeeNameFull(accountingInfoSourceMap.get(customerServiceDocHandover.getCustomerServicePeriodMonthId()));
        }

        //转交给总部核验/转交给A（B）核验。
        //转交给总部核验
        //转交给福州十组（刘莉）核验。
//        String operContent = verificationEmployeeType > 0 ? String.format("转交给%s核验", name) : "转交给总部核验";
        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("承验转交", verificationEmployeeType > 0 ? (StringUtils.isEmpty(name) ? "转交给区域核验" : String.format("转交给%s核验", name)) : "转交给总部核验");
        String operContent = JSONObject.toJSONString(map);
        try {
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(id)
                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                            .setDeptId(deptId)
                            .setOperType("承验转交")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperContent(operContent)
                            .setOperRemark("承验转交")
                            .setOperImages(null)
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }

    }

    @Override
    public Integer operateChangeVerificationBatch(Long deptId, List<Long> ids) {
        int count = 0;

        for (Long id : ids) {
            try {
                operateChangeVerification(deptId, id);

                count++;
            } catch (Exception e) {
                log.error("operateChangeVerificationBatch 失败 id:{} e:{}", id, e.getMessage());
            }
        }

        return count;
    }

    @Override
    @Transactional
    public void operateDelete(Long deptId, Long id) {
        CustomerServiceDocHandover customerServiceDocHandover = getById(id);
        if (customerServiceDocHandover == null || customerServiceDocHandover.getIsDel()) {
            throw new ServiceException("材料交接不存在");
        }
        if (!DocHandoverStatus.canOperateDelete(customerServiceDocHandover.getStatus())) {
            throw new ServiceException("当前状态的材料交接不可删除");
        }

        CustomerServiceDocHandover updateEntry = new CustomerServiceDocHandover();
        updateEntry.setId(id);
        updateEntry.setUpdateTime(LocalDateTime.now());
        updateEntry.setIsDel(Boolean.TRUE);

        //customerServiceDocHandoverMapper.updateCustomerServiceDocHandover(updateEntry);
        customerServiceDocHandoverMapper.updateById(updateEntry);

        Map<String, Object> map = Maps.newLinkedHashMap();
        String operContent = JSONObject.toJSONString(map);
        try {
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();

            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(id)
                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                            .setDeptId(deptId)
                            .setOperType("删除交接单")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperContent(operContent)
                            .setOperRemark("删除交接单")
                            .setOperImages(null)
                            .setOperUserId(userId)
            );
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public Integer operateDeleteBatch(Long deptId, List<Long> ids) {
        int count = 0;

        for (Long id : ids) {
            try {
                operateDelete(deptId, id);

                count++;
            } catch (Exception e) {
                log.error("operateDeleteBatch 失败 id:{} e:{}", id, e.getMessage());
            }
        }

        return count;
    }

    @Override
    public List<DocHandoverSimpleDTO> getDocHandoverTitleByAddFromType(Integer addFromType, Long addFromId) {
        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        //.eq(CustomerServiceDocHandover::getIsDel, Boolean.FALSE)
                        .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                        .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
        );

        return covDocHandoverSimpleDTO(customerServiceDocHandovers);
    }

    @Override
    public List<DocHandoverInstrumentDTO> getDocHandoverInstrumentByAddFromType(Integer addFromType, Long addFromId) {
        List<DocHandoverInstrumentDTO> result = Lists.newArrayList();

        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        //.eq(CustomerServiceDocHandover::getIsDel, Boolean.FALSE)
                        .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                        .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
        );

        for (CustomerServiceDocHandover customerServiceDocHandover : customerServiceDocHandovers) {
            result.add(
                    getDocHandoverInstrumentResult(customerServiceDocHandover)
            );
        }

        return result;
    }

    @Override
    public List<CustomerServiceDocHandover> getDocHandoverByAddFromType(Integer addFromType, Long addFromId) {
        return customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        //.eq(CustomerServiceDocHandover::getIsDel, Boolean.FALSE)
                        .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                        .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
        );
    }

    @Override
    public void operateDeleteByAddFromType(Long deptId, Integer addFromType, Long addFromId) {
        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                        .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
        );

        if (!ObjectUtils.isEmpty(customerServiceDocHandovers)) {
            List<Long> ids = customerServiceDocHandovers.stream().map(CustomerServiceDocHandover::getId).collect(Collectors.toList());

            //删除主体
            removeByIds(ids);

            //删除银行信息
            iCustomerServiceDocHandoverBankInstrumentService.remove(
                    new LambdaQueryWrapper<CustomerServiceDocHandoverBankInstrument>()
                            .in(CustomerServiceDocHandoverBankInstrument::getCustomerServiceDocId, ids)
            );

            //删除税票信息
            iCustomerServiceDocHandoverTaxInstrumentService.remove(
                    new LambdaQueryWrapper<CustomerServiceDocHandoverTaxInstrument>()
                            .in(CustomerServiceDocHandoverTaxInstrument::getCustomerServiceDocId, ids)
            );

            //删除附件
            iCustomerServiceDocHandoverFileService.remove(
                    new LambdaQueryWrapper<CustomerServiceDocHandoverFile>()
                            .in(CustomerServiceDocHandoverFile::getCustomerServiceDocId, ids)
            );
        }
    }

    @Override
    public void operateEffectByAddFromType(Long deptId, Integer addFromType, Long addFromId, Integer isVoucherEntry) {
        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                        .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
        );

        if (!ObjectUtils.isEmpty(customerServiceDocHandovers)) {
            //获取操作人员信息
            Long userId = SecurityUtils.getUserId();
            OperateUserInfoDTO operateUserInfoDTO = getOperateUserInfo(deptId, userId);

            //插入操作日志
            customerServiceDocHandovers.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("凭票入账：", YesNo.getByCode(isVoucherEntry).getName());
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("分派补账服务生成材料交接单")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark("分派补账服务生成材料交接单")
                                    .setOperImages(null)
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });

            //批量更新
            update(new LambdaUpdateWrapper<CustomerServiceDocHandover>()
                    .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                    .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
                    .set(CustomerServiceDocHandover::getIsEffect, Boolean.TRUE)
                    .set(CustomerServiceDocHandover::getIsVoucherEntry, isVoucherEntry));
        }
    }

    //内部逻辑会捞一把刚才生成的账期，设置到材料交接单上
    @Override
    public void operateEffectByAddFromTypeV2(Long deptId, Integer addFromType, Long addFromId, Integer isVoucherEntry) {
        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                        .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
        );

        if (!ObjectUtils.isEmpty(customerServiceDocHandovers)) {
            //获取操作人员信息
            Long userId = SecurityUtils.getUserId();
            OperateUserInfoDTO operateUserInfoDTO = getOperateUserInfo(deptId, userId);
            LocalDateTime now = LocalDateTime.now();

            //内部逻辑会捞一把刚才生成的账期，设置到材料交接单上
            List<CustomerServicePeriodMonth> customerServicePeriodMonths = customerServicePeriodMonthMapper.selectList(
                    new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                            .eq(CustomerServicePeriodMonth::getAddFromType, addFromType)
                            .eq(CustomerServicePeriodMonth::getAddFromId, addFromId)
            );
            Map<Integer, CustomerServicePeriodMonth> customerServicePeriodMonthsMap = customerServicePeriodMonths.stream().collect(Collectors.toMap(CustomerServicePeriodMonth::getPeriod, r -> r));


            for (CustomerServiceDocHandover row : customerServiceDocHandovers) {

                //插入操作日志
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    map.put("凭票入账：", YesNo.getByCode(isVoucherEntry).getName());
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("分派补账服务生成材料交接单")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark("分派补账服务生成材料交接单")
                                    .setOperImages(null)
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                }

                //这个材料交接单的账期
                CustomerServicePeriodMonth hisPeriod = customerServicePeriodMonthsMap.get(row.getPeriod());

                row.setUpdateTime(now);
                row.setIsEffect(Boolean.TRUE);
                row.setIsVoucherEntry(isVoucherEntry);
                row.setCustomerServicePeriodMonthId(hisPeriod == null ? null : hisPeriod.getId());
            }

            updateBatchById(customerServiceDocHandovers);
        }
    }

    @Override
    @Transactional
    public void operateUnEffectByAddFromType(Long deptId, Integer addFromType, Long addFromId) {
        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                        .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
        );

        if (!ObjectUtils.isEmpty(customerServiceDocHandovers)) {
            //获取操作人员信息
            Long userId = SecurityUtils.getUserId();
            OperateUserInfoDTO operateUserInfoDTO = getOperateUserInfo(deptId, userId);

            //插入操作日志
            customerServiceDocHandovers.forEach(row -> {
                try {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    String operContent = JSONObject.toJSONString(map);

                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("退回补账服务删除材料交接单")
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperContent(operContent)
                                    .setOperRemark("退回补账服务删除材料交接单")
                                    .setOperImages(null)
                                    .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });

            //批量更新
            update(new LambdaUpdateWrapper<CustomerServiceDocHandover>()
                    .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                    .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
                    .set(CustomerServiceDocHandover::getIsEffect, Boolean.FALSE)
            );
        }
    }

    @Override
    public void operateSubmitByAddFromType(Long deptId, Integer addFromType, Long addFromId) {
        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        .eq(CustomerServiceDocHandover::getAddFromType, addFromType)
                        .eq(CustomerServiceDocHandover::getAddFromId, addFromId)
        );

        if (!ObjectUtils.isEmpty(customerServiceDocHandovers)) {
            operateSubmitBatch(deptId,
                    customerServiceDocHandovers.stream().map(CustomerServiceDocHandover::getId).distinct().collect(Collectors.toList()));
        }
    }

    //处理，标签搜索
    @Override
    public TagSearchVO tagSearch(Integer tagIncludeFlag, String tagName, TagBusinessType tagBusinessType) {
        boolean needSearch = true;//是否需要搜索
        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
        List<Long> ids = null;

        if (StringUtils.isEmpty(tagName)) {
            needSearch = false;
            tagIncludeFlag = null;
        } else {
            if (tagIncludeFlag == null) {
                tagIncludeFlag = 1;
            }

            ids = icBusinessTagRelationService.getCustomerIdsByTagNameLike(tagName, tagBusinessType);

            if (ObjectUtils.isEmpty(ids) && tagIncludeFlag == 1) {
                fail = true;
            }
        }

        return TagSearchVO.builder()
                .needSearch(needSearch)
                .fail(fail)
                .tagIncludeFlag(tagIncludeFlag)
                .ids(ids)
                .build();
    }

    //处理，会计搜索
    @Override
    public CommonIdsSearchVO accountingSearch(String accountingEmployee) {
        boolean needSearch = true;//是否需要搜索
        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
        List<Long> ids = null;

        if (StringUtils.isEmpty(accountingEmployee)) {
            needSearch = false;
        } else {
            //拿到所有账期的所有会计组别
            ids = customerServicePeriodMonthMapper.searchAccounting(accountingEmployee);

            if (ObjectUtils.isEmpty(ids)) {
                fail = true;
            }
        }

        return CommonIdsSearchVO.builder()
                .needSearch(needSearch)
                .fail(fail)
                .ids(ids)
                .build();
    }

    public CommonIdsSearchVO advisorSearch(String advisorEmployee) {
        boolean needSearch = true;//是否需要搜索
        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
        List<Long> ids = null;

        if (StringUtils.isEmpty(advisorEmployee)) {
            needSearch = false;
        } else {
            ids = customerServicePeriodMonthMapper.searchAdvisor(advisorEmployee);

            if (ObjectUtils.isEmpty(ids)) {
                fail = true;
            }
        }

        return CommonIdsSearchVO.builder()
                .needSearch(needSearch)
                .fail(fail)
                .ids(ids)
                .build();
    }

    //获取会计信息
    @Override
    public Map<Long, List<AccountingInfoSourceDTO>> getAccountingInfoSource(List<Long> customerServicePeriodMonthIds) {
        Map<Long, List<AccountingInfoSourceDTO>> result = Maps.newHashMap();

        if (ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
            return result;
        }

        List<AccountingInfoSourceDTO> accountingInfoSourceDTOS = customerServicePeriodMonthMapper.selectAccountingInfoByIds(customerServicePeriodMonthIds);
        return accountingInfoSourceDTOS.stream().collect(Collectors.groupingBy(AccountingInfoSourceDTO::getCustomerServicePeriodMonthId));
    }

    @Override
    public Map<Long, List<AccountingTopInfoSourceDTO>> getAccountingTopInfoSource(List<Long> customerServicePeriodMonthIds) {
        Map<Long, List<AccountingTopInfoSourceDTO>> result = Maps.newHashMap();

        if (ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
            return result;
        }

        List<AccountingTopInfoSourceDTO> accountingTopInfoSourceDTOS = customerServicePeriodMonthMapper.selectAccountingTopInfoByIds(customerServicePeriodMonthIds);
        return accountingTopInfoSourceDTOS.stream().collect(Collectors.groupingBy(AccountingTopInfoSourceDTO::getCustomerServicePeriodMonthId));
    }

    public Map<Long, List<AdvisorInfoSourceDTO>> getAdvisorInfoSource(List<Long> customerServicePeriodMonthIds) {
        Map<Long, List<AdvisorInfoSourceDTO>> result = Maps.newHashMap();

        if (ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
            return result;
        }

        List<AdvisorInfoSourceDTO> advisorInfoSourceDTOS = customerServicePeriodMonthMapper.selectAdvisorInfoByIds(customerServicePeriodMonthIds);
        return advisorInfoSourceDTOS.stream().collect(Collectors.groupingBy(AdvisorInfoSourceDTO::getCustomerServicePeriodMonthId));
    }

    @Override
    public Map<Long, List<CustomerServiceDocHandover>> getMapByPeriodMonthIds(List<Long> customerServicePeriodMonthIds) {
        List<CustomerServiceDocHandover> customerServiceDocHandovers = customerServiceDocHandoverMapper.selectList(
                new LambdaQueryWrapper<CustomerServiceDocHandover>()
                        .in(CustomerServiceDocHandover::getCustomerServicePeriodMonthId, customerServicePeriodMonthIds)
        );

        if (ObjectUtils.isEmpty(customerServiceDocHandovers)) {
            return Maps.newHashMap();
        }

        return customerServiceDocHandovers
                .stream()
                .collect(Collectors.groupingBy(CustomerServiceDocHandover::getCustomerServicePeriodMonthId));
    }

    @Override
    public List<DocHandoverInstrumentBankDTO> handleInitBankInstrument(Long customerServiceId, Integer period) {
        List<DocHandoverInstrumentBankDTO> bankInstruments = Lists.newArrayList();

        //获取银行
        List<CustomerServiceBankAccount> customerServiceBankAccounts = iCustomerServiceBankAccountService.selectByCustomerServiceIdAndPeriod(
                customerServiceId,
                period
        );

        if (!ObjectUtils.isEmpty(customerServiceBankAccounts)) {
            bankInstruments.addAll(customerServiceBankAccounts.stream()
                    .map(row -> DocHandoverInstrumentBankDTO.builder()
                            .backTicketContents(handleTicketContent(null))
                            .backTicketFiles(Lists.newArrayList())
                            .bankName(row.getBankName())
                            .checkTicketContents(handleTicketContent(null))
                            .checkTicketFiles(Lists.newArrayList())
                            .has(YesNo.YES.getCode())
                            .hasBackTicket(YesNo.YES.getCode())
                            .hasCheckTicket(YesNo.YES.getCode())
                            .hasPayment(YesNo.YES.getCode())
                            .build())
                    .collect(Collectors.toList())
            );
        }

        return bankInstruments;
    }

    @Override
    public DocHandoverWorkBenchDTO docHandoverStatistic(Long deptId) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException(false);
        if (Objects.isNull(userDeptDTO)) {
            return new DocHandoverWorkBenchDTO();
        }
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return new DocHandoverWorkBenchDTO();
        }
        List<CustomerServiceDocHandover> docHandovers = list(new LambdaQueryWrapper<CustomerServiceDocHandover>()
                .eq(CustomerServiceDocHandover::getIsDel, false)
                .eq(CustomerServiceDocHandover::getIsEffect, true)
                .in(!ObjectUtils.isEmpty(userDeptDTO.getDeptIds()), CustomerServiceDocHandover::getSubmitEmployeeDeptId, userDeptDTO.getDeptIds())
                .select(CustomerServiceDocHandover::getId, CustomerServiceDocHandover::getStatus));
        return DocHandoverWorkBenchDTO.builder()
                .waitCheckCount(docHandovers.stream().filter(row -> Objects.equals(row.getStatus(), DocHandoverStatus.NEED_VERIFICATION.getCode())).count())
                .needSubmitCount(docHandovers.stream().filter(row -> Objects.equals(row.getStatus(), DocHandoverStatus.NEED_SUBMIT.getCode())).count())
                .needReSubmitCount(docHandovers.stream().filter(row -> Objects.equals(row.getStatus(), DocHandoverStatus.NEED_RE_SUBMIT.getCode())).count())
                .needPerfectCount(docHandovers.stream().filter(row -> Objects.equals(row.getStatus(), DocHandoverStatus.NEED_PERFECT.getCode())).count())
                .build();
    }

    @Override
    public OperateUserInfoDTO getOperateUserInfo(Long deptId, Long userId) {
        //获取操作人员信息
        //Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();

        return OperateUserInfoDTO.builder()
                .userId(userId)

                .deptId(deptId)
                .topDeptId(sysDept.getParentId())
                .sysDept(sysDept)

                .employeeId(employeeId)
                .employees(employees)

                .operName(operName)

                .build();
    }

    @Override
    public CommonIdsSearchVO deptSearch(Long queryDeptId) {
        boolean needSearch = true;//是否需要搜索
        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
        List<Long> ids = null;

        if (Objects.isNull(queryDeptId)) {
            needSearch = false;
        } else {
            List<Long> queryDeptIds = remoteDeptService.getAllChildrenIdByTopDeptId(queryDeptId).getDataThrowException();
            if (ObjectUtils.isEmpty(queryDeptIds)) {
                fail = true;
            } else {
                ids = customerServicePeriodMonthMapper.searchByDeptIds(queryDeptIds);

                if (ObjectUtils.isEmpty(ids)) {
                    fail = true;
                }
            }
        }

        return CommonIdsSearchVO.builder()
                .needSearch(needSearch)
                .fail(fail)
                .ids(ids)
                .build();
    }

    @Override
    public void updateBankInstrumentUnHasCount() {
        List<CustomerServiceDocHandover> all = list();

        if (!ObjectUtils.isEmpty(all)) {
            List<CustomerServiceDocHandover> updateEntryList = Lists.newArrayList();

            for (CustomerServiceDocHandover row : all) {
                if (row.getBankInstrumentCount() > 0) {
                    List<CustomerServiceDocHandoverBankInstrument> allBanks = iCustomerServiceDocHandoverBankInstrumentService.list(
                            new LambdaQueryWrapper<CustomerServiceDocHandoverBankInstrument>()
                                    .in(CustomerServiceDocHandoverBankInstrument::getCustomerServiceDocId, row.getId())
                    );

                    CustomerServiceDocHandover updateEntry = new CustomerServiceDocHandover();
                    updateEntry.setId(row.getId());
                    updateEntry.setBankInstrumentUnHasCount(
                            (int) allBanks.stream().filter(r -> Objects.equals(r.getHas(), YesNo.NO.getCode())).count()
                    );

                    updateEntryList.add(
                            updateEntry
                    );
                }
            }

            updateBatchById(updateEntryList);
        }
    }

    //处理会计圈文案
    public static String handleAdvisorEmployeeNameFull(List<AdvisorInfoSourceDTO> source) {
        String result = "";

        if (ObjectUtils.isEmpty(source)) {
            return result;
        }

        String advisorDeptName = source.get(0).getAdvisorDeptName();
        String advisorEmployeeNameStr = source.stream().map(AdvisorInfoSourceDTO::getAdvisorEmployeeName).collect(Collectors.joining(","));
        result = advisorDeptName + "（" + advisorEmployeeNameStr + "）";

        return result;
    }

    //处理会计圈文案
    public static String handleAccountingEmployeeNameFull(List<AccountingInfoSourceDTO> source) {
        log.info("handleAccountingEmployeeNameFull source={}", new Gson().toJson(source));
        String result = null;

        if (!ObjectUtils.isEmpty(source)) {
            String accountingDeptName = source.get(0).getAccountingDeptName();

            if (!StringUtils.isEmpty(accountingDeptName) || source.stream().anyMatch(r -> !StringUtils.isEmpty(r.getAccountingEmployeeName()))) {
                String accountingEmployeeNameStr = source.stream()
                        .filter(r -> !StringUtils.isEmpty(r.getAccountingEmployeeName()))
                        .map(AccountingInfoSourceDTO::getAccountingEmployeeName)
                        .collect(Collectors.joining(","));
                log.info("handleAccountingEmployeeNameFull accountingDeptName={}, accountingEmployeeNameStr={}", accountingDeptName, accountingEmployeeNameStr);

                result = (
                        StringUtils.isEmpty(accountingDeptName) ? "" : accountingDeptName
                )
                        + (
                        StringUtils.isEmpty(accountingEmployeeNameStr) ? "" : ("（" + accountingEmployeeNameStr + "）"));
            }
        }

        return result;
    }

    public static void main(String[] args) {
        AccountingInfoSourceDTO accountingInfoSourceDTO = AccountingInfoSourceDTO.builder().build();
        AccountingInfoSourceDTO accountingInfoSourceDTO2 = AccountingInfoSourceDTO.builder().build();
        List<AccountingInfoSourceDTO> source = Lists.newArrayList();
        source.add(accountingInfoSourceDTO);
        source.add(accountingInfoSourceDTO2);
        String accountingEmployeeNameStr = source.stream()
                .filter(r -> !StringUtils.isEmpty(r.getAccountingEmployeeName()))
                .map(AccountingInfoSourceDTO::getAccountingEmployeeName).collect(Collectors.joining(","));
        System.out.println("accountingEmployeeNameStr: " + accountingEmployeeNameStr);
        System.out.println("accountingEmployeeNameStr: " + StringUtils.isEmpty(accountingEmployeeNameStr));
    }

    //处理基础信息结果
    private DocHandoverBaseDTO getDocHandoverBaseResult(CustomerServiceDocHandover customerServiceDocHandover) {
        if (customerServiceDocHandover == null) {
            return null;
        }

        List<CustomerServiceDocHandoverFile> files = iCustomerServiceDocHandoverFileService.selectByDocHandover(customerServiceDocHandover.getId(), Lists.newArrayList(DocHandoverFileType.BASE));

        return DocHandoverBaseDTO.builder()
                .id(customerServiceDocHandover.getId())
                .customerName(customerServiceDocHandover.getCustomerName())
                .customerServiceId(customerServiceDocHandover.getCustomerServiceId())
                .customerServicePeriodMonthId(customerServiceDocHandover.getCustomerServicePeriodMonthId())
                .files(iCustomerServiceDocHandoverFileService.covToCommonFileVO(files))
                .isVoucherEntry(customerServiceDocHandover.getIsVoucherEntry())
                .period(customerServiceDocHandover.getPeriod())
                .remark(customerServiceDocHandover.getRemark())
                .build();
    }

    //处理票据信息结果
    private DocHandoverInstrumentDTO getDocHandoverInstrumentResult(CustomerServiceDocHandover customerServiceDocHandover) {
        Long id = customerServiceDocHandover.getId();

        //获取所有附件
        Map<String, List<CustomerServiceDocHandoverFile>> filesMap = iCustomerServiceDocHandoverFileService.selectMapSubKeyByDocHandover(id, DocHandoverFileType.exceptBase());

        //获取关联的银行
        List<CustomerServiceDocHandoverBankInstrument> customerServiceDocHandoverBankInstruments = iCustomerServiceDocHandoverBankInstrumentService.list(
                new LambdaQueryWrapper<CustomerServiceDocHandoverBankInstrument>()
                        .eq(CustomerServiceDocHandoverBankInstrument::getCustomerServiceDocId, id)
        );

        //获取关联的税票、其他票,  发票业务：1-其他票据/2-税号发票
        List<CustomerServiceDocHandoverTaxInstrument> customerServiceDocHandoverTaxInstruments = iCustomerServiceDocHandoverTaxInstrumentService.list(
                new LambdaQueryWrapper<CustomerServiceDocHandoverTaxInstrument>()
                        .eq(CustomerServiceDocHandoverTaxInstrument::getCustomerServiceDocId, id)
        );
        Map<Integer, List<CustomerServiceDocHandoverTaxInstrument>> customerServiceDocHandoverTaxInstrumentsMap = customerServiceDocHandoverTaxInstruments.stream()
                .collect(Collectors.groupingBy(CustomerServiceDocHandoverTaxInstrument::getBizType));

        //处理银行票据
        List<DocHandoverInstrumentBankDTO> bankInstruments;
        /*List<String> alreadyBanks = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(customerServiceDocHandoverBankInstruments)) {
            for (CustomerServiceDocHandoverBankInstrument row : customerServiceDocHandoverBankInstruments) {
                alreadyBanks.add(row.getBankName());
                bankInstruments.add(
                        DocHandoverInstrumentBankDTO.builder()
                                .backTicketContents(handleTicketContent(row.getBackTicketContent()))
                                .backTicketFiles(
                                        iCustomerServiceDocHandoverFileService.covToCommonFileVO(
                                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.BANK_BACK_TICKET.getCode(), row.getBankName()))
                                        )
                                )
                                .bankName(row.getBankName())
                                .checkTicketContents(handleTicketContent(row.getCheckTicketContent()))
                                .checkTicketFiles(
                                        iCustomerServiceDocHandoverFileService.covToCommonFileVO(
                                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.BANK_CHECK_TICKET.getCode(), row.getBankName()))
                                        )
                                )
                                .has(row.getHas())
                                .hasBackTicket(row.getHasBackTicket())
                                .hasCheckTicket(row.getHasCheckTicket())
                                .hasPayment(row.getHasPayment())
                                .build()
                );
            }
        }
        if (!ObjectUtils.isEmpty(customerServiceBankAccounts)) {
            bankInstruments.addAll(customerServiceBankAccounts.stream()
                    .filter(row -> !alreadyBanks.contains(row.getBankName()))
                    .map(row -> DocHandoverInstrumentBankDTO.builder()
                            .backTicketContents(handleTicketContent(null))
                            .backTicketFiles(Lists.newArrayList())
                            .bankName(row.getBankName())
                            .checkTicketContents(handleTicketContent(null))
                            .checkTicketFiles(Lists.newArrayList())
                            .has(YesNo.YES.getCode())
                            .hasBackTicket(YesNo.YES.getCode())
                            .hasCheckTicket(YesNo.YES.getCode())
                            .hasPayment(YesNo.YES.getCode())
                            .build())
                    .collect(Collectors.toList())
            );
        }*/
        if (customerServiceDocHandover.getBankInstrumentCount() == null) {
            //给 默认数据
            bankInstruments = handleInitBankInstrument(customerServiceDocHandover.getCustomerServiceId(), customerServiceDocHandover.getPeriod());
        } else {
            //给 之前用户提交的数据
            bankInstruments = handleHisBankInstrument(customerServiceDocHandoverBankInstruments, filesMap);
        }

        //处理 税号发票
        List<DocHandoverInstrumentTaxItemDTO> taxInstruments = Lists.newArrayList();
        Map<String, CustomerServiceDocHandoverTaxInstrument> alreadyDocHandoverTaxInstrumentMap = customerServiceDocHandoverTaxInstrumentsMap.getOrDefault(2, Lists.newArrayList())
                .stream().collect(Collectors.toMap(CustomerServiceDocHandoverTaxInstrument::getName, row -> row));
        for (DocHandoverTaxInstrumentType row : DocHandoverTaxInstrumentType.values()) {
            CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument = alreadyDocHandoverTaxInstrumentMap.get(row.name());
            DocHandoverInstrumentTaxItemDTO docHandoverInstrumentTaxItemDTO;
            if (customerServiceDocHandoverTaxInstrument == null) {
                docHandoverInstrumentTaxItemDTO = DocHandoverInstrumentTaxItemDTO.builder()
                        .name(row.name())
                        .paperCount(null)
                        .remark(null)
                        .files(null)
                        .build();
            } else {
                docHandoverInstrumentTaxItemDTO = DocHandoverInstrumentTaxItemDTO.builder()
                        .name(customerServiceDocHandoverTaxInstrument.getName())
                        .paperCount(customerServiceDocHandoverTaxInstrument.getPaperCount())
                        .remark(customerServiceDocHandoverTaxInstrument.getRemark())
                        .files(iCustomerServiceDocHandoverFileService.covToCommonFileVO(
                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.TAX_TICKET.getCode(), String.valueOf(customerServiceDocHandoverTaxInstrument.getRowNum())))
                                )
                        )
                        .build();
            }
            taxInstruments.add(docHandoverInstrumentTaxItemDTO);
        }

        //处理 其他票据
        List<DocHandoverInstrumentTaxItemDTO> otherInstruments = customerServiceDocHandoverTaxInstrumentsMap.getOrDefault(1, Lists.newArrayList())
                .stream()
                .map(row -> DocHandoverInstrumentTaxItemDTO.builder()
                        .name(row.getName())
                        .paperCount(row.getPaperCount())
                        .remark(row.getRemark())
                        .files(iCustomerServiceDocHandoverFileService.covToCommonFileVO(
                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.OTHER_TICKET.getCode(), String.valueOf(row.getRowNum())))
                                )
                        )
                        .build())
                .collect(Collectors.toList());

        return DocHandoverInstrumentDTO.builder()
                .id(id)

                .title(customerServiceDocHandover.getTitle())
                .period(customerServiceDocHandover.getPeriod())

                .bankInstruments(bankInstruments)
                .otherInstruments(otherInstruments)
                .taxInstruments(taxInstruments)

                .hasOtherTicket(customerServiceDocHandover.getHasOtherTicket() == null ? YesNo.YES.getCode() : customerServiceDocHandover.getHasOtherTicket())
                .hasTaxTicket(customerServiceDocHandover.getHasTaxTicket() == null ? YesNo.YES.getCode() : customerServiceDocHandover.getHasTaxTicket())

                .build();
    }

    private List<DocHandoverInstrumentBankDTO> handleHisBankInstrument(List<CustomerServiceDocHandoverBankInstrument> source, Map<String, List<CustomerServiceDocHandoverFile>> filesMap) {
        List<DocHandoverInstrumentBankDTO> bankInstruments = Lists.newArrayList();

        if (!ObjectUtils.isEmpty(source)) {
            for (CustomerServiceDocHandoverBankInstrument row : source) {
                bankInstruments.add(
                        DocHandoverInstrumentBankDTO.builder()
                                .backTicketContents(handleTicketContent(row.getBackTicketContent()))
                                .backTicketFiles(
                                        iCustomerServiceDocHandoverFileService.covToCommonFileVO(
                                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.BANK_BACK_TICKET.getCode(), row.getBankName()))
                                        )
                                )
                                .bankName(row.getBankName())
                                .checkTicketContents(handleTicketContent(row.getCheckTicketContent()))
                                .checkTicketFiles(
                                        iCustomerServiceDocHandoverFileService.covToCommonFileVO(
                                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.BANK_CHECK_TICKET.getCode(), row.getBankName()))
                                        )
                                )
                                .has(row.getHas())
                                .hasBackTicket(row.getHasBackTicket())
                                .hasCheckTicket(row.getHasCheckTicket())
                                .hasPayment(row.getHasPayment())
                                .build()
                );
            }
        }

        return bankInstruments;
    }

    //处理材质介质
    public static List<Integer> handleTicketContent(Integer source) {
        if (source == null) {
            return Lists.newArrayList();
        }

        return TicketContent.getByCode(source).getActualCodes();
    }

    //处理材质介质
    public static Integer handleTicketContentToDb(List<Integer> source) {
        Integer result = null;

        if (ObjectUtils.isEmpty(source)) {
            return result;
        } else {
            if (source.contains(TicketContent.BANK.getCode()) && source.contains(TicketContent.PAPER.getCode())) {
                result = TicketContent.ALL.getCode();
            } else if (source.contains(TicketContent.BANK.getCode())) {
                result = TicketContent.BANK.getCode();
            } else if (source.contains(TicketContent.PAPER.getCode())) {
                result = TicketContent.PAPER.getCode();
            }
        }

        return result;
    }

    //计算保存、提交的状态
    private Integer getStatus(Boolean isSubmit, Boolean checkCanSubmitFlag) {
        Integer status;

        if (isSubmit) {
            //提交

            if (checkCanSubmitFlag) {
                //通过
                status = DocHandoverStatus.NEED_VERIFICATION.getCode();
            } else {
                throw new ServiceException("不符合可提交条件，不可提交");
            }
        } else {
            //保存

            if (checkCanSubmitFlag) {
                //通过
                status = DocHandoverStatus.NEED_SUBMIT.getCode();
            } else {
                status = DocHandoverStatus.NEED_PERFECT.getCode();
            }
        }

        return status;
    }

    //删除原来的票据数据
    private void deleteInstrument(Long id) {
        //删除所有附件，除了base
        iCustomerServiceDocHandoverFileService.deleteByDocHandoverId(id, DocHandoverFileType.exceptBase());

        //删除关联的银行票据
        iCustomerServiceDocHandoverBankInstrumentService.remove(
                new LambdaQueryWrapper<CustomerServiceDocHandoverBankInstrument>()
                        .eq(CustomerServiceDocHandoverBankInstrument::getCustomerServiceDocId, id)
        );

        //删除关联的税票、其他票,  发票业务：1-其他票据/2-税号发票
        iCustomerServiceDocHandoverTaxInstrumentService.remove(
                new LambdaQueryWrapper<CustomerServiceDocHandoverTaxInstrument>()
                        .eq(CustomerServiceDocHandoverTaxInstrument::getCustomerServiceDocId, id)
        );
    }

    //批量存银行票据和他的附件
    private void saveBankInstruments(Long id, List<DocHandoverInstrumentBankDTO> bankInstruments) {
        if (!ObjectUtils.isEmpty(bankInstruments)) {
            List<CustomerServiceDocHandoverBankInstrument> saveCustomerServiceDocHandoverBankInstruments = Lists.newArrayList();
            List<CustomerServiceDocHandoverFile> saveCustomerServiceDocHandoverFiles = Lists.newArrayList();

            for (DocHandoverInstrumentBankDTO row : bankInstruments) {
                saveCustomerServiceDocHandoverBankInstruments.add(
                        new CustomerServiceDocHandoverBankInstrument()
                                .setBackTicketContent(handleTicketContentToDb(row.getBackTicketContents()))
                                .setHas(row.getHas())
                                .setBankName(row.getBankName())
                                .setCheckTicketContent(handleTicketContentToDb(row.getCheckTicketContents()))
                                .setCustomerServiceDocId(id)
                                .setHasBackTicket(row.getHasBackTicket())
                                .setHasCheckTicket(row.getHasCheckTicket())
                                .setHasPayment(row.getHasPayment())
                );

                if (!ObjectUtils.isEmpty(row.getCheckTicketFiles())) {
                    saveCustomerServiceDocHandoverFiles.addAll(
                            row.getCheckTicketFiles()
                                    .stream()
                                    .map(f -> new CustomerServiceDocHandoverFile()
                                            .setCustomerServiceDocId(id)
                                            .setFileName(f.getFileName())
                                            .setFileType(DocHandoverFileType.BANK_CHECK_TICKET.getCode())
                                            .setSubFileType(row.getBankName())
                                            .setFileUrl(f.getFileUrl()))
                                    .collect(Collectors.toList())
                    );
                }

                if (!ObjectUtils.isEmpty(row.getBackTicketFiles())) {
                    saveCustomerServiceDocHandoverFiles.addAll(
                            row.getBackTicketFiles()
                                    .stream()
                                    .map(f -> new CustomerServiceDocHandoverFile()
                                            .setCustomerServiceDocId(id)
                                            .setFileName(f.getFileName())
                                            .setFileType(DocHandoverFileType.BANK_BACK_TICKET.getCode())
                                            .setSubFileType(row.getBankName())
                                            .setFileUrl(f.getFileUrl()))
                                    .collect(Collectors.toList())
                    );
                }
            }

            //银行票据 主体信息
            if (!ObjectUtils.isEmpty(saveCustomerServiceDocHandoverBankInstruments)) {
                iCustomerServiceDocHandoverBankInstrumentService.saveBatch(saveCustomerServiceDocHandoverBankInstruments);
            }

            //银行票据的附件
            if (!ObjectUtils.isEmpty(saveCustomerServiceDocHandoverFiles)) {
                iCustomerServiceDocHandoverFileService.saveBatch(saveCustomerServiceDocHandoverFiles);
            }
        }
    }

    //铺量存税票、其他票据
    private void saveTaxInstruments(Long id, List<DocHandoverInstrumentTaxItemDTO> taxInstruments, List<DocHandoverInstrumentTaxItemDTO> otherInstruments) {
        List<CustomerServiceDocHandoverTaxInstrument> saveCustomerServiceDocHandoverTaxInstrument = Lists.newArrayList();
        List<CustomerServiceDocHandoverFile> saveCustomerServiceDocHandoverFiles = Lists.newArrayList();

        //存税票
        if (!ObjectUtils.isEmpty(taxInstruments)) {
            for (int i = 0; i < taxInstruments.size(); i++) {
                DocHandoverInstrumentTaxItemDTO row = taxInstruments.get(i);
                Integer rowNum = i + 1;
                String rowNumStr = String.valueOf(rowNum);

                saveCustomerServiceDocHandoverTaxInstrument.add(
                        new CustomerServiceDocHandoverTaxInstrument()
                                .setBizType(2)
                                .setCustomerServiceDocId(id)
                                .setName(row.getName())
                                .setPaperCount(row.getPaperCount())
                                .setRemark(row.getRemark())
                                .setRowNum(rowNum)
                );

                if (!ObjectUtils.isEmpty(row.getFiles())) {
                    for (CommonFileVO f : row.getFiles()) {
                        saveCustomerServiceDocHandoverFiles.add(
                                new CustomerServiceDocHandoverFile()
                                        .setCustomerServiceDocId(id)
                                        .setFileName(f.getFileName())
                                        .setFileType(DocHandoverFileType.TAX_TICKET.getCode())
                                        .setSubFileType(rowNumStr)
                                        .setFileUrl(f.getFileUrl())
                        );
                    }
                }
            }
        }

        if (!ObjectUtils.isEmpty(otherInstruments)) {
            for (int i = 0; i < otherInstruments.size(); i++) {
                DocHandoverInstrumentTaxItemDTO row = otherInstruments.get(i);
                Integer rowNum = i + 1;
                String rowNumStr = String.valueOf(rowNum);

                saveCustomerServiceDocHandoverTaxInstrument.add(
                        new CustomerServiceDocHandoverTaxInstrument()
                                .setBizType(1)
                                .setCustomerServiceDocId(id)
                                .setName(row.getName())
                                .setPaperCount(row.getPaperCount())
                                .setRemark(row.getRemark())
                                .setRowNum(rowNum)
                );

                if (!ObjectUtils.isEmpty(row.getFiles())) {
                    for (CommonFileVO f : row.getFiles()) {
                        saveCustomerServiceDocHandoverFiles.add(
                                new CustomerServiceDocHandoverFile()
                                        .setCustomerServiceDocId(id)
                                        .setFileName(f.getFileName())
                                        .setFileType(DocHandoverFileType.OTHER_TICKET.getCode())
                                        .setSubFileType(rowNumStr)
                                        .setFileUrl(f.getFileUrl())
                        );
                    }
                }
            }
        }

        //银行票据 主体信息
        if (!ObjectUtils.isEmpty(saveCustomerServiceDocHandoverTaxInstrument)) {
            iCustomerServiceDocHandoverTaxInstrumentService.saveBatch(saveCustomerServiceDocHandoverTaxInstrument);
        }

        //银行票据的附件
        if (!ObjectUtils.isEmpty(saveCustomerServiceDocHandoverFiles)) {
            iCustomerServiceDocHandoverFileService.saveBatch(saveCustomerServiceDocHandoverFiles);
        }
    }

    private static List<DocHandoverSimpleDTO> covDocHandoverSimpleDTO(List<CustomerServiceDocHandover> customerServiceDocHandovers) {
        List<DocHandoverSimpleDTO> result = Lists.newArrayList();

        if (ObjectUtils.isEmpty(customerServiceDocHandovers)) {
            return result;
        }

        customerServiceDocHandovers.sort(Comparator.comparing(CustomerServiceDocHandover::getPeriod));

        Map<Integer, List<CustomerServiceDocHandover>> sourceMap = customerServiceDocHandovers.stream().collect(Collectors.groupingBy(CustomerServiceDocHandover::getPeriod));
        List<Integer> keys = Lists.newArrayList(sourceMap.keySet()).stream().sorted(Integer::compareTo).collect(Collectors.toList());

        for (Integer period : keys) {
            if (sourceMap.get(period).size() == 1) {
                CustomerServiceDocHandover row = sourceMap.get(period).get(0);

                result.add(
                        DocHandoverSimpleDTO.builder()
                                .batchNum(row.getBatchNum())
                                .id(row.getId())
                                .title(row.getTitle())
                                .titleFull(row.getTitle())
                                .build()
                );
            } else {
                for (CustomerServiceDocHandover row : sourceMap.get(period)) {
                    result.add(
                            DocHandoverSimpleDTO.builder()
                                    .batchNum(row.getBatchNum())
                                    .id(row.getId())
                                    .title(row.getTitle())
                                    .titleFull(row.getTitle() + "-" + row.getBatchNum())
                                    .build()
                    );
                }
            }
        }

        return result;
    }

    //判断材料交接单的 所有材料都是空
    public static Boolean checkAllContentUnHas(CustomerServiceDocHandover row) {
        boolean result = false;

        if (row != null) {
            boolean bankFlag = (row.getBankInstrumentCount() == null || row.getBankInstrumentCount() == 0)
                    || Objects.equals(row.getBankInstrumentUnHasCount(), row.getBankInstrumentCount());

            result = bankFlag && (row.getHasTaxTicket() == null || row.getHasTaxTicket() == 0) && (row.getHasOtherTicket() == null || row.getHasOtherTicket() == 0);
        }

        return result;
    }
}
