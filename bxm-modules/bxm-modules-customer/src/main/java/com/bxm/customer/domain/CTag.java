package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 标签对象 c_tag
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("标签对象")
@Accessors(chain = true)
@TableName("c_tag")
public class CTag extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 标签id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "标签id")
    @ApiModelProperty(value = "标签id")
    private Long id;

    /** 标签名称 */
    @Excel(name = "标签名称")
    @TableField("tag_name")
    @ApiModelProperty(value = "标签名称")
    private String tagName;

    /** 是否有变量 */
    @Excel(name = "是否有变量")
    @TableField("has_param")
    @ApiModelProperty(value = "是否有变量")
    private Boolean hasParam;

    /** 标签类型，1-系统标签，2-集团标签 */
    @Excel(name = "标签类型，1-系统标签，2-集团标签")
    @TableField("tag_type")
    @ApiModelProperty(value = "标签类型，1-系统标签，2-集团标签")
    private Integer tagType;

    /** 是否自定义标签，0-否，1-是 */
    @Excel(name = "是否自定义标签，0-否，1-是")
    @TableField("is_customize")
    @ApiModelProperty(value = "是否自定义标签，0-否，1-是")
    private Boolean isCustomize;

    /** 归属集团id */
    @Excel(name = "归属集团id")
    @TableField("top_dept_id")
    @ApiModelProperty(value = "归属集团id")
    private Long topDeptId;

    /** 状态，0-禁用，1-启用 */
    @Excel(name = "状态，0-禁用，1-启用")
    @TableField("status")
    @ApiModelProperty(value = "状态，0-禁用，1-启用")
    private Integer status;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

}
