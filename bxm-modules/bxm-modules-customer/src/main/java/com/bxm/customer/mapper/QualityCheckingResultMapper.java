package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.QualityCheckingResult;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionMiniListDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionStatisticDTO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultVO;
import com.bxm.customer.domain.vo.workOrder.QualityExceptionMiniListSearchVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 质检结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Mapper
public interface QualityCheckingResultMapper extends BaseMapper<QualityCheckingResult>
{
    /**
     * 查询质检结果
     * 
     * @param id 质检结果主键
     * @return 质检结果
     */
    public QualityCheckingResult selectQualityCheckingResultById(Long id);

    /**
     * 查询质检结果列表
     * 
     * @param qualityCheckingResult 质检结果
     * @return 质检结果集合
     */
    public List<QualityCheckingResult> selectQualityCheckingResultList(QualityCheckingResult qualityCheckingResult);

    /**
     * 新增质检结果
     * 
     * @param qualityCheckingResult 质检结果
     * @return 结果
     */
    public int insertQualityCheckingResult(QualityCheckingResult qualityCheckingResult);

    /**
     * 修改质检结果
     * 
     * @param qualityCheckingResult 质检结果
     * @return 结果
     */
    public int updateQualityCheckingResult(QualityCheckingResult qualityCheckingResult);

    /**
     * 删除质检结果
     * 
     * @param id 质检结果主键
     * @return 结果
     */
    public int deleteQualityCheckingResultById(Long id);

    /**
     * 批量删除质检结果
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQualityCheckingResultByIds(Long[] ids);

    List<QualityExceptionStatisticDTO> selectExceptionStatistic(@Param("userDept") UserDeptDTO userDept,
                                                                @Param("queryDeptIds") List<Long> queryDeptIds);

    List<CommonDeptCountDTO> qualityCheckingExceptionAdvisorDeptList(@Param("userDept") UserDeptDTO userDeptDTO,
                                                                     @Param("qualityCheckingType") Integer qualityCheckingType,
                                                                     @Param("queryDeptIds") List<Long> queryDeptIds);

    List<CommonDeptCountDTO> qualityCheckingExceptionAccountingDeptList(@Param("userDept") UserDeptDTO userDeptDTO,
                                                                        @Param("qualityCheckingType") Integer qualityCheckingType,
                                                                        @Param("queryDeptIds") List<Long> queryDeptIds);

    List<QualityExceptionMiniListDTO> selectExceptionMiniList(IPage<QualityExceptionMiniListDTO> result,
                                                              @Param("vo") QualityExceptionMiniListSearchVO vo,
                                                              @Param("userDept") UserDeptDTO userDept,
                                                              @Param("queryDeptIds") List<Long> queryDeptIds,
                                                              @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds);

    List<CommonDeptCountDTO> qualityCheckingResultAdvisorDeptList(@Param("userDept") UserDeptDTO userDeptDTO, @Param("vo") QualityCheckingResultVO vo);

    List<CommonDeptCountDTO> qualityCheckingResultAccountingDeptList(@Param("userDept") UserDeptDTO userDeptDTO, @Param("vo") QualityCheckingResultVO vo);

    List<QualityCheckingResultDTO> qualityCheckResultListByIds(@Param("ids") List<Long> ids);

    List<QualityCheckingResultDTO> qualityCheckResultPageList(IPage<QualityCheckingResultDTO> result,
                                                              @Param("vo") QualityCheckingResultVO vo,
                                                              @Param("userDept") UserDeptDTO userDept,
                                                              @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds);

    void createNewPeriodQualityCheckingResult(@Param("period") Integer period);

    void createNewPeriodQualityCheckingOperationRecord(@Param("period") Integer period);
}
