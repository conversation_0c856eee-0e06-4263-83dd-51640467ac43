package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.customer.api.domain.dto.*;
import com.bxm.customer.service.StatisticService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

@RestController
@RequestMapping("/statistic")
@Api(tags = "大屏数据统计相关")
public class StatisticController {

    @Autowired
    private StatisticService statisticService;

    @GetMapping("/getDeliverAtfStatistic")
    @ApiIgnore
    @InnerAuth
    public Result<DeliverAtfStatisticDTO> getDeliverAtfStatistic() {
        return Result.ok(statisticService.getDeliverAtfStatistic());
    }

    @GetMapping("/getDeliverProgressStatistic")
    @ApiIgnore
    @InnerAuth
    public Result<DeliverProgressStatisticDTO> getDeliverProgressStatistic() {
        return Result.ok(statisticService.getDeliverProgressStatistic());
    }

    @GetMapping("/getAccountingCashierProgressStatistic")
    @ApiIgnore
    @InnerAuth
    public Result<AccountingCashierProgressStatisticDTO> getAccountingCashierProgressStatistic() {
        return Result.ok(statisticService.getAccountingCashierProgressStatistic());
    }

    @GetMapping("/getAccountingCashierAtfStatistic")
    @ApiIgnore
    @InnerAuth
    public Result<AccountingCashierAtfStatisticDTO> getAccountingCashierAtfStatistic() {
        return Result.ok(statisticService.getAccountingCashierAtfStatistic());
    }

    @GetMapping("/getAccountingCashierMonthProgressStatistic")
    @ApiIgnore
    @InnerAuth
    public Result<Map<String, AccountingCashierMonthProgressDTO>> getAccountingCashierMonthProgressStatistic() {
        return Result.ok(statisticService.getAccountingCashierMonthProgressStatistic());
    }

    @GetMapping("/getAtfStatistic")
    @ApiIgnore
    @InnerAuth
    public Result<AtfStatisticDTO> getAtfStatistic() {
        return Result.ok(statisticService.getAtfStatistic());
    }
}
