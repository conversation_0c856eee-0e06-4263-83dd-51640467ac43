package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverResult;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverStatus;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.api.domain.dto.RemoteAccountingCashierSimpleDTO;
import com.bxm.customer.api.domain.vo.RemoteAccountingCashierPeriodTypeVO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDetailDTO;
import com.bxm.customer.domain.dto.businessTask.BizIdBankAccountDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialRepeatDTO;
import com.bxm.customer.domain.vo.CommonInAccountVO;
import com.bxm.customer.domain.vo.CommonNoticeVO;
import com.bxm.customer.domain.vo.InAccountAutoSettlementVO;
import com.bxm.customer.domain.vo.accoutingCashier.*;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.thirdpart.api.domain.BanksEnterprisesExtractDTO;
import com.bxm.thirdpart.api.domain.CheckFilesDTO;
import com.bxm.thirdpart.api.domain.CheckFilesVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客户账务Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
public interface ICustomerServiceCashierAccountingService extends IService<CustomerServiceCashierAccounting>
{
    /**
     * 查询客户账务
     * 
     * @param id 客户账务主键
     * @return 客户账务
     */
    public CustomerServiceCashierAccounting selectCustomerServiceCashierAccountingById(Long id);

    /**
     * 查询客户账务列表
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 客户账务集合
     */
    public List<CustomerServiceCashierAccounting> selectCustomerServiceCashierAccountingList(CustomerServiceCashierAccounting customerServiceCashierAccounting);

    /**
     * 新增客户账务
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 结果
     */
    public int insertCustomerServiceCashierAccounting(CustomerServiceCashierAccounting customerServiceCashierAccounting);

    /**
     * 修改客户账务
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 结果
     */
    public int updateCustomerServiceCashierAccounting(CustomerServiceCashierAccounting customerServiceCashierAccounting);

    /**
     * 批量删除客户账务
     * 
     * @param ids 需要删除的客户账务主键集合
     * @return 结果
     */
    public int deleteCustomerServiceCashierAccountingByIds(Long[] ids);

    /**
     * 删除客户账务信息
     * 
     * @param id 客户账务主键
     * @return 结果
     */
    public int deleteCustomerServiceCashierAccountingById(Long id);

    IPage<AccountingCashierDTO> accountingCashierList(AccountingCashierSearchVO vo);

    List<CommonFileVO> getAccountingCashierFiles(Long accountingCashierId, Integer fileType);

    List<CommonDeptCountDTO> accountingCashierPeriodAccountingDeptCountList(UserDeptDTO userDeptDTO, Integer type);

    List<CommonDeptCountDTO> accountingCashierCustomerAdvisorDeptCountList(UserDeptDTO userDeptDTO, Integer type);

    List<CommonDeptCountDTO> accountingCashierPeriodAdvisorDeptCountList(UserDeptDTO userDeptDTO, Integer type);

    List<CommonDeptCountDTO> accountingCashierCustomerAccountingDeptCountList(UserDeptDTO userDeptDTO, Integer type);

    AccountingCashierDetailDTO detail(Long accountingCashierId);

    void modifyMattersNotes(AccountingCashierMattersNotesModifyVO vo, Long deptId);

    void createAccountingCashier(AccountingCashierCreateVO vo);

    CustomerServiceCashierAccounting createAccountingCashierByMaterial(AccountingCashierCreateVO vo, OperateUserInfoDTO operateUserInfoDTO);

    void remoteCreateAccountingCashier(AccountingCashierCreateVO vo);

    void modifyAccountingCashier(AccountingCashierModifyVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> submit(AccountingCashierOperateVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> submitV2(AccountingCashierOperateVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> waitSubmitSubmit(AccountingCashierOperateVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> delete(AccountingCashierOperateVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> deliver(AccountingCashierOperateVO vo);

    void remoteDeliver(AccountingCashierOperateVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> dealException(AccountingCashierDealExceptionVO vo);

    void remoteDealException(AccountingCashierDealExceptionVO vo);

    void changeAccountingCashier(AccountingCashierChangeDeliverVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> reBackAccountingCashier(AccountingCashierOperateVO vo);

    void rpaUpdate(AccountingCashierRpaUpdateVO vo);

    void remoteRpaUpdate(AccountingCashierRpaUpdateVO vo);

    void updateProfit(AccountingCashierUpdateProfitVO vo);

    void remoteUpdateProfit(AccountingCashierUpdateProfitVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> supplementFiles(AccountingCashierSupplementFileVO vo);

    void remoteSupplementFiles(AccountingCashierSupplementFileVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> checkFiles(AccountingCashierCheckFileVO vo);

    void remoteCheckFiles(AccountingCashierCheckFileVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> receiveChange(AccountingCashierOperateVO vo);

    TCommonOperateDTO<CustomerServiceCashierAccounting> createBankTask(AccountingCashierBankCreateTaskVO vo);

    void updateByCommonNotice(OpenApiData openApiData, CommonInAccountVO commonInAccountVO, CustomerServiceCashierAccounting customerServiceCashierAccounting);

    void supplementFilesByCommonNotice(OpenApiData openApiData, CommonInAccountVO commonInAccountVO, CustomerServiceCashierAccounting customerServiceCashierAccounting);

    void updateBankPaymentResultSettleAccountStatus(Long customerServicePeriodMonthId, LocalDateTime operTime, String operName, Long deptId, Long userId, String operType);

    void remoteUpdateBankPaymentResultSettleAccountStatus(UpdateBankPaymentResultSettleAccountStatusVO vo);

    void updateBankAccountingCashierStatus(List<Long> ids, Integer deliverStatus, String operName, String operRemark, LocalDateTime operTime, String operType, Long deptId, Long userId);

    void updateBankAccountingCashierStatusBatch(List<CustomerServiceCashierAccounting> cashierAccountings, Integer deliverStatus, String operName, String operRemark, LocalDateTime operTime, String operType, Long deptId, Long userId, Integer materialIntegrity);

    boolean checkInAccountIsEnd(Long customerServiceId, Integer period);

    boolean checkInAccountIsEnd(Long customerServiceId, Integer startPeriod, Integer endPeriod);

    List<AccountingCashierBankSimpleVO> bankPaymentByPeriodId(Long customerServicePeriodMonthId);

    List<AccountingCashierInAccountSimpleVO> inAccountByPeriodId(Long customerServicePeriodMonthId);

    List<MaterialFileSimpleVO> materialFilesByPeriodId(Long customerServicePeriodMonthId, Integer fileType, String bankName, String fileRemark);

    List<String> materialFileBankSelect(Long customerServicePeriodMonthId);

    TCommonOperateDTO<CustomerServiceCashierAccountingFile> deleteMaterialFiles(List<Long> ids, Long deptId);

    List<MaterialFileSimpleErrorVO> getMaterialFilesByIds(List<Long> ids);

    List<CommonDeptCountDTO> accountingCashierMiniListAccountingDeptCountList(UserDeptDTO userDeptDTO, List<Long> queryDeptIds, Integer accountingCashierType, Integer statisticType);

    List<CommonDeptCountDTO> accountingCashierMiniListAdvisorDeptCountList(UserDeptDTO userDeptDTO, List<Long> queryDeptIds, Integer accountingCashierType, Integer statisticType);

    List<String> getBankAccountNumberByListBankAccountNumber(List<String> bankAccountNumbers);

    void rpaInAccountTask();

    List<CommonDeptCountDTO> customerAccountingCashierMiniListAccountingDeptCountList(UserDeptDTO userDeptDTO, List<Long> queryDeptIds, Integer accountingCashierType, Integer statisticType);

    List<MaterialRepeatDTO> repeatFile(Long customerServiceId, String fileName);

    List<RemoteAccountingCashierSimpleDTO> getAccountingCashierByPeriodIdsAndAccountingCashierType(RemoteAccountingCashierPeriodTypeVO vo);

    List<CustomerServiceCashierAccounting> selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber(@Param("dtoList") List<BizIdBankAccountDTO> dtoList);

    void saveNewPeriodInAccount(Integer period);

    void saveExceptionPeriodInAccount(Integer period);

    void addInAccountFromPeriod(CCustomerService customerService, CustomerServicePeriodMonth monthPeriod, Long deptId, Long userId, String operName);

    void updateBankInfoByCustomerServiceId(Long customerServiceId, CustomerServiceBankAccount oldBankAccount, String bankAccountNumber, String bankName, Long deptId, Long userId, String operName);

    void createFlowAccountingCashier(String jobParam);

    void rpaCheckFileTask();

    void autoFlowRpaDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, String operator, Long customerServiceId, Long userId, Long deptId);

    void autoFlowRpaDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, String operator, Map<Long, CCustomerService> customerMap, Long userId, Long deptId);

    void autoRpaBankPaperUpload(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CommonFileVO> files, String operator, Long userId, Long deptId);

    void createWaitInAccountBusinessTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, R resp, String groupName, String groupId, CCustomerService customerService);

    void createWaitCompleteBusinessTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, R<BanksEnterprisesExtractDTO> banksEnterprisesExtractResp, String groupName, String groupId, CCustomerService customerService);

    void checkFile(Long customerServiceCashierAccountingId, Long businessTaskId, Long searchTaskId, CheckFilesVO vo);

    boolean checkFile(CustomerServiceCashierAccounting customerServiceCashierAccounting, BusinessTask businessTask, CheckFilesVO vo);

    void accountingCashierExceptionDealByCheckFile(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, Integer checkType, R<CheckFilesDTO> resp);

    void accountingCashierExceptionDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, R<BanksEnterprisesExtractDTO> resp);

    CustomerServiceCashierAccounting accountingCashierNoBankPaymentDeal(Long customerServiceCashierAccountingId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType);

    void dealBanksEnterprisesExtract(CommonNoticeVO commonNoticeVO);

    void dealFileCheck(CommonNoticeVO commonNoticeVO);

    void dealGenerateVoucher(CommonNoticeVO commonNoticeVO);

    void dealGenerateVoucherV2(CommonNoticeVO commonNoticeVO);

    void dealBankReceiptPaperFileUpload(CommonNoticeVO commonNoticeVO);

    void updateWaitSubmitDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, CommonNoticeVO commonNoticeVO, AccountingCashierDeliverResult deliverResult);

    void updateByInvoiceUpdate(CustomerServiceCashierAccounting customerServiceCashierAccounting, String operator, AccountingCashierDeliverResult deliverResult, AccountingCashierDeliverStatus deliverStatus, String sourceName, Long deptId);

    void bankReceiptPaperUpload(List<AccountingCashierCreateVO> createVOList);

    TCommonOperateDTO<CustomerServiceCashierAccounting> batchUpdateBankAccountNumber(BatchUpdateBankAccountNumberVO vo, Long deptId);

    TCommonOperateDTO<CustomerServiceCashierAccounting> managerDelete(AccountingCashierOperateVO vo);

    Map<String, List<CustomerServiceCashierAccountingFile>> selectBatchByPeriodIdAndBankAccountNumber(List<PeriodBankAccountNumberVO> voList);

    TCommonOperateDTO<CustomerServiceCashierAccounting> modifyDdl(CustomerServiceCashierAccountingModifyDdlVO vo, Long deptId);

    void autoSettlement(OpenApiData openApiData, InAccountAutoSettlementVO autoSettlementVO, CustomerServiceCashierAccounting customerServiceCashierAccounting);
}
