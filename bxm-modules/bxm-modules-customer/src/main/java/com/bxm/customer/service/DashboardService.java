package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.customer.domain.DashboardConfig;
import com.bxm.customer.mapper.DashboardMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DashboardService {

    @Autowired
    private DashboardMapper dashboardMapper;

    public List<DashboardConfig> getDashboardConfigList(Long deptId) {
        return dashboardMapper.selectList(new LambdaQueryWrapper<DashboardConfig>()
                .eq(DashboardConfig::getDeptId, deptId)
                .eq(DashboardConfig::getIsDel, false));
    }
}
