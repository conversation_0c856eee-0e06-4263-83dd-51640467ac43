package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.OpenApiSyncItem;
import com.bxm.customer.domain.vo.CommonNoticeVO;
import com.bxm.customer.domain.vo.ReportDeductionResultVO;
import com.bxm.customer.mapper.OpenApiSyncItemMapper;
import com.bxm.customer.service.IOpenApiSyncItemService;
import com.bxm.customer.service.IReportTableTaxConfigService;
import com.bxm.thirdpart.api.domain.KouKuanDTO;
import com.bxm.thirdpart.api.domain.ReportDeductionGetDTO;
import com.bxm.thirdpart.api.domain.ShenBaoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 第三方申报同步客户详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class OpenApiSyncItemServiceImpl extends ServiceImpl<OpenApiSyncItemMapper, OpenApiSyncItem> implements IOpenApiSyncItemService
{
    @Autowired
    private OpenApiSyncItemMapper openApiSyncItemMapper;

    @Autowired
    private IReportTableTaxConfigService reportTableTaxConfigService;

    /**
     * 查询第三方申报同步客户详情
     * 
     * @param id 第三方申报同步客户详情主键
     * @return 第三方申报同步客户详情
     */
    @Override
    public OpenApiSyncItem selectOpenApiSyncItemById(Long id)
    {
        return openApiSyncItemMapper.selectOpenApiSyncItemById(id);
    }

    /**
     * 查询第三方申报同步客户详情列表
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 第三方申报同步客户详情
     */
    @Override
    public List<OpenApiSyncItem> selectOpenApiSyncItemList(OpenApiSyncItem openApiSyncItem)
    {
        return openApiSyncItemMapper.selectOpenApiSyncItemList(openApiSyncItem);
    }

    /**
     * 新增第三方申报同步客户详情
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 结果
     */
    @Override
    public int insertOpenApiSyncItem(OpenApiSyncItem openApiSyncItem)
    {
        openApiSyncItem.setCreateTime(DateUtils.getNowDate());
        return openApiSyncItemMapper.insertOpenApiSyncItem(openApiSyncItem);
    }

    /**
     * 修改第三方申报同步客户详情
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 结果
     */
    @Override
    public int updateOpenApiSyncItem(OpenApiSyncItem openApiSyncItem)
    {
        openApiSyncItem.setUpdateTime(DateUtils.getNowDate());
        return openApiSyncItemMapper.updateOpenApiSyncItem(openApiSyncItem);
    }

    /**
     * 批量删除第三方申报同步客户详情
     * 
     * @param ids 需要删除的第三方申报同步客户详情主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiSyncItemByIds(Long[] ids)
    {
        return openApiSyncItemMapper.deleteOpenApiSyncItemByIds(ids);
    }

    /**
     * 删除第三方申报同步客户详情信息
     * 
     * @param id 第三方申报同步客户详情主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiSyncItemById(Long id)
    {
        return openApiSyncItemMapper.deleteOpenApiSyncItemById(id);
    }

    @Override
    public List<OpenApiSyncItem> selectBySyncRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<OpenApiSyncItem>()
                .eq(OpenApiSyncItem::getSycRecordId, recordId));
    }

    @Override
    public List<OpenApiSyncItem> selectBySyncCustomerId(Long syncCustomerId) {
        if (Objects.isNull(syncCustomerId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<OpenApiSyncItem>()
                .eq(OpenApiSyncItem::getSyncCustomerId, syncCustomerId));
    }

    @Override
    @Transactional
    public void dealReportDeductionList(ReportDeductionGetDTO reportDeductionResult, Long syncRecordId, Long customerServiceId) {
        List<OpenApiSyncItem> itemList = selectBySyncRecordId(syncRecordId);
        if (ObjectUtils.isEmpty(itemList)) {
            return;
        }
        List<ShenBaoDTO> shenBaoList = reportDeductionResult.getShenBaoList();
        List<KouKuanDTO> kouKuanList = reportDeductionResult.getKouKuanList();
        List<KouKuanDTO> weiKouKuanList = reportDeductionResult.getWeiKouKuanList();
        List<OpenApiSyncItem> afterReportList = reportTableTaxConfigService.dealReportList(shenBaoList, itemList);
        List<OpenApiSyncItem> afterNotDeductionList = reportTableTaxConfigService.dealNotDeductionList(weiKouKuanList, afterReportList);
        List<OpenApiSyncItem> afterDeductionList = reportTableTaxConfigService.dealDeductionList(kouKuanList, afterNotDeductionList);
        saveOrUpdateBatch(afterDeductionList);
    }
}
