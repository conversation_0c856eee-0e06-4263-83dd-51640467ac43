package com.bxm.customer.domain.dto.accoutingCashier;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierDTO {

    @ApiModelProperty("交付单id")
    private Long id;

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("信用代码")
    private String creditCode;

    @ApiModelProperty("账期")
    private Integer period;

    @ApiModelProperty("服务纳税人性质，1-小规模，2-一般纳税人")
    private Integer customerServiceTaxType;

    @ApiModelProperty("服务纳税人性质")
    private String customerServiceTaxTypeStr;

    @ApiModelProperty("账期纳税人性质，1-小规模，2-一般纳税人")
    private Integer periodTaxType;

    @ApiModelProperty("账期纳税人性质")
    private String periodTaxTypeStr;

    @ApiModelProperty("客户标签")
    private String customerServiceTagNames;

    @ApiModelProperty("账期标签")
    private String periodTagNames;

    @ApiModelProperty("服务业务公司id")
    private Long customerServiceBusinessDeptId;

    @ApiModelProperty("服务业务公司名称")
    private String customerServiceBusinessDeptName;

    @ApiModelProperty("服务会计区域id")
    private Long customerServiceAccountingTopDeptId;

    @ApiModelProperty("服务会计区域名称")
    private String customerServiceAccountingTopDeptName;

    @ApiModelProperty("服务顾问小组id")
    private Long customerServiceAdvisorDeptId;

    @ApiModelProperty("服务顾问")
    private String customerServiceAdvisorInfo;

    @ApiModelProperty("服务会计小组id")
    private Long customerServiceAccountingDeptId;

    @ApiModelProperty("服务会计")
    private String customerServiceAccountingInfo;

    @ApiModelProperty("账期会计小组id")
    private Long periodAccountingDeptId;

    @ApiModelProperty("账期会计")
    private String periodAccountingInfo;

    @ApiModelProperty("账期顾问小组id")
    private Long periodAdvisorDeptId;

    @ApiModelProperty("账期顾问")
    private String periodAdvisorInfo;

    @ApiModelProperty("是否有银行流水")
    private Boolean hasBankPayment;

    @ApiModelProperty("是否有银行流水（文案）")
    private String hasBankPaymentStr;

    @ApiModelProperty("是否凭票入账")
    private Boolean hasTicket;

    @ApiModelProperty("是否凭票入账（文案）")
    private String hasTicketStr;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账号")
    private String bankAccountNumber;

    @ApiModelProperty("银行信息")
    private String bankInfo;

    @ApiModelProperty("是否有事项备注")
    private Boolean hasMattersNotes;

    @ApiModelProperty("是否有事项备注（文案）")
    private String mattersNotes;

    @ApiModelProperty("交付要求")
    private String deliverRequire;

    @ApiModelProperty("材料介质，1-电子，2-纸质，3-无，4-其他，5-银企")
    private Integer materialMedia;

    @ApiModelProperty("材料介质（文案）")
    private String materialMediaStr;

    @ApiModelProperty("介质材料数量")
    private Long materialMediaFileCount;

    @ApiModelProperty("任务id")
    private Long taskId;

    @ApiModelProperty("任务状态，1-待完成、2-待审核、3-已完结、4-已关闭、5-异常")
    private Integer taskStatus;

    @ApiModelProperty("任务状态名称")
    private String taskStatusStr;

    @ApiModelProperty("交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，5-关闭交付")
    private Integer deliverStatus;

    @ApiModelProperty("交付状态名称")
    private String deliverStatusStr;

    @ApiModelProperty("交付结果，1-正常，2-无账务，3-无需交付，4-异常")
    private Integer deliverResult;

    @ApiModelProperty("交付结果名称")
    private String deliverResultStr;

    @ApiModelProperty("完成时间")
    private LocalDateTime completeTime;

    @ApiModelProperty("完成时间（格式化后的）")
    private String completeTimeStr;

    @ApiModelProperty("银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成")
    private Integer bankPaymentResult;

    @ApiModelProperty("银行流水结果（文案）")
    private String bankPaymentResultStr;

    @ApiModelProperty("结账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer settleAccountStatus;

    @ApiModelProperty("结账状态（文案）")
    private String settleAccountStatusStr;

    @ApiModelProperty("结账时间")
    private LocalDate endTime;

    @ApiModelProperty("结账时间（格式化后的）")
    private String endTimeStr;

    @ApiModelProperty("交付备注")
    private String deliverRemark;

    @ApiModelProperty("入账附件数量")
    private Long deliverFileCount;

    @ApiModelProperty("利润取数更新时间")
    private LocalDateTime profitGetTime;

    @ApiModelProperty("利润取数更新时间（格式化后的）")
    private String profitGetTimeStr;

    @ApiModelProperty(value = "本年累计主营收入")
    private BigDecimal majorIncomeTotal;

    @ApiModelProperty(value = "本年累计主营收入（格式化后的）")
    private String majorIncomeTotalStr;

    /** 本年累计主营成本 */
    @ApiModelProperty(value = "本年累计主营成本")
    private BigDecimal majorCostTotal;

    @ApiModelProperty(value = "本年累计主营成本（格式化后的）")
    private String majorCostTotalStr;

    /** 本年累计会计利润 */
    @ApiModelProperty(value = "本年累计会计利润")
    private BigDecimal profitTotal;

    @ApiModelProperty(value = "本年累计会计利润（格式化后的）")
    private String profitTotalStr;

    /** 本年费用调增 */
    @ApiModelProperty(value = "本年费用调增")
    private String priorYearExpenseIncrease;

    /** 个税申报人数 */
    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    /** 本年个税申报工资总额 */
    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    @ApiModelProperty(value = "本年个税申报工资总额（格式化后的）")
    private String taxReportSalaryTotalStr;

    @ApiModelProperty("利润")
    private String profit;

    @ApiModelProperty("暂估")
    private String tempesti;

    @ApiModelProperty("福利费")
    private String welfare;

    @ApiModelProperty("招待费")
    private String enterain;

    @ApiModelProperty(value = "原材料")
    private String rawMaterial;

    @ApiModelProperty(value = "人工工资")
    private String laborWages;

    @ApiModelProperty(value = "费用")
    private String cost;

    @ApiModelProperty(value = "期间费用")
    private String periodCost;

    @ApiModelProperty(value = "报表状态是否平衡，0-否，1-是")
    private String tableStatusBalance;

    @ApiModelProperty(value = "报表状态是否平衡（文案）")
    private String tableStatusBalanceStr;

    @ApiModelProperty(value = "RPA执行结果：1-成功、0-失败")
    private Integer rpaExeResult;

    @ApiModelProperty(value = "RPA执行结果（文案）")
    private String rpaExeResultStr;

    @ApiModelProperty(value = "RPA查询时间")
    private LocalDateTime rpaSearchTime;

    @ApiModelProperty(value = "RPA查询时间（格式化后的）")
    private String rpaSearchTimeStr;

    @ApiModelProperty(value = "RPA备注")
    private String rpaRemark;

    @ApiModelProperty(value = "RPA附件数量")
    private Long rpaFileCount;

    @ApiModelProperty(value = "是否交付变更")
    private Boolean hasChanged;

    @ApiModelProperty(value = "附件列表")
    private List<CommonFileVO> files;

    @ApiModelProperty(value = "交付单类型，1-入账，2-流水，3-改账")
    private Integer type;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    private Integer materialIntegrity;

    @ApiModelProperty("材料完整度（文案）")
    private String materialIntegrityStr;

    @ApiModelProperty("材料补充状态，1-无需处理，2-待核对，3-已核对")
    private Integer materialSupplementStatus;

    @ApiModelProperty("对账单余额")
    private BigDecimal statementBalance;

    @ApiModelProperty("对账单余额（格式化后的）")
    private String statementBalanceStr;

    private LocalDate lastInTime;

    private String lastInTimeStr;

    private LocalDate lastEndTime;

    private String lastEndTimeStr;

    private LocalDateTime createTime;

    private String createTimeStr;

    private String createBy;

    private String lastOperName;

    private LocalDateTime lastOperTime;

    private String lastOperType;

    private String lastOperRemark;

    private LocalDate ddl;

    private String ddlStr;
}
