package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceCashierAccountingFileMapper;
import com.bxm.customer.domain.CustomerServiceCashierAccountingFile;
import com.bxm.customer.service.ICustomerServiceCashierAccountingFileService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 客户账务附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Service
public class CustomerServiceCashierAccountingFileServiceImpl extends ServiceImpl<CustomerServiceCashierAccountingFileMapper, CustomerServiceCashierAccountingFile> implements ICustomerServiceCashierAccountingFileService
{
    @Autowired
    private CustomerServiceCashierAccountingFileMapper customerServiceCashierAccountingFileMapper;

    /**
     * 查询客户账务附件
     * 
     * @param id 客户账务附件主键
     * @return 客户账务附件
     */
    @Override
    public CustomerServiceCashierAccountingFile selectCustomerServiceCashierAccountingFileById(Long id)
    {
        return customerServiceCashierAccountingFileMapper.selectCustomerServiceCashierAccountingFileById(id);
    }

    /**
     * 查询客户账务附件列表
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 客户账务附件
     */
    @Override
    public List<CustomerServiceCashierAccountingFile> selectCustomerServiceCashierAccountingFileList(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile)
    {
        return customerServiceCashierAccountingFileMapper.selectCustomerServiceCashierAccountingFileList(customerServiceCashierAccountingFile);
    }

    /**
     * 新增客户账务附件
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 结果
     */
    @Override
    public int insertCustomerServiceCashierAccountingFile(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile)
    {
        customerServiceCashierAccountingFile.setCreateTime(DateUtils.getNowDate());
        return customerServiceCashierAccountingFileMapper.insertCustomerServiceCashierAccountingFile(customerServiceCashierAccountingFile);
    }

    /**
     * 修改客户账务附件
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 结果
     */
    @Override
    public int updateCustomerServiceCashierAccountingFile(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile)
    {
        customerServiceCashierAccountingFile.setUpdateTime(DateUtils.getNowDate());
        return customerServiceCashierAccountingFileMapper.updateCustomerServiceCashierAccountingFile(customerServiceCashierAccountingFile);
    }

    /**
     * 批量删除客户账务附件
     * 
     * @param ids 需要删除的客户账务附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceCashierAccountingFileByIds(Long[] ids)
    {
        return customerServiceCashierAccountingFileMapper.deleteCustomerServiceCashierAccountingFileByIds(ids);
    }

    /**
     * 删除客户账务附件信息
     * 
     * @param id 客户账务附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceCashierAccountingFileById(Long id)
    {
        return customerServiceCashierAccountingFileMapper.deleteCustomerServiceCashierAccountingFileById(id);
    }

    @Override
    public List<CustomerServiceCashierAccountingFile> selectBatchByCashierAccountingIds(List<Long> accountingCashierIds) {
        if (ObjectUtils.isEmpty(accountingCashierIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceCashierAccountingFile>()
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false)
                .in(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId, accountingCashierIds));
    }

    @Override
    public List<CustomerServiceCashierAccountingFile> selectByAccountingCashierIdAndFileType(Long accountingCashierId, Integer fileType) {
        if (Objects.isNull(accountingCashierId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceCashierAccountingFile>()
                .eq(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId, accountingCashierId)
                .eq(!Objects.isNull(fileType), CustomerServiceCashierAccountingFile::getFileType, fileType)
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false));
    }

    @Override
    public List<CustomerServiceCashierAccountingFile> selectByAccountingCashierIdsAndFileType(List<Long> accountingCashierIds, Integer fileType) {
        if (ObjectUtils.isEmpty(accountingCashierIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceCashierAccountingFile>()
                .in(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId, accountingCashierIds)
                .eq(!Objects.isNull(fileType), CustomerServiceCashierAccountingFile::getFileType, fileType)
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false));
    }

    @Override
    @Transactional
    public void saveAccountingCashierFile(Long accountingCashierId, List<CommonFileVO> files, AccountingCashierFileType accountingCashierFileType, Integer subFileType) {
        if (ObjectUtils.isEmpty(files) || Objects.isNull(accountingCashierId)) {
            return;
        }
        saveBatch(files.stream().map(f -> new CustomerServiceCashierAccountingFile().setCustomerServiceCashierAccountingId(accountingCashierId)
                .setFileName(f.getFileName()).setFileUrl(f.getFileUrl()).setFileSize(f.getFileSize())
                .setFileRemark(f.getFileRemark())
                .setFileNo(StringUtils.isEmpty(f.getFileNumber()) ? null : Integer.parseInt(f.getFileNumber()))
                .setFileType(accountingCashierFileType.getCode()).setSubFileType(subFileType)).collect(Collectors.toList()));
    }

    @Override
    @Transactional
    public void saveAccountingCashierFile(Long accountingCashierId, List<CommonFileVO> files, AccountingCashierFileType accountingCashierFileType) {
        if (ObjectUtils.isEmpty(files) || Objects.isNull(accountingCashierId)) {
            return;
        }
        saveBatch(files.stream().map(f -> new CustomerServiceCashierAccountingFile().setCustomerServiceCashierAccountingId(accountingCashierId)
                .setFileName(f.getFileName()).setFileUrl(f.getFileUrl()).setFileSize(f.getFileSize())
                .setFileRemark(f.getFileRemark())
                .setFileNo(StringUtils.isEmpty(f.getFileNumber()) ? null : Integer.parseInt(f.getFileNumber()))
                .setOfficalFilename(f.getOfficalFilename())
                .setFileType(accountingCashierFileType.getCode()).setSubFileType(f.getFileName().contains("回单") ? 2 : 1)).collect(Collectors.toList()));
    }

    @Override
    public void logicDeleteByCashierAccountingIdAndFileType(Long accountingCashierId, AccountingCashierFileType accountingCashierFileType) {
        if (Objects.isNull(accountingCashierId) || Objects.isNull(accountingCashierFileType)) {
            return;
        }
        update(new LambdaUpdateWrapper<CustomerServiceCashierAccountingFile>()
                .eq(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId, accountingCashierId)
                .eq(CustomerServiceCashierAccountingFile::getFileType, accountingCashierFileType.getCode())
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false)
                .set(CustomerServiceCashierAccountingFile::getIsDel, true));
    }

    @Override
    public void logicDeleteByCashierAccountingIdAndFileType(Long accountingCashierId, AccountingCashierFileType accountingCashierFileType, Integer subFileType) {
        if (Objects.isNull(accountingCashierId) || Objects.isNull(accountingCashierFileType)) {
            return;
        }
        update(new LambdaUpdateWrapper<CustomerServiceCashierAccountingFile>()
                .eq(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId, accountingCashierId)
                .eq(CustomerServiceCashierAccountingFile::getFileType, accountingCashierFileType.getCode())
                .eq(CustomerServiceCashierAccountingFile::getSubFileType, subFileType)
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false)
                .set(CustomerServiceCashierAccountingFile::getIsDel, true));
    }

    @Override
    public void logicDeleteByCashierAccountingIdsAndFileType(List<Long> accountingCashierIds, AccountingCashierFileType accountingCashierFileType) {
        if (ObjectUtils.isEmpty(accountingCashierIds) || Objects.isNull(accountingCashierFileType)) {
            return;
        }
        update(new LambdaUpdateWrapper<CustomerServiceCashierAccountingFile>()
                .in(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId, accountingCashierIds)
                .eq(CustomerServiceCashierAccountingFile::getFileType, accountingCashierFileType.getCode())
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false)
                .set(CustomerServiceCashierAccountingFile::getIsDel, true));
    }

    @Override
    public void logicDeleteByCashierAccountingIdsAndFileType(List<Long> accountingCashierIds, AccountingCashierFileType accountingCashierFileType, Integer subFileType) {
        if (ObjectUtils.isEmpty(accountingCashierIds) || Objects.isNull(accountingCashierFileType)) {
            return;
        }
        update(new LambdaUpdateWrapper<CustomerServiceCashierAccountingFile>()
                .in(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId, accountingCashierIds)
                .eq(CustomerServiceCashierAccountingFile::getFileType, accountingCashierFileType.getCode())
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false)
                .eq(CustomerServiceCashierAccountingFile::getSubFileType, subFileType)
                .set(CustomerServiceCashierAccountingFile::getIsDel, true));
    }

    @Override
    @Transactional
    public void saveAccountingCashierFileBatch(List<Long> accountingCashierIds, List<CommonFileVO> files, AccountingCashierFileType accountingCashierFileType) {
        if (ObjectUtils.isEmpty(files) || ObjectUtils.isEmpty(accountingCashierIds)) {
            return;
        }
        List<CustomerServiceCashierAccountingFile> customerServiceCashierAccountingFiles = Lists.newArrayList();
        files.forEach(f -> accountingCashierIds.forEach(id -> customerServiceCashierAccountingFiles.add(new CustomerServiceCashierAccountingFile().setCustomerServiceCashierAccountingId(id)
                .setFileName(f.getFileName()).setFileUrl(f.getFileUrl()).setFileSize(f.getFileSize())
                .setFileType(accountingCashierFileType.getCode()))));
        if (!ObjectUtils.isEmpty(customerServiceCashierAccountingFiles)) {
            saveBatch(customerServiceCashierAccountingFiles);
        }
    }

    @Override
    @Transactional
    public void saveAccountingCashierFileBatch(List<Long> accountingCashierIds, List<CommonFileVO> files, AccountingCashierFileType accountingCashierFileType, Integer subFileType) {
        if (ObjectUtils.isEmpty(files) || ObjectUtils.isEmpty(accountingCashierIds)) {
            return;
        }
        List<CustomerServiceCashierAccountingFile> customerServiceCashierAccountingFiles = Lists.newArrayList();
        files.forEach(f -> accountingCashierIds.forEach(id -> customerServiceCashierAccountingFiles.add(new CustomerServiceCashierAccountingFile().setCustomerServiceCashierAccountingId(id)
                .setFileName(f.getFileName()).setFileUrl(f.getFileUrl()).setFileSize(f.getFileSize())
                .setSubFileType(subFileType)
                .setFileType(accountingCashierFileType.getCode()))));
        if (!ObjectUtils.isEmpty(customerServiceCashierAccountingFiles)) {
            saveBatch(customerServiceCashierAccountingFiles);
        }
    }

    @Override
    @Transactional
    public void removeAndSaveNewFiles(Long accountingCashierId, List<CommonFileVO> files, Integer fileType) {
        List<CustomerServiceCashierAccountingFile> customerServiceCashierAccountingFiles = list(new LambdaQueryWrapper<CustomerServiceCashierAccountingFile>()
                .eq(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId, accountingCashierId)
                .eq(CustomerServiceCashierAccountingFile::getFileType, fileType)
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false));
        files = files.stream().filter(f -> !StringUtils.isEmpty(f.getOfficalFilename())).collect(Collectors.toList());
        Map<String, List<CommonFileVO>> originFileMap = files.stream().collect(Collectors.groupingBy(CommonFileVO::getOfficalFilename));
        Map<String, List<CustomerServiceCashierAccountingFile>> fileMap = customerServiceCashierAccountingFiles.stream().collect(Collectors.groupingBy(CustomerServiceCashierAccountingFile::getOfficalFilename));
        List<CustomerServiceCashierAccountingFile> saveOrUpdates = Lists.newArrayList();
        for (Map.Entry<String, List<CommonFileVO>> entry : originFileMap.entrySet()) {
            List<CustomerServiceCashierAccountingFile> fileList = fileMap.get(entry.getKey());
            if (!ObjectUtils.isEmpty(fileList)) {
                fileList.forEach(file -> file.setIsDel(true));
                saveOrUpdates.addAll(fileList);
            }
            saveOrUpdates.addAll(entry.getValue().stream().map(file -> new CustomerServiceCashierAccountingFile().setCustomerServiceCashierAccountingId(accountingCashierId)
                    .setFileUrl(file.getFileUrl())
                    .setFileSize(0L)
                    .setFileName(file.getFileName())
                    .setFileType(fileType)
                    .setSubFileType(1)
                    .setIsDel(false)
                    .setOfficalFilename(file.getOfficalFilename()))
                    .collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(saveOrUpdates)) {
            saveOrUpdateBatch(saveOrUpdates);
        }
    }
}
