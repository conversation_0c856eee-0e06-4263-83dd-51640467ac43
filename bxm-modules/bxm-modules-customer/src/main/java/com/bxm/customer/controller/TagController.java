package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.customer.domain.dto.tag.TagV2DTO;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/tag")
@Api(tags = "标签相关")
@RestController
public class TagController {

//    public Result<TagV2DTO> addCustomizeTag()
}
