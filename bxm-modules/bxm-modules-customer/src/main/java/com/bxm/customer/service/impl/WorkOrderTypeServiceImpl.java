package com.bxm.customer.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.WorkOrderTypeDeptRelation;
import com.bxm.customer.domain.WorkOrderTypeTopDeptConfig;
import com.bxm.customer.domain.dto.workOrder.WorkOrderTypeDTO;
import com.bxm.customer.mapper.WorkOrderTypeDeptRelationMapper;
import com.bxm.customer.mapper.WorkOrderTypeTopDeptConfigMapper;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysDept;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.WorkOrderTypeMapper;
import com.bxm.customer.domain.WorkOrderType;
import com.bxm.customer.service.IWorkOrderTypeService;
import org.springframework.util.ObjectUtils;

/**
 * 工单类型配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
@Service
public class WorkOrderTypeServiceImpl extends ServiceImpl<WorkOrderTypeMapper, WorkOrderType> implements IWorkOrderTypeService
{
    @Autowired
    private WorkOrderTypeMapper workOrderTypeMapper;

    @Autowired
    private WorkOrderTypeDeptRelationMapper workOrderTypeDeptRelationMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private WorkOrderTypeTopDeptConfigMapper workOrderTypeTopDeptConfigMapper;

    /**
     * 查询工单类型配置
     * 
     * @param id 工单类型配置主键
     * @return 工单类型配置
     */
    @Override
    public WorkOrderType selectWorkOrderTypeById(Long id)
    {
        return workOrderTypeMapper.selectWorkOrderTypeById(id);
    }

    /**
     * 查询工单类型配置列表
     * 
     * @param workOrderType 工单类型配置
     * @return 工单类型配置
     */
    @Override
    public List<WorkOrderType> selectWorkOrderTypeList(WorkOrderType workOrderType)
    {
        return workOrderTypeMapper.selectWorkOrderTypeList(workOrderType);
    }

    /**
     * 新增工单类型配置
     * 
     * @param workOrderType 工单类型配置
     * @return 结果
     */
    @Override
    public int insertWorkOrderType(WorkOrderType workOrderType)
    {
        workOrderType.setCreateTime(DateUtils.getNowDate());
        return workOrderTypeMapper.insertWorkOrderType(workOrderType);
    }

    /**
     * 修改工单类型配置
     * 
     * @param workOrderType 工单类型配置
     * @return 结果
     */
    @Override
    public int updateWorkOrderType(WorkOrderType workOrderType)
    {
        workOrderType.setUpdateTime(DateUtils.getNowDate());
        return workOrderTypeMapper.updateWorkOrderType(workOrderType);
    }

    /**
     * 批量删除工单类型配置
     * 
     * @param ids 需要删除的工单类型配置主键
     * @return 结果
     */
    @Override
    public int deleteWorkOrderTypeByIds(Long[] ids)
    {
        return workOrderTypeMapper.deleteWorkOrderTypeByIds(ids);
    }

    /**
     * 删除工单类型配置信息
     * 
     * @param id 工单类型配置主键
     * @return 结果
     */
    @Override
    public int deleteWorkOrderTypeById(Long id)
    {
        return workOrderTypeMapper.deleteWorkOrderTypeById(id);
    }

    @Override
    public List<WorkOrderTypeDTO> workOrderTypeListCanSee(Long deptId, Integer showType) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException(false);
        if (Objects.isNull(sysDept) || !"0".equals(sysDept.getDelFlag())) {
            return Collections.emptyList();
        }
        if (Objects.isNull(showType)) {
            showType = 1;
        }
        Long topDeptId = sysDept.getParentId();
        List<WorkOrderTypeDeptRelation> relations = workOrderTypeDeptRelationMapper.selectList(new LambdaQueryWrapper<WorkOrderTypeDeptRelation>()
                .eq(WorkOrderTypeDeptRelation::getDeptType, sysDept.getDeptType()).eq(WorkOrderTypeDeptRelation::getShowType, showType));
        List<WorkOrderTypeTopDeptConfig> configs = workOrderTypeTopDeptConfigMapper.selectList(new LambdaQueryWrapper<WorkOrderTypeTopDeptConfig>()
                .eq(WorkOrderTypeTopDeptConfig::getDeptId, topDeptId).eq(WorkOrderTypeTopDeptConfig::getShowType, showType));
        Set<Long> workOrderTypeIds = new HashSet<>();
        if (!ObjectUtils.isEmpty(relations)) {
            workOrderTypeIds.addAll(relations.stream().map(WorkOrderTypeDeptRelation::getWorkOrderTypeId).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(configs)) {
            workOrderTypeIds.addAll(configs.stream().map(WorkOrderTypeTopDeptConfig::getWorkOrderTypeId).collect(Collectors.toList()));
        }
        if (ObjectUtils.isEmpty(workOrderTypeIds)) {
            return Collections.emptyList();
        }
        List<WorkOrderType> types = list(new LambdaQueryWrapper<WorkOrderType>()
                .eq(WorkOrderType::getIsDel, false)
                .eq(WorkOrderType::getIsShow, true)
                .in(WorkOrderType::getId, workOrderTypeIds)
                .orderByAsc(WorkOrderType::getWorkOrderType));
        if (ObjectUtils.isEmpty(types)) {
            return Collections.emptyList();
        }
        return types.stream().map(row -> {
            WorkOrderTypeDTO workOrderTypeDTO = new WorkOrderTypeDTO();
            BeanUtils.copyProperties(row, workOrderTypeDTO);
            return workOrderTypeDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public WorkOrderType selectByWorkOrderType(Integer workOrderType) {
        if (Objects.isNull(workOrderType)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<WorkOrderType>().eq(WorkOrderType::getWorkOrderType, workOrderType));
    }

    @Override
    public List<WorkOrderTypeDTO> workOrderTypeList() {
        List<WorkOrderType> types = list(new LambdaQueryWrapper<WorkOrderType>()
                .eq(WorkOrderType::getIsDel, false)
                .eq(WorkOrderType::getIsShow, true)
                .orderByAsc(WorkOrderType::getWorkOrderType));
        if (ObjectUtils.isEmpty(types)) {
            return Collections.emptyList();
        }
        return types.stream().map(row -> WorkOrderTypeDTO.builder().workOrderType(row.getWorkOrderType())
                .workOrderTypeName(row.getWorkOrderTypeName()).build()).collect(Collectors.toList());
    }
}
