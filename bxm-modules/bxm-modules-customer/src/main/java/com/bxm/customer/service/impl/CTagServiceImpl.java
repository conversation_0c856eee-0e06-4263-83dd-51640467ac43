package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.TagTypeEnum;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CBusinessTagRelation;
import com.bxm.customer.domain.CTag;
import com.bxm.customer.domain.dto.tag.TagSelectDTO;
import com.bxm.customer.domain.dto.tag.TagV2DTO;
import com.bxm.customer.mapper.CBusinessTagRelationMapper;
import com.bxm.customer.mapper.CTagMapper;
import com.bxm.customer.service.ICTagService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysDept;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
public class CTagServiceImpl extends ServiceImpl<CTagMapper, CTag> implements ICTagService
{
    @Autowired
    private CTagMapper cTagMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private CBusinessTagRelationMapper businessTagRelationMapper;

    /**
     * 查询标签
     * 
     * @param id 标签主键
     * @return 标签
     */
    @Override
    public CTag selectCTagById(Long id)
    {
        return cTagMapper.selectCTagById(id);
    }

    /**
     * 查询标签列表
     * 
     * @param cTag 标签
     * @return 标签
     */
    @Override
    public List<CTag> selectCTagList(CTag cTag)
    {
        return cTagMapper.selectCTagList(cTag);
    }

    /**
     * 新增标签
     * 
     * @param cTag 标签
     * @return 结果
     */
    @Override
    public int insertCTag(CTag cTag)
    {
        cTag.setCreateTime(DateUtils.getNowDate());
        return cTagMapper.insertCTag(cTag);
    }

    /**
     * 修改标签
     * 
     * @param cTag 标签
     * @return 结果
     */
    @Override
    public int updateCTag(CTag cTag)
    {
        cTag.setUpdateTime(DateUtils.getNowDate());
        return cTagMapper.updateCTag(cTag);
    }

    /**
     * 批量删除标签
     * 
     * @param ids 需要删除的标签主键
     * @return 结果
     */
    @Override
    public int deleteCTagByIds(Long[] ids)
    {
        return cTagMapper.deleteCTagByIds(ids);
    }

    /**
     * 删除标签信息
     * 
     * @param id 标签主键
     * @return 结果
     */
    @Override
    public int deleteCTagById(Long id)
    {
        return cTagMapper.deleteCTagById(id);
    }

    @Override
    public CTag selectByTagTypeAndTagName(Integer tagType, String tagName) {
        return getOne(new LambdaQueryWrapper<CTag>().eq(CTag::getTagName, tagName)
                .eq(CTag::getTagType, tagType).last("limit 1"));
    }

    @Override
    public TagSelectDTO tagSelectList(Long deptId, Integer businessType, Long businessId) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        Long topDeptId = Long.parseLong(sysDept.getAncestors().split(",")[1]);
        List<CTag> tagList = list(new LambdaQueryWrapper<CTag>()
                .eq(CTag::getIsDel, false)
                .eq(CTag::getStatus, 1)
                .eq(CTag::getIsCustomize, false)
                .and(queryWrapper -> queryWrapper.eq(CTag::getTagType, TagTypeEnum.SYSTEM_TAG.getCode())
                        .or(subWrapper -> subWrapper.eq(CTag::getTagType, TagTypeEnum.GROUP_TAG.getCode()).eq(CTag::getTopDeptId, topDeptId))));
        if (ObjectUtils.isEmpty(tagList)) {
            return new TagSelectDTO();
        }
        boolean hasBusiness = !Objects.isNull(businessType) && !Objects.isNull(businessId);
        List<CBusinessTagRelation> relations = hasBusiness ?
                businessTagRelationMapper.selectList(new LambdaQueryWrapper<CBusinessTagRelation>()
                        .eq(CBusinessTagRelation::getBusinessId, businessId)
                        .eq(CBusinessTagRelation::getBusinessType, businessType)) : Lists.newArrayList();
        List<TagV2DTO> groupTagList = tagList.stream().filter(tag -> Objects.equals(tag.getTagType(), TagTypeEnum.GROUP_TAG.getCode())).map(tag -> TagV2DTO
                .builder()
                .isSelected(relations.stream().anyMatch(relation -> Objects.equals(relation.getTagId(), tag.getId())))
                .hasParam(tag.getHasParam())
                .tagName(tag.getTagName())
                .isCustomize(false)
                .id(tag.getId())
                .build()).collect(Collectors.toList());
        // 查询自定义的标签
        List<TagV2DTO> selfTagList = hasBusiness ? businessTagRelationMapper.selectTagByBusinessTypeAndBusinessId(businessType, businessId) : Lists.newArrayList();
        if (!ObjectUtils.isEmpty(selfTagList)) {
            selfTagList.forEach(tag -> {
                tag.setIsCustomize(true);
                tag.setIsSelected(true);
                tag.setHasParam(false);
            });
        }
        groupTagList.addAll(selfTagList);
        return TagSelectDTO.builder()
                .systemTagList(tagList.stream().filter(tag -> Objects.equals(tag.getTagType(), TagTypeEnum.SYSTEM_TAG.getCode())).map(tag -> TagV2DTO
                        .builder()
                        .isSelected(relations.stream().anyMatch(relation -> Objects.equals(relation.getTagId(), tag.getId())))
                        .hasParam(tag.getHasParam())
                        .tagName(tag.getTagName())
                        .isCustomize(false)
                        .id(tag.getId())
                        .build()).collect(Collectors.toList()))
                .deptTagList(groupTagList)
                .build();
    }
}
