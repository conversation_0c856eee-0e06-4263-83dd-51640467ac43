package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 报表配置对象 c_dashboard_config
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("报表配置对象")
@Accessors(chain = true)
@TableName("c_dashboard_config")
public class DashboardConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "自增id")
    @ApiModelProperty(value = "自增id")
    private Long id;

    @ApiModelProperty("组织id")
    @TableField("dept_id")
    private Long deptId;

    @ApiModelProperty("报表标题")
    @TableField("title")
    private String title;

    @ApiModelProperty("报表备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty("报表链接")
    @TableField("url")
    private String url;

    @ApiModelProperty(value = "是否删除，0-否，1-是")
    @TableField("is_del")
    private Boolean isDel;
}
