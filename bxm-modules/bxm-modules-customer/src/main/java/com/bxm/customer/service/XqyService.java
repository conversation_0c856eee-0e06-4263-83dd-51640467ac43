package com.bxm.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.PreAuthInfoDTO;
import com.bxm.customer.domain.vo.*;
import com.bxm.customer.domain.vo.xqy.XqyFileVO;
import com.bxm.customer.domain.vo.xqy.XqyReportVO;
import com.bxm.customer.domain.vo.xqy.XqySupplementVO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class XqyService {

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    @Autowired
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    private ICustomerDeliverService customerDeliverService;

    @Autowired
    private IOpenApiSyncCustomerService openApiSyncCustomerService;

    @Autowired
    private IOpenApiSyncItemService openApiSyncItemService;

    @Autowired
    private IOpenApiSyncStatementDetailService openApiSyncStatementDetailService;

    @Autowired
    private IOpenApiSyncRecordService openApiSyncRecordService;

    @Autowired
    private IOpenApiSupplementRecordService openApiSupplementRecordService;

    @Autowired
    private IOpenApiDeductionRecordService openApiDeductionRecordService;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private IOpenApiSyncVatDataService openApiSyncVatDataService;

    @Autowired
    private IOpenApiDataService openApiDataService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Autowired
    private IOpenApiInvoiceDataService openApiInvoiceDataService;

    @Autowired
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IQualityCheckingResultService qualityCheckingResultService;

    public void report(XqyReportVO reportVO) {
        Long recordId = saveOpenApiSyncRecord(reportVO);
        dealBySyncRecordId(recordId, reportVO.getOperateName(), reportVO.getReportPeriod(), reportVO.getDeliverType(), reportVO.getDeptId());
        updateAsyncRecordDataCount(recordId);
    }

    public void updateAsyncRecordDataCount(Long recordId) {
        List<OpenApiSyncCustomer> customers = openApiSyncCustomerService.selectBySyncRecordId(recordId);
        if (ObjectUtils.isEmpty(customers)) {
            return;
        }
        openApiSyncRecordService.updateById(new OpenApiSyncRecord().setId(recordId)
                .setTotalDataCount((long) customers.size())
                .setSuccessDataCount(customers.stream().filter(OpenApiSyncCustomer::getIsSuccess).count()));
    }

    private Long saveOpenApiSyncRecord(XqyReportVO xqyReportVO) {
        Integer period = StringUtils.isEmpty(xqyReportVO.getReportPeriod()) ? DateUtils.getNowPeriod() : DateUtils.yearMonthToPeriod(xqyReportVO.getReportPeriod());
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(xqyReportVO.getBatchNo());
        record.setSourceType(OpenApiAppRelations.XQY.getId());
        record.setPeriod(period);
        record.setDeliverType(xqyReportVO.getDeliverType());
        record.setCreateBy(xqyReportVO.getOperateName());
        record.setType(xqyReportVO.getOperateType());
        if (ObjectUtils.isEmpty(xqyReportVO.getTaxNumberList())) {
            log.info("鑫启易申报数据异常，本次申报税号列表为空");
            record.setErrorReason("数据异常，本次申报税号列表为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getReportPeriod())) {
            log.info("鑫启易申报数据异常，本次申报所属期为空");
            record.setErrorReason("数据异常，本次申报所属期为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getOperateName())) {
            log.info("鑫启易申报数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        openApiSyncRecordService.save(record);
        String periodMonth = xqyReportVO.getReportPeriod().split("-")[1];
        String prePeriod;
        if (Objects.equals(periodMonth, "01")) {
            prePeriod = (Integer.parseInt(xqyReportVO.getReportPeriod().split("-")[0]) - 1) + "12";
        } else {
            prePeriod = period - 1 + "";
        }
        Integer prePeriodInt = Integer.parseInt(prePeriod);
        List<CustomerServicePeriodMonth> periodList = customerServicePeriodMonthService.list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getPeriod, period, prePeriodInt)
                .in(CustomerServicePeriodMonth::getCreditCode, xqyReportVO.getTaxNumberList()));
        Map<String, List<CustomerServicePeriodMonth>> periodMap = periodList.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCreditCode));
        Map<Long, List<CustomerDeliver>> deliverMap = ObjectUtils.isEmpty(periodList) ? Maps.newHashMap() :
                customerDeliverService.list(new LambdaQueryWrapper<CustomerDeliver>()
                                .eq(CustomerDeliver::getIsDel, false)
                                .in(CustomerDeliver::getCustomerServicePeriodMonthId, periodList.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList())))
                        .stream().collect(Collectors.groupingBy(CustomerDeliver::getCustomerServicePeriodMonthId));
        Map<Long, CCustomerService> customerServiceMap = ObjectUtils.isEmpty(periodList) ? Maps.newHashMap() :
                customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                        .in(CCustomerService::getId, periodList.stream().map(CustomerServicePeriodMonth::getCustomerServiceId).distinct().collect(Collectors.toList())))
                        .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        List<Integer> deliverTypes = Lists.newArrayList();
        if (StringUtils.isEmpty(xqyReportVO.getDeliverType())) {
            deliverTypes.addAll(Arrays.stream(DeliverType.values()).map(DeliverType::getCode).collect(Collectors.toList()));
        } else {
            List<String> deliverTypeList = Arrays.stream(xqyReportVO.getDeliverType().split(",")).collect(Collectors.toList());
            if (deliverTypeList.contains("1")) {
                deliverTypes.add(DeliverType.MEDICAL_INSURANCE.getCode());
            }
            if (deliverTypeList.contains("2")) {
                deliverTypes.add(DeliverType.SOCIAL_INSURANCE.getCode());
            }
            if (deliverTypeList.contains("3")) {
                deliverTypes.add(DeliverType.TAX.getCode());
                deliverTypes.add(DeliverType.TAX_OPERATING_INCOME.getCode());
            }
            if (deliverTypeList.contains("4")) {
                deliverTypes.add(DeliverType.NATIONAL_TAX.getCode());
            }
        }
        List<OpenApiSyncItem> items = Lists.newArrayList();
        List<OpenApiSyncStatementDetail> details = Lists.newArrayList();
        xqyReportVO.getTaxNumberList().forEach(taxNumber -> {
            // 循环查询每个税号的申报情况
            OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
            customer.setTaxNumber(taxNumber);
            customer.setSycRecordId(record.getId());
            try {
                List<Map<String, Object>> result = remoteThirdpartService.xqyQueryTaxDeclarationData(taxNumber, xqyReportVO.getReportPeriod()).getDataThrowException();
                log.info("鑫启易申报查询数据结果:{}，税号为：{}，申报周期：{}，操作人：{}", result, taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
                List<Map<String, Object>> statementDetailResult = Lists.newArrayList();
                if (deliverTypes.contains(DeliverType.MEDICAL_INSURANCE.getCode()) || deliverTypes.contains(DeliverType.SOCIAL_INSURANCE.getCode())) {
                    statementDetailResult = remoteThirdpartService.xqySocialSecurityStatementDetailsQuery(taxNumber, DateUtils.periodToYeaMonth(period)).getDataThrowException(false);
                    statementDetailResult = Objects.isNull(statementDetailResult) ? Lists.newArrayList() : statementDetailResult;
                    log.info("鑫启易社保个人明细数据查询结果:{}，税号为：{}，申报周期：{}，操作人：{}", statementDetailResult, taxNumber, DateUtils.periodToYeaMonth(period), xqyReportVO.getOperateName());
                }
                if (!ObjectUtils.isEmpty(result)) {
                    String customerName = String.valueOf(result.get(0).get("客户名称"));
                    customer.setCustomerName(customerName);
                    customer.setIsSuccess(true);
                    List<CustomerServicePeriodMonth> periodMonths = periodMap.get(taxNumber);
                    if (ObjectUtils.isEmpty(periodMonths)) {
                        customer.setNationalTaxResult("账期不存在");
                        customer.setPersonTaxResult("账期不存在");
                        customer.setSocialSecurityResult("账期不存在");
                        customer.setMedicalSecurityResult("账期不存在");
                        customer.setTaxOperatingResult("账期不存在");
                        customer.setIsSuccess(false);
                    } else {
                        customer.setCustomerServiceId(periodMonths.get(0).getCustomerServiceId());
                        CCustomerService customerService = customerServiceMap.get(customer.getCustomerServiceId());
                        Integer comparePeriod = !Objects.isNull(customerService) && customerService.getDeliverReportType() == 2 ? prePeriodInt : period;
                        if (periodMonths.stream().noneMatch(p -> Objects.equals(p.getPeriod(), comparePeriod))) {
                            customer.setSocialSecurityResult("账期不存在");
                            customer.setMedicalSecurityResult("账期不存在");
                            customer.setIsSuccess(false);
                        } else {
                            CustomerServicePeriodMonth thisPeriod = periodMonths.stream().filter(p -> Objects.equals(p.getPeriod(), comparePeriod)).collect(Collectors.toList()).get(0);
                            if (!deliverMap.containsKey(thisPeriod.getId())) {
                                customer.setSocialSecurityResult("交付单不存在");
                                customer.setMedicalSecurityResult("交付单不存在");
                                customer.setIsSuccess(false);
                            } else {
                                List<CustomerDeliver> customerDelivers = deliverMap.get(thisPeriod.getId());
                                Map<Integer, CustomerDeliver> deliverTypeMap = customerDelivers.stream().collect(Collectors.toMap(CustomerDeliver::getDeliverType, Function.identity(), (v1, v2) -> v1));
                                CustomerDeliver medicalSecurityDeliver = getByDeliverType(DeliverType.MEDICAL_INSURANCE.getCode(), deliverTypeMap);
                                customer.setMedicalSecurityDeliverId(Objects.isNull(medicalSecurityDeliver) ? null : medicalSecurityDeliver.getId());
                                if (Objects.isNull(medicalSecurityDeliver)) {
                                    customer.setMedicalSecurityResult("交付单不存在");
                                    customer.setIsSuccess(false);
                                } else {
                                    if (medicalSecurityDeliver.getHasChanged()) {
                                        customer.setMedicalSecurityResult("交付变更待确认");
                                        customer.setIsSuccess(false);
                                    } else {
                                        if (!DeliverStatus.canOpenApiReportStatus().contains(medicalSecurityDeliver.getStatus())) {
                                            customer.setMedicalSecurityResult("交付单状态不符");
                                            customer.setIsSuccess(false);
                                        }
                                    }
                                }
                                CustomerDeliver socialSecurityDeliver = getByDeliverType(DeliverType.SOCIAL_INSURANCE.getCode(), deliverTypeMap);
                                customer.setSocialSecurityDeliverId(Objects.isNull(socialSecurityDeliver) ? null : socialSecurityDeliver.getId());
                                if (Objects.isNull(socialSecurityDeliver)) {
                                    customer.setSocialSecurityResult("交付单不存在");
                                    customer.setIsSuccess(false);
                                } else {
                                    if (socialSecurityDeliver.getHasChanged()) {
                                        customer.setSocialSecurityResult("交付变更待确认");
                                        customer.setIsSuccess(false);
                                    } else {
                                        if (!DeliverStatus.canOpenApiReportStatus().contains(socialSecurityDeliver.getStatus())) {
                                            customer.setSocialSecurityResult("交付单状态不符");
                                            customer.setIsSuccess(false);
                                        }
                                    }
                                }
                            }
                        }
                        if (periodMonths.stream().noneMatch(p -> Objects.equals(p.getPeriod(), prePeriodInt))) {
                            customer.setNationalTaxResult("账期不存在");
                            customer.setPersonTaxResult("账期不存在");
                            customer.setTaxOperatingResult("账期不存在");
                            customer.setIsSuccess(false);
                        } else {
                            CustomerServicePeriodMonth prePeriodMoth = periodMonths.stream().filter(p -> Objects.equals(p.getPeriod(), prePeriodInt)).collect(Collectors.toList()).get(0);
                            if (!deliverMap.containsKey(prePeriodMoth.getId())) {
                                customer.setNationalTaxResult("交付单不存在");
                                customer.setPersonTaxResult("交付单不存在");
                                customer.setTaxOperatingResult("交付单不存在");
                                customer.setIsSuccess(false);
                            } else {
                                List<CustomerDeliver> customerDelivers = deliverMap.get(prePeriodMoth.getId());
                                Map<Integer, CustomerDeliver> deliverTypeMap = customerDelivers.stream().collect(Collectors.toMap(CustomerDeliver::getDeliverType, Function.identity(), (v1, v2) -> v1));
                                CustomerDeliver nationalTaxDeliver = getByDeliverType(DeliverType.NATIONAL_TAX.getCode(), deliverTypeMap);
                                customer.setNationalTaxDeliverId(Objects.isNull(nationalTaxDeliver) ? null : nationalTaxDeliver.getId());
                                if (Objects.isNull(nationalTaxDeliver)) {
                                    customer.setNationalTaxResult("交付单不存在");
                                    customer.setIsSuccess(false);
                                } else {
                                    if (nationalTaxDeliver.getHasChanged()) {
                                        customer.setNationalTaxResult("交付变更待确认");
                                        customer.setIsSuccess(false);
                                    } else {
                                        if (!DeliverStatus.canOpenApiReportStatus().contains(nationalTaxDeliver.getStatus())) {
                                            customer.setNationalTaxResult("交付单状态不符");
                                            customer.setIsSuccess(false);
                                        }
                                    }
                                }
                                CustomerDeliver personTaxDeliver = getByDeliverType(DeliverType.TAX.getCode(), deliverTypeMap);
                                customer.setPersonTaxDeliverId(Objects.isNull(personTaxDeliver) ? null : personTaxDeliver.getId());
                                if (Objects.isNull(personTaxDeliver)) {
                                    customer.setPersonTaxResult("交付单不存在");
                                    customer.setIsSuccess(false);
                                } else {
                                    if (personTaxDeliver.getHasChanged()) {
                                        customer.setPersonTaxResult("交付变更待确认");
                                        customer.setIsSuccess(false);
                                    } else {
                                        if (!DeliverStatus.canOpenApiReportStatus().contains(personTaxDeliver.getStatus())) {
                                            customer.setPersonTaxResult("交付单状态不符");
                                            customer.setIsSuccess(false);
                                        }
                                    }
                                }
                                CustomerDeliver operatingTaxDeliver = getByDeliverType(DeliverType.TAX_OPERATING_INCOME.getCode(), deliverTypeMap);
                                customer.setTaxOperatingDeliverId(Objects.isNull(operatingTaxDeliver) ? null : operatingTaxDeliver.getId());
                                if (Objects.isNull(operatingTaxDeliver)) {
                                    customer.setTaxOperatingResult("交付单不存在");
                                    customer.setIsSuccess(false);
                                } else {
                                    if (operatingTaxDeliver.getHasChanged()) {
                                        customer.setTaxOperatingResult("交付变更待确认");
                                        customer.setIsSuccess(false);
                                    } else {
                                        if (!DeliverStatus.canOpenApiReportStatus().contains(operatingTaxDeliver.getStatus())) {
                                            customer.setTaxOperatingResult("交付单状态不符");
                                            customer.setIsSuccess(false);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    openApiSyncCustomerService.save(customer);
                    for (Map<String, Object> map : result) {
                        if (Objects.isNull(map.get("申报情况"))) {
                            continue;
                        }
                        Map<String, Object> subMap = JSONObject.parseObject(map.get("申报情况").toString(), Map.class);
                        if (Objects.isNull(subMap.get("申报数据"))) {
                            continue;
                        }
                        List<Map<String, Object>> subList = JSONObject.parseObject(subMap.get("申报数据").toString(), List.class);
                        subList.forEach(reportList -> {
                            OpenApiSyncItem item = new OpenApiSyncItem();
                            item.setSycRecordId(record.getId());
                            item.setSyncCustomerId(customer.getId());
                            item.setCustomerName(customerName);
                            item.setTaxNumber(taxNumber);
                            item.setItemCategoryName(Objects.isNull(reportList.get("征收项目")) ? null : reportList.get("征收项目").toString());
                            item.setItemName(Objects.isNull(reportList.get("征收品目")) ? null : reportList.get("征收品目").toString());
                            item.setActualPayTaxAmount(Objects.isNull(reportList.get("实际应纳税额")) ? null : reportList.get("实际应纳税额").toString());
                            item.setIsReport(Objects.isNull(reportList.get("是否申报")) ? null : reportList.get("是否申报").toString());
                            item.setIsPaid(Objects.isNull(reportList.get("是否缴款")) ? null : reportList.get("是否缴款").toString());
                            item.setTaxPeriodStart(Objects.isNull(reportList.get("税款所属期起")) ? null : reportList.get("税款所属期起").toString());
                            item.setTaxPeriodEnd(Objects.isNull(reportList.get("税款所属期止")) ? null : reportList.get("税款所属期止").toString());
                            item.setReportPeriod(Objects.isNull(reportList.get("申报期")) ? null : reportList.get("申报期").toString());
                            item.setReportType(Objects.isNull(reportList.get("纳税期限")) ? null : reportList.get("纳税期限").toString());
                            item.setReportDate(Objects.isNull(reportList.get("申报日期")) ? null : reportList.get("申报日期").toString());
                            item.setTaxPeriodEndMonth(StringUtils.isEmpty(item.getTaxPeriodEnd()) ? null : item.getTaxPeriodEnd().substring(0, 7));
                            items.add(item);
                        });
                    }
                    for (Map<String, Object> map : statementDetailResult) {
                        OpenApiSyncStatementDetail detail = new OpenApiSyncStatementDetail();
                        detail.setSycRecordId(record.getId());
                        detail.setSyncCustomerId(customer.getId());
                        detail.setTaxNumber(taxNumber);
                        detail.setUid(Objects.isNull(map.get("UID")) ? null : map.get("UID").toString());
                        detail.setTaxNumber(Objects.isNull(map.get("税号")) ? null : map.get("税号").toString());
                        detail.setCustomerId(Objects.isNull(map.get("客户ID")) ? null : map.get("客户ID").toString());
                        detail.setAccountingMonth(Objects.isNull(map.get("建账月份")) ? null : map.get("建账月份").toString());
                        detail.setPaymentMonth(Objects.isNull(map.get("缴费所属月份")) ? null : map.get("缴费所属月份").toString());
                        detail.setPersonalId(Objects.isNull(map.get("个人识别号")) ? null : map.get("个人识别号").toString());
                        detail.setName(Objects.isNull(map.get("姓名")) ? null : map.get("姓名").toString());
                        detail.setInsuredIdentity(Objects.isNull(map.get("参保人员身份")) ? null : map.get("参保人员身份").toString());
                        detail.setTotalInsuredMonths(Objects.isNull(map.get("累计参保月数")) ? null : map.get("累计参保月数").toString());
                        detail.setTotalPaymentAmount(Objects.isNull(map.get("缴费总金额")) ? null : map.get("缴费总金额").toString());
                        detail.setCompanyBearAmount(Objects.isNull(map.get("单位承担金额")) ? null : map.get("单位承担金额").toString());
                        detail.setPersonalBearAmount(Objects.isNull(map.get("个人承担金额")) ? null : map.get("个人承担金额").toString());
                        detail.setMedicalPersonalBear(Objects.isNull(map.get("医疗_个人承担")) ? null : map.get("医疗_个人承担").toString());
                        detail.setPensionPersonalBear(Objects.isNull(map.get("养老_个人承担")) ? null : map.get("养老_个人承担").toString());
                        detail.setUnemploymentPersonalBear(Objects.isNull(map.get("失业_个人承担")) ? null : map.get("失业_个人承担").toString());
                        detail.setMaternityPersonalBear(Objects.isNull(map.get("生育_个人承担")) ? null : map.get("生育_个人承担").toString());
                        detail.setEmployeeMedicalInsurancePersonal(Objects.isNull(map.get("职工社会医疗保险_个人")) ? null : map.get("职工社会医疗保险_个人").toString());
                        detail.setOccupationalAnnuityPersonal(Objects.isNull(map.get("职业年金缴费金额_个人")) ? null : map.get("职业年金缴费金额_个人").toString());
                        detail.setGovernmentAgencyPensionPersonal(Objects.isNull(map.get("机关事业单位养老保险缴费金额_个人")) ? null : map.get("机关事业单位养老保险缴费金额_个人").toString());
                        detail.setCivilServantMedicalSubsidyPersonal(Objects.isNull(map.get("公务员医疗补助缴费金额_个人")) ? null : map.get("公务员医疗补助缴费金额_个人").toString());
                        detail.setMajorMedicalExpenseSubsidyPersonal(Objects.isNull(map.get("大额医疗费用补助缴费金额_个人")) ? null : map.get("大额医疗费用补助缴费金额_个人").toString());
                        detail.setLongTermCareInsurancePersonal(Objects.isNull(map.get("长期照护保险缴费金额_个人")) ? null : map.get("长期照护保险缴费金额_个人").toString());
                        detail.setMedicalCompanyBear(Objects.isNull(map.get("医疗_单位承担")) ? null : map.get("医疗_单位承担").toString());
                        detail.setPensionCompanyBear(Objects.isNull(map.get("养老_单位承担")) ? null : map.get("养老_单位承担").toString());
                        detail.setInjuryCompanyBear(Objects.isNull(map.get("工伤_单位承担")) ? null : map.get("工伤_单位承担").toString());
                        detail.setUnemploymentCompanyBear(Objects.isNull(map.get("失业_单位承担")) ? null : map.get("失业_单位承担").toString());
                        detail.setMaternityCompanyBear(Objects.isNull(map.get("生育_单位承担")) ? null : map.get("生育_单位承担").toString());
                        detail.setEmployeeMajorDiseaseMedicalSubsidyCompanyBear(Objects.isNull(map.get("职工重大疾病医疗补助_单位承担")) ? null : map.get("职工重大疾病医疗补助_单位承担").toString());
                        detail.setEmployeeMedicalInsuranceCompany(Objects.isNull(map.get("职工社会医疗保险_单位")) ? null : map.get("职工社会医疗保险_单位").toString());
                        detail.setEmployeeAdditionalMedicalInsuranceCompany(Objects.isNull(map.get("职工补充医疗保险_单位")) ? null : map.get("职工补充医疗保险_单位").toString());
                        detail.setOccupationalAnnuityCompany(Objects.isNull(map.get("职业年金缴费金额_单位")) ? null : map.get("职业年金缴费金额_单位").toString());
                        detail.setGovernmentAgencyPensionCompany(Objects.isNull(map.get("机关事业单位养老保险缴费金额_单位")) ? null : map.get("机关事业单位养老保险缴费金额_单位").toString());
                        detail.setCivilServantMedicalSubsidyCompany(Objects.isNull(map.get("公务员医疗补助缴费金额_单位")) ? null : map.get("公务员医疗补助缴费金额_单位").toString());
                        detail.setMajorMedicalExpenseSubsidyCompany(Objects.isNull(map.get("大额医疗费用补助缴费金额_单位")) ? null : map.get("大额医疗费用补助缴费金额_单位").toString());
                        detail.setEmployeeMajorMedicalMutualInsuranceCompany(Objects.isNull(map.get("职工大额医疗互助保险缴费金额_单位")) ? null : map.get("职工大额医疗互助保险缴费金额_单位").toString());
                        detail.setLongTermCareInsuranceCompany(Objects.isNull(map.get("长期照护保险缴费金额_单位")) ? null : map.get("长期照护保险缴费金额_单位").toString());
                        detail.setEmployeeMajorDiseaseMutualInsuranceCompany(Objects.isNull(map.get("职工大病医疗互助保险缴费金额_单位")) ? null : map.get("职工大病医疗互助保险缴费金额_单位").toString());
                        details.add(detail);
                    }
                } else {
                    customer.setNationalTaxResult("未查询到申报数据");
                    customer.setPersonTaxResult("未查询到申报数据");
                    customer.setSocialSecurityResult("未查询到申报数据");
                    customer.setMedicalSecurityResult("未查询到申报数据");
                    customer.setTaxOperatingResult("未查询到申报数据");
                    customer.setIsSuccess(false);
                    openApiSyncCustomerService.save(customer);
                }
            } catch (Exception e) {
                log.error("鑫启易申报查询数据异常:{}，税号为：{}，申报周期：{}，操作人：{}", e.getMessage(), taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
                customer.setNationalTaxResult("申报查询数据异常:" + e.getMessage());
                customer.setPersonTaxResult("申报查询数据异常:" + e.getMessage());
                customer.setSocialSecurityResult("申报查询数据异常:" + e.getMessage());
                customer.setMedicalSecurityResult("申报查询数据异常:" + e.getMessage());
                customer.setTaxOperatingResult("申报查询数据异常:" + e.getMessage());
                customer.setIsSuccess(false);
                openApiSyncCustomerService.save(customer);
            }
        });
        if (!ObjectUtils.isEmpty(items)) {
            openApiSyncItemService.saveBatch(items);
        }
        if (!ObjectUtils.isEmpty(details)) {
            openApiSyncStatementDetailService.saveBatch(details);
        }
        return record.getId();
    }

    private Long saveOpenApiSyncRecord(CommonReportVO commonReportVO) {
        Integer period = StringUtils.isEmpty(commonReportVO.getReportPeriod()) ? null : DateUtils.yearMonthToPeriod(commonReportVO.getReportPeriod());
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(commonReportVO.getBatchNo());
        record.setSourceType(commonReportVO.getSource());
        record.setPeriod(period);
        record.setDeliverType(commonReportVO.getDeliverType());
        record.setCreateBy(commonReportVO.getOperateName());
        record.setType(2);
        if (StringUtils.isEmpty(commonReportVO.getTaxNumber())) {
            log.info("公共申报数据异常，本次申报税号为空");
            record.setErrorReason("数据异常，本次申报税号为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(commonReportVO.getReportPeriod())) {
            log.info("公共申报数据异常，本次申报所属期为空");
            record.setErrorReason("数据异常，本次申报所属期为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(commonReportVO.getOperateName())) {
            log.info("公共申报数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(commonReportVO.getDeliverType())) {
            log.info("公共申报数据异常，本次交付类型为空");
            record.setErrorReason("数据异常，本次交付类型为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        List<CustomerServicePeriodMonth> periodList = customerServicePeriodMonthService.list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getPeriod, period)
                .eq(CustomerServicePeriodMonth::getCreditCode, commonReportVO.getTaxNumber()));
        if (periodList.size() > 1) {
            log.info("公共申报数据异常，同税号下查询到多条账期");
            record.setErrorReason("数据异常，同税号下查询到多条账期");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        openApiSyncRecordService.save(record);
        CustomerServicePeriodMonth periodMonth = ObjectUtils.isEmpty(periodList) ? null : periodList.get(0);
        CustomerDeliver customerDeliver = Objects.isNull(periodMonth) ? null : customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodList.get(0).getId()).eq(CustomerDeliver::getDeliverType, commonReportVO.getDeliverType())
                .eq(CustomerDeliver::getIsDel, Boolean.FALSE), false);
        OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
        customer.setTaxNumber(commonReportVO.getTaxNumber());
        customer.setSycRecordId(record.getId());
        customer.setCustomerName(Objects.isNull(periodMonth) ? "" : periodMonth.getCustomerName());
        customer.setIsSuccess(true);
        if (Objects.equals(commonReportVO.getDeliverType(), DeliverType.MEDICAL_INSURANCE.getCode().toString())) {
            if (Objects.isNull(periodMonth)) {
                customer.setMedicalSecurityResult("账期不存在");
                customer.setIsSuccess(false);
            } else {
                if (Objects.isNull(customerDeliver)) {
                    customer.setMedicalSecurityResult("交付单不存在");
                    customer.setIsSuccess(false);
                } else {
                    if (customerDeliver.getHasChanged()) {
                        customer.setMedicalSecurityResult("交付变更待确认");
                        customer.setIsSuccess(false);
                    } else {
                        if (!Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_0.getCode()) && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_1.getCode())) {
                            customer.setMedicalSecurityResult("交付单状态不符");
                            customer.setIsSuccess(false);
                        } else {
                            customer.setMedicalSecurityDeliverId(customerDeliver.getId());
                        }
                    }
                }
            }
        } else if (Objects.equals(commonReportVO.getDeliverType(), DeliverType.SOCIAL_INSURANCE.getCode().toString())) {
            if (Objects.isNull(periodMonth)) {
                customer.setSocialSecurityResult("账期不存在");
                customer.setIsSuccess(false);
            } else {
                if (Objects.isNull(customerDeliver)) {
                    customer.setSocialSecurityResult("交付单不存在");
                    customer.setIsSuccess(false);
                } else {
                    if (customerDeliver.getHasChanged()) {
                        customer.setSocialSecurityResult("交付变更待确认");
                        customer.setIsSuccess(false);
                    } else {
                        if (!Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_0.getCode()) && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_1.getCode())) {
                            customer.setSocialSecurityResult("交付单状态不符");
                            customer.setIsSuccess(false);
                        } else {
                            customer.setSocialSecurityDeliverId(customerDeliver.getId());
                        }
                    }
                }
            }
        } else if (Objects.equals(commonReportVO.getDeliverType(), DeliverType.TAX.getCode().toString())) {
            if (Objects.isNull(periodMonth)) {
                customer.setPersonTaxResult("账期不存在");
                customer.setIsSuccess(false);
            } else {
                if (Objects.isNull(customerDeliver)) {
                    customer.setPersonTaxResult("交付单不存在");
                    customer.setIsSuccess(false);
                } else {
                    if (customerDeliver.getHasChanged()) {
                        customer.setPersonTaxResult("交付变更待确认");
                        customer.setIsSuccess(false);
                    } else {
                        if (!Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_0.getCode()) && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_1.getCode())) {
                            customer.setPersonTaxResult("交付单状态不符");
                            customer.setIsSuccess(false);
                        } else {
                            customer.setPersonTaxDeliverId(customerDeliver.getId());
                        }
                    }
                }
            }
        } else if (Objects.equals(commonReportVO.getDeliverType(), DeliverType.TAX_OPERATING_INCOME.getCode().toString())) {
            if (Objects.isNull(periodMonth)) {
                customer.setTaxOperatingResult("账期不存在");
                customer.setIsSuccess(false);
            } else {
                if (Objects.isNull(customerDeliver)) {
                    customer.setTaxOperatingResult("交付单不存在");
                    customer.setIsSuccess(false);
                } else {
                    if (customerDeliver.getHasChanged()) {
                        customer.setTaxOperatingResult("交付变更待确认");
                        customer.setIsSuccess(false);
                    } else {
                        if (!Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_0.getCode()) && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_1.getCode())) {
                            customer.setTaxOperatingResult("交付单状态不符");
                            customer.setIsSuccess(false);
                        } else {
                            customer.setTaxOperatingDeliverId(customerDeliver.getId());
                        }
                    }
                }
            }
        } else {
            if (Objects.isNull(periodMonth)) {
                customer.setNationalTaxResult("账期不存在");
                customer.setIsSuccess(false);
            } else {
                if (Objects.isNull(customerDeliver)) {
                    customer.setNationalTaxResult("交付单不存在");
                    customer.setIsSuccess(false);
                } else {
                    if (customerDeliver.getHasChanged()) {
                        customer.setNationalTaxResult("交付变更待确认");
                        customer.setIsSuccess(false);
                    } else {
                        if (!Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_0.getCode()) && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_1.getCode())) {
                            customer.setNationalTaxResult("交付单状态不符");
                            customer.setIsSuccess(false);
                        } else {
                            customer.setNationalTaxDeliverId(customerDeliver.getId());
                        }
                    }
                }
            }
        }
        openApiSyncCustomerService.save(customer);
        return record.getId();
    }

    private Long createTaxCheckRecord(XqyReportVO xqyReportVO) {
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(xqyReportVO.getBatchNo());
        record.setSourceType(OpenApiAppRelations.XQY.getId());
        record.setPeriod(null);
        record.setCreateBy(xqyReportVO.getOperateName());
        record.setType(xqyReportVO.getOperateType());
        if (ObjectUtils.isEmpty(xqyReportVO.getTaxNumberList())) {
            log.info("鑫启易申报数据异常，本次申报税号列表为空");
            record.setErrorReason("数据异常，本次申报税号列表为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getOperateName())) {
            log.info("鑫启易申报数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        openApiSyncRecordService.save(record);
        List<CCustomerService> customerServices = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .in(CCustomerService::getCreditCode, xqyReportVO.getTaxNumberList()).eq(CCustomerService::getServiceStatus, ServiceStatus.SERVICE.getCode())
                .eq(CCustomerService::getIsDel, false));
        Map<String, CCustomerService> customerServiceMap = customerServices.stream().collect(Collectors.toMap(CCustomerService::getCreditCode, Function.identity(), (v1, v2) -> v1));
        List<CustomerServicePeriodMonth> periodList = ObjectUtils.isEmpty(customerServices) ? Lists.newArrayList() :
                customerServicePeriodMonthService.list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getCustomerServiceId, customerServices.stream().map(CCustomerService::getId).collect(Collectors.toList()))
                .in(CustomerServicePeriodMonth::getPeriod, DateUtils.getNowPeriod(), DateUtils.getPrePeriod()));
        Map<Long, List<CustomerServicePeriodMonth>> periodMap = periodList.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCustomerServiceId));
        List<OpenApiSyncItem> items = Lists.newArrayList();
        xqyReportVO.getTaxNumberList().forEach(taxNumber -> {
            // 循环查询每个税号的申报情况
            OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
            customer.setTaxNumber(taxNumber);
            customer.setSycRecordId(record.getId());
            customer.setIsSuccess(true);
            try {
                List<Map<String, Object>> result = remoteThirdpartService.xqyQueryTaxDeclarationData(taxNumber, DateUtils.periodToYeaMonth(DateUtils.getNowPeriod())).getDataThrowException();
                log.info("鑫启易申报查询数据结果:{}，税号为：{}，申报周期：{}，操作人：{}", result, taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
                if (!ObjectUtils.isEmpty(result)) {
                    String customerName = String.valueOf(result.get(0).get("客户名称"));
                    customer.setCustomerName(customerName);
                    CCustomerService customerService = customerServiceMap.get(taxNumber);
                    if (Objects.isNull(customerService)) {
                        customer.setTaxCheckError("服务不存在");
                        customer.setIsSuccess(false);
                    } else {
                        customer.setCustomerServiceId(customerService.getId());
                        List<CustomerServicePeriodMonth> periodMonths = periodMap.get(customerService.getId());
                        if (!ObjectUtils.isEmpty(periodMonths)) {
                            customer.setPeriodIds(periodMonths.stream().map(row -> row.getId().toString()).collect(Collectors.joining(",")));
                        }
                    }
                    openApiSyncCustomerService.saveOrUpdate(customer);
                    for (Map<String, Object> map : result) {
                        if (Objects.isNull(map.get("申报情况"))) {
                            continue;
                        }
                        Map<String, Object> subMap = JSONObject.parseObject(map.get("申报情况").toString(), Map.class);
                        if (Objects.isNull(subMap.get("申报数据"))) {
                            continue;
                        }
                        List<Map<String, Object>> subList = JSONObject.parseObject(subMap.get("申报数据").toString(), List.class);
                        subList.forEach(reportList -> {
                            OpenApiSyncItem item = new OpenApiSyncItem();
                            item.setSycRecordId(record.getId());
                            item.setSyncCustomerId(customer.getId());
                            item.setCustomerName(customerName);
                            item.setTaxNumber(taxNumber);
                            item.setItemCategoryName(Objects.isNull(reportList.get("征收项目")) ? null : reportList.get("征收项目").toString());
                            item.setItemName(Objects.isNull(reportList.get("征收品目")) ? null : reportList.get("征收品目").toString());
                            item.setActualPayTaxAmount(Objects.isNull(reportList.get("实际应纳税额")) ? null : reportList.get("实际应纳税额").toString());
                            item.setIsReport(Objects.isNull(reportList.get("是否申报")) ? null : reportList.get("是否申报").toString());
                            item.setIsPaid(Objects.isNull(reportList.get("是否缴款")) ? null : reportList.get("是否缴款").toString());
                            item.setTaxPeriodStart(Objects.isNull(reportList.get("税款所属期起")) ? null : reportList.get("税款所属期起").toString());
                            item.setTaxPeriodEnd(Objects.isNull(reportList.get("税款所属期止")) ? null : reportList.get("税款所属期止").toString());
                            item.setReportPeriod(Objects.isNull(reportList.get("申报期")) ? null : reportList.get("申报期").toString());
                            item.setReportType(Objects.isNull(reportList.get("纳税期限")) ? null : reportList.get("纳税期限").toString());
                            item.setReportDate(Objects.isNull(reportList.get("申报日期")) ? null : reportList.get("申报日期").toString());
                            items.add(item);
                        });
                    }
                } else {
                    customer.setNationalTaxResult("未查询到申报数据");
                    customer.setPersonTaxResult("未查询到申报数据");
                    customer.setSocialSecurityResult("未查询到申报数据");
                    customer.setMedicalSecurityResult("未查询到申报数据");
                    customer.setTaxOperatingResult("未查询到申报数据");
                    customer.setIsSuccess(false);
                    openApiSyncCustomerService.saveOrUpdate(customer);
                }
            } catch (Exception e) {
                log.error("鑫启易申报查询数据异常:{}，税号为：{}，申报周期：{}，操作人：{}", e.getMessage(), taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
                customer.setNationalTaxResult("申报查询数据异常:" + e.getMessage());
                customer.setPersonTaxResult("申报查询数据异常:" + e.getMessage());
                customer.setSocialSecurityResult("申报查询数据异常:" + e.getMessage());
                customer.setMedicalSecurityResult("申报查询数据异常:" + e.getMessage());
                customer.setTaxOperatingResult("申报查询数据异常:" + e.getMessage());
                customer.setIsSuccess(false);
                openApiSyncCustomerService.saveOrUpdate(customer);
            }
        });
        if (!ObjectUtils.isEmpty(items)) {
            openApiSyncItemService.saveBatch(items);
        }
        return record.getId();
    }

    private Long createTaxCheckRecordV2(XqyReportVO xqyReportVO) {
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(xqyReportVO.getBatchNo());
        record.setSourceType(OpenApiAppRelations.XQY.getId());
        record.setPeriod(null);
        record.setCreateBy(xqyReportVO.getOperateName());
        record.setType(xqyReportVO.getOperateType());
        if (ObjectUtils.isEmpty(xqyReportVO.getTaxNumberList())) {
            log.info("鑫启易申报数据异常，本次申报税号列表为空");
            record.setErrorReason("数据异常，本次申报税号列表为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getOperateName())) {
            log.info("鑫启易申报数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        openApiSyncRecordService.save(record);
        List<CCustomerService> customerServices = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .in(CCustomerService::getCreditCode, xqyReportVO.getTaxNumberList()).eq(CCustomerService::getServiceStatus, ServiceStatus.SERVICE.getCode())
                .eq(CCustomerService::getIsDel, false));
        Map<String, CCustomerService> customerServiceMap = customerServices.stream().collect(Collectors.toMap(CCustomerService::getCreditCode, Function.identity(), (v1, v2) -> v1));
        List<CustomerServicePeriodMonth> periodList = ObjectUtils.isEmpty(customerServices) ? Lists.newArrayList() :
                customerServicePeriodMonthService.list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getCustomerServiceId, customerServices.stream().map(CCustomerService::getId).collect(Collectors.toList()))
                .in(CustomerServicePeriodMonth::getPeriod, DateUtils.getNowPeriod(), DateUtils.getPrePeriod()));
        Map<Long, List<CustomerServicePeriodMonth>> periodMap = periodList.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCustomerServiceId));
        List<OpenApiSyncItem> items = Lists.newArrayList();
        xqyReportVO.getTaxNumberList().forEach(taxNumber -> {
            // 循环查询每个税号的申报情况
            OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
            customer.setTaxNumber(taxNumber);
            customer.setSycRecordId(record.getId());
            customer.setIsSuccess(true);
            try {
                List<Map<String, Object>> result = remoteThirdpartService.xqyTaxItemConfirmQuery(taxNumber).getDataThrowException();
                log.info("鑫启易税种认定查询数据结果:{}，税号为：{}，操作人：{}", result, taxNumber, xqyReportVO.getOperateName());
                if (!ObjectUtils.isEmpty(result)) {
                    CCustomerService customerService = customerServiceMap.get(taxNumber);
                    String customerName = Objects.isNull(customerService) ? "" : customerService.getCustomerName();
                    customer.setCustomerName(customerName);
                    if (Objects.isNull(customerService)) {
                        customer.setTaxCheckError("服务不存在");
                        customer.setIsSuccess(false);
                    } else {
                        customer.setCustomerServiceId(customerService.getId());
                        List<CustomerServicePeriodMonth> periodMonths = periodMap.get(customerService.getId());
                        if (!ObjectUtils.isEmpty(periodMonths)) {
                            customer.setPeriodIds(periodMonths.stream().map(row -> row.getId().toString()).collect(Collectors.joining(",")));
                        }
                    }
                    openApiSyncCustomerService.saveOrUpdate(customer);
                    for (Map<String, Object> map : result) {
                        OpenApiSyncItem item = new OpenApiSyncItem();
                        item.setSycRecordId(record.getId());
                        item.setSyncCustomerId(customer.getId());
                        item.setCustomerName(customerName);
                        item.setTaxNumber(taxNumber);
                        item.setItemCategoryName(Objects.isNull(map.get("征收项目")) ? null : map.get("征收项目").toString());
                        item.setItemName(Objects.isNull(map.get("征收品目")) ? null : map.get("征收品目").toString());
                        item.setTaxPeriodStart(Objects.isNull(map.get("认定有效期起")) ? null : map.get("认定有效期起").toString());
                        item.setTaxPeriodEnd(Objects.isNull(map.get("认定有效期止")) ? null : map.get("认定有效期止").toString());
                        item.setReportType(Objects.isNull(map.get("纳税期限")) ? null : map.get("纳税期限").toString());
                        item.setDeclarationDeadline(Objects.isNull(map.get("申报期限")) ? null : map.get("申报期限").toString());
                        items.add(item);
                    }
                } else {
                    customer.setNationalTaxResult("未查询到税种认定数据");
                    customer.setPersonTaxResult("未查询到税种认定数据");
                    customer.setSocialSecurityResult("未查询到税种认定数据");
                    customer.setMedicalSecurityResult("未查询到税种认定数据");
                    customer.setTaxOperatingResult("未查询到税种认定数据");
                    customer.setIsSuccess(false);
                    openApiSyncCustomerService.saveOrUpdate(customer);
                }
            } catch (Exception e) {
                log.error("鑫启易税种认定查询数据异常:{}，税号为：{}，操作人：{}", e.getMessage(), taxNumber, xqyReportVO.getOperateName());
                customer.setNationalTaxResult("税种认定查询数据异常:" + e.getMessage());
                customer.setPersonTaxResult("税种认定查询数据异常:" + e.getMessage());
                customer.setSocialSecurityResult("税种认定查询数据异常:" + e.getMessage());
                customer.setMedicalSecurityResult("税种认定查询数据异常:" + e.getMessage());
                customer.setTaxOperatingResult("税种认定查询数据异常:" + e.getMessage());
                customer.setIsSuccess(false);
                openApiSyncCustomerService.saveOrUpdate(customer);
            }
        });
        if (!ObjectUtils.isEmpty(items)) {
            openApiSyncItemService.saveBatch(items);
        }
        return record.getId();
    }

    private CustomerDeliver getByDeliverType(Integer deliverType, Map<Integer, CustomerDeliver> deliverTypeMap) {
        return deliverTypeMap.getOrDefault(deliverType, null);
    }

    public void dealBySyncRecordId(Long recordId, String operName, String reportPeriod, String deliverType, Long deptId) {
        List<OpenApiSyncCustomer> syncCustomerList = openApiSyncCustomerService.selectBySyncRecordId(recordId);
        if (ObjectUtils.isEmpty(syncCustomerList)) {
            log.info("本次申报无客户信息,recordId:{}", recordId);
            return;
        }
        Map<Long, OpenApiSyncCustomer> syncCustomerMap = syncCustomerList.stream().collect(Collectors.toMap(OpenApiSyncCustomer::getId, Function.identity()));
        Map<Long, List<OpenApiSyncItem>> syncItemMap = openApiSyncItemService.selectBySyncRecordId(recordId)
                .stream().collect(Collectors.groupingBy(OpenApiSyncItem::getSyncCustomerId));
        Map<Long, List<OpenApiSyncStatementDetail>> detailMap = openApiSyncStatementDetailService.selectBySyncRecordId(recordId)
                .stream().collect(Collectors.groupingBy(OpenApiSyncStatementDetail::getSyncCustomerId));
        if (ObjectUtils.isEmpty(syncItemMap)) {
            log.info("本次申报无申报数据,recordId:{}", recordId);
            return;
        }
        List<Long> deliverIds = Lists.newArrayList();
        for (OpenApiSyncCustomer openApiSyncCustomer : syncCustomerList) {
            if (!Objects.isNull(openApiSyncCustomer.getNationalTaxDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getNationalTaxDeliverId());
            }
            if (!Objects.isNull(openApiSyncCustomer.getPersonTaxDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getPersonTaxDeliverId());
            }
            if (!Objects.isNull(openApiSyncCustomer.getSocialSecurityDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getSocialSecurityDeliverId());
            }
            if (!Objects.isNull(openApiSyncCustomer.getMedicalSecurityDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getMedicalSecurityDeliverId());
            }
            if (!Objects.isNull(openApiSyncCustomer.getTaxOperatingDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getTaxOperatingDeliverId());
            }
        }
        List<CustomerDeliver> customerDelivers = ObjectUtils.isEmpty(deliverIds) ? Lists.newArrayList() :
                customerDeliverService.list(new LambdaQueryWrapper<CustomerDeliver>().in(CustomerDeliver::getId, deliverIds).eq(CustomerDeliver::getIsDel, false));
        Map<Long, CustomerDeliver> deliverMap = customerDelivers.stream().collect(Collectors.toMap(CustomerDeliver::getId, Function.identity()));
        asyncService.asyncOpenApiReport(syncCustomerMap, syncItemMap, detailMap, deliverMap, operName, reportPeriod, deliverType, deptId);
    }

    public void dealTaxCheckBySyncRecordId(Long recordId, String operName, Long deptId) {
        List<OpenApiSyncCustomer> syncCustomerList = openApiSyncCustomerService.selectBySyncRecordId(recordId).stream().filter(row -> !Objects.isNull(row.getCustomerServiceId())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(syncCustomerList)) {
            log.info("本次税种核定无客户信息,recordId:{}", recordId);
            return;
        }
        List<Long> customerServiceIds = Lists.newArrayList();
        List<Long> customerServicePeriodMonthIds = Lists.newArrayList();
        Map<Long, List<Long>> customerServicePeriodMonthMap = new HashMap<>();
        for (OpenApiSyncCustomer syncCustomer : syncCustomerList) {
            List<Long> periodIds = StringUtils.isEmpty(syncCustomer.getPeriodIds()) ? Lists.newArrayList() :
                    Arrays.stream(syncCustomer.getPeriodIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(periodIds)) {
                customerServicePeriodMonthIds.addAll(periodIds);
            }
            if (!Objects.isNull(syncCustomer.getCustomerServiceId())) {
                customerServiceIds.add(syncCustomer.getCustomerServiceId());
                customerServicePeriodMonthMap.put(syncCustomer.getCustomerServiceId(), periodIds);
            }
        }
        Map<Long, OpenApiSyncCustomer> syncCustomerMap = syncCustomerList.stream().collect(Collectors.toMap(OpenApiSyncCustomer::getId, Function.identity()));
        Map<Long, List<OpenApiSyncItem>> syncItemMap = openApiSyncItemService.selectBySyncRecordId(recordId)
                .stream().collect(Collectors.groupingBy(OpenApiSyncItem::getSyncCustomerId));
        if (ObjectUtils.isEmpty(syncItemMap)) {
            log.info("本次税种核定无数据,recordId:{}", recordId);
            return;
        }
        asyncService.asyncOpenApiTaxCheck(syncCustomerMap, syncItemMap, operName, customerServiceIds, customerServicePeriodMonthIds, customerServicePeriodMonthMap, deptId);
    }

    public void supplementFile(XqySupplementVO xqySupplementVO) {
        if (ObjectUtils.isEmpty(xqySupplementVO.getFiles())) {
            return;
        }
        xqySupplementVO.getFiles().forEach(f -> f.setDeliverType(f.getOfficalFilename().contains("社会保险费") || f.getOfficalFilename().contains("社保") ? DeliverType.SOCIAL_INSURANCE.getCode() : DeliverType.NATIONAL_TAX.getCode()));
        List<OpenApiSupplementRecord> records = Lists.newArrayList();
        xqySupplementVO.getFiles().forEach(f -> {
            OpenApiSupplementRecord supplementRecord = new OpenApiSupplementRecord();
            BeanUtils.copyProperties(xqySupplementVO, supplementRecord);
            supplementRecord.setSourceType(1);
            supplementRecord.setCreateBy(xqySupplementVO.getOperateName());
            supplementRecord.setReportPeriod(f.getReportPeriod());
            supplementRecord.setTaxPeriodStart(f.getTaxPeriodStart());
            supplementRecord.setTaxPeriodEnd(f.getTaxPeriodEnd());
            supplementRecord.setFileId(f.getFileId());
            supplementRecord.setFileName(f.getFileName());
            supplementRecord.setOfficalFilename(f.getOfficalFilename());
            if (Objects.equals(f.getDeliverType(), DeliverType.SOCIAL_INSURANCE.getCode())) {
                Integer period = DateUtils.yearMonthToPeriod(f.getReportPeriod());
                CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                        .eq(CustomerServicePeriodMonth::getTaxNumber, xqySupplementVO.getTaxNumber())
                        .eq(CustomerServicePeriodMonth::getPeriod, period), false);
                if (!Objects.isNull(periodMonth)) {
                    // 社保找不到 找医保
                    CustomerDeliver customerDeliver = customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                            .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodMonth.getId())
                            .eq(CustomerDeliver::getDeliverType, DeliverType.SOCIAL_INSURANCE.getCode())
                            .eq(CustomerDeliver::getIsDel, false), false);
                    if (Objects.isNull(customerDeliver)) {
                        customerDeliver = customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                                .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodMonth.getId())
                                .eq(CustomerDeliver::getDeliverType, DeliverType.MEDICAL_INSURANCE.getCode())
                                .eq(CustomerDeliver::getIsDel, false), false);
                    }
                    if (!Objects.isNull(customerDeliver)) {
                        supplementRecord.setDeliverId(customerDeliver.getId());
                    }
                }
            } else {
                String month = f.getReportPeriod().split("-")[1];
                String prePeriod;
                if (Objects.equals(month, "01")) {
                    prePeriod = (Integer.parseInt(f.getReportPeriod().split("-")[0]) - 1) + "12";
                } else {
                    prePeriod = DateUtils.yearMonthToPeriod(f.getReportPeriod()) - 1 + "";
                }
                Integer prePeriodInt = Integer.parseInt(prePeriod);
                CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                        .eq(CustomerServicePeriodMonth::getTaxNumber, xqySupplementVO.getTaxNumber())
                        .eq(CustomerServicePeriodMonth::getPeriod, prePeriodInt), false);
                if (!Objects.isNull(periodMonth)) {
                    // 国税
                    CustomerDeliver customerDeliver = customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                            .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodMonth.getId())
                            .eq(CustomerDeliver::getDeliverType, DeliverType.NATIONAL_TAX.getCode())
                            .eq(CustomerDeliver::getIsDel, false), false);
                    if (!Objects.isNull(customerDeliver)) {
                        supplementRecord.setDeliverId(customerDeliver.getId());
                    }
                }
            }
            records.add(supplementRecord);
        });
        if (!ObjectUtils.isEmpty(records)) {
            openApiSupplementRecordService.saveBatch(records);
            List<OpenApiSupplementRecord> waitDealRecords = records.stream().filter(r -> !Objects.isNull(r.getDeliverId())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(waitDealRecords)) {
                asyncService.asyncOpenApiSupplement(waitDealRecords, xqySupplementVO.getOperateName(), xqySupplementVO.getDeptId());
            }
        }
    }

    public void updateTaxCheck(XqyReportVO reportVO) {
        Long recordId = createTaxCheckRecord(reportVO);
        dealTaxCheckBySyncRecordId(recordId, reportVO.getOperateName(), reportVO.getDeptId());
        updateAsyncRecordDataCount(recordId);
    }

    public void updateTaxCheckV2(XqyReportVO reportVO) {
        Long recordId = createTaxCheckRecordV2(reportVO);
        dealTaxCheckBySyncRecordId(recordId, reportVO.getOperateName(), reportVO.getDeptId());
        updateAsyncRecordDataCount(recordId);
    }

    public void commonReport(CommonReportVO commonReportVO) {
        Long recordId = saveOpenApiSyncRecord(commonReportVO);
        dealCommonReportBySyncRecordId(recordId, commonReportVO);
        updateAsyncRecordDataCount(recordId);
    }

    private void dealCommonReportBySyncRecordId(Long recordId, CommonReportVO commonReportVO) {
        List<OpenApiSyncCustomer> syncCustomerList = openApiSyncCustomerService.selectBySyncRecordId(recordId);
        if (ObjectUtils.isEmpty(syncCustomerList)) {
            log.info("本次申报无客户信息,recordId:{}", recordId);
            return;
        }
        OpenApiSyncCustomer syncCustomer = syncCustomerList.get(0);
        List<Long> deliverIds = Lists.newArrayList();
        for (OpenApiSyncCustomer openApiSyncCustomer : syncCustomerList) {
            if (!Objects.isNull(openApiSyncCustomer.getNationalTaxDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getNationalTaxDeliverId());
            }
            if (!Objects.isNull(openApiSyncCustomer.getPersonTaxDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getPersonTaxDeliverId());
            }
            if (!Objects.isNull(openApiSyncCustomer.getSocialSecurityDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getSocialSecurityDeliverId());
            }
            if (!Objects.isNull(openApiSyncCustomer.getMedicalSecurityDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getMedicalSecurityDeliverId());
            }
            if (!Objects.isNull(openApiSyncCustomer.getTaxOperatingDeliverId())) {
                deliverIds.add(openApiSyncCustomer.getTaxOperatingDeliverId());
            }
        }
        List<CustomerDeliver> customerDelivers = ObjectUtils.isEmpty(deliverIds) ? Lists.newArrayList() :
                customerDeliverService.list(new LambdaQueryWrapper<CustomerDeliver>().in(CustomerDeliver::getId, deliverIds).eq(CustomerDeliver::getIsDel, false));
        if (ObjectUtils.isEmpty(customerDelivers)) {
            log.info("本次申报交付单信息,recordId:{}", recordId);
            return;
        }
        CustomerDeliver customerDeliver = customerDelivers.get(0);
        asyncService.asyncOpenApiCommonReport(syncCustomer, customerDeliver, commonReportVO);
    }

    public void commonSupplementFile(XqySupplementVO xqySupplementVO) {
        List<OpenApiSupplementRecord> records = Lists.newArrayList();
        Long deliverId = null;
        Integer period = DateUtils.yearMonthToPeriod(xqySupplementVO.getReportPeriod());
        CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getTaxNumber, xqySupplementVO.getTaxNumber())
                .eq(CustomerServicePeriodMonth::getPeriod, period), false);
        if (!Objects.isNull(periodMonth)) {
            CustomerDeliver customerDeliver = customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                    .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodMonth.getId())
                    .eq(CustomerDeliver::getDeliverType, xqySupplementVO.getDeliverType())
                    .eq(CustomerDeliver::getIsDel, false), false);
            if (!Objects.isNull(customerDeliver)) {
                deliverId = customerDeliver.getId();
            }
        }
        if (ObjectUtils.isEmpty(xqySupplementVO.getFiles())) {
            OpenApiSupplementRecord supplementRecord = new OpenApiSupplementRecord();
            BeanUtils.copyProperties(xqySupplementVO, supplementRecord);
            supplementRecord.setSourceType(xqySupplementVO.getSource());
            supplementRecord.setCreateBy(xqySupplementVO.getOperateName());
            supplementRecord.setReportPeriod(xqySupplementVO.getReportPeriod());
            supplementRecord.setDeliverId(deliverId);
        } else {
            for (XqyFileVO file : xqySupplementVO.getFiles()) {
                OpenApiSupplementRecord supplementRecord = new OpenApiSupplementRecord();
                BeanUtils.copyProperties(xqySupplementVO, supplementRecord);
                supplementRecord.setSourceType(xqySupplementVO.getSource());
                supplementRecord.setCreateBy(xqySupplementVO.getOperateName());
                supplementRecord.setReportPeriod(xqySupplementVO.getReportPeriod());
                supplementRecord.setDeliverId(deliverId);
                supplementRecord.setFileId(file.getFileId());
                supplementRecord.setFileName(file.getFileName());
                supplementRecord.setOfficalFilename(file.getOfficalFilename());
                records.add(supplementRecord);
            }
        }
        if (!ObjectUtils.isEmpty(records)) {
            openApiSupplementRecordService.saveBatch(records);
            List<OpenApiSupplementRecord> waitDealRecords = records.stream().filter(r -> !Objects.isNull(r.getDeliverId()) && !StringUtils.isEmpty(r.getFileId())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(waitDealRecords)) {
                asyncService.asyncOpenApiCommonSupplement(waitDealRecords, xqySupplementVO.getOperateName(), xqySupplementVO.getSourceName(), xqySupplementVO.getFileType(), xqySupplementVO.getDeptId());
            }
        }
    }

    public void commonDeduction(CommonDeductionVO commonDeductionVO) {
        List<OpenApiDeductionRecord> records = Lists.newArrayList();
        Long deliverId = null;
        Integer period = DateUtils.yearMonthToPeriod(commonDeductionVO.getReportPeriod());
        CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getTaxNumber, commonDeductionVO.getTaxNumber())
                .eq(CustomerServicePeriodMonth::getPeriod, period), false);
        String errorResult = "";
        if (!Objects.isNull(periodMonth)) {
            CustomerDeliver customerDeliver = customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                    .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodMonth.getId())
                    .eq(CustomerDeliver::getDeliverType, commonDeductionVO.getDeliverType())
                    .eq(CustomerDeliver::getIsDel, false), false);
            if (!Objects.isNull(customerDeliver)) {
                deliverId = customerDeliver.getId();
                if (!Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_3.getCode()) && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_4.getCode())) {
                    errorResult = "交付单状态不符";
                }
            }
        }
        if (ObjectUtils.isEmpty(commonDeductionVO.getFiles())) {
            OpenApiDeductionRecord deductionRecord = new OpenApiDeductionRecord();
            BeanUtils.copyProperties(commonDeductionVO, deductionRecord);
            deductionRecord.setSourceType(commonDeductionVO.getSource());
            deductionRecord.setCreateBy(commonDeductionVO.getOperateName());
            deductionRecord.setReportPeriod(commonDeductionVO.getReportPeriod());
            deductionRecord.setDeliverId(deliverId);
            deductionRecord.setErrorResult(errorResult);
            records.add(deductionRecord);
        } else {
            for (XqyFileVO file : commonDeductionVO.getFiles()) {
                OpenApiDeductionRecord deductionRecord = new OpenApiDeductionRecord();
                BeanUtils.copyProperties(commonDeductionVO, deductionRecord);
                deductionRecord.setSourceType(commonDeductionVO.getSource());
                deductionRecord.setCreateBy(commonDeductionVO.getOperateName());
                deductionRecord.setReportPeriod(commonDeductionVO.getReportPeriod());
                deductionRecord.setDeliverId(deliverId);
                deductionRecord.setFileId(file.getFileId());
                deductionRecord.setFileName(file.getFileName());
                deductionRecord.setOfficalFilename(file.getOfficalFilename());
                deductionRecord.setErrorResult(errorResult);
                records.add(deductionRecord);
            }
        }
        if (!ObjectUtils.isEmpty(records)) {
            openApiDeductionRecordService.saveBatch(records);
            List<OpenApiDeductionRecord> waitDealRecords = records.stream().filter(r -> !Objects.isNull(r.getDeliverId()) && StringUtils.isEmpty(r.getErrorResult())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(waitDealRecords)) {
                asyncService.asyncOpenApiCommonDeduction(waitDealRecords, commonDeductionVO.getOperateName(), commonDeductionVO.getSourceName(), commonDeductionVO.getRemark(), commonDeductionVO.getDeptId());
            }
        }
    }

    public void preAuthAuth(XqyReportVO reportVO) {
        Long recordId = saveOpenApiSyncRecordVatData(reportVO);
        dealVatDataBySyncRecordId(recordId, reportVO.getOperateName(), reportVO.getReportPeriod(), reportVO.getDeliverType(), reportVO.getDeptId());
        updateAsyncRecordDataCount(recordId);
    }

    public void preAuthRemind(XqyReportVO reportVO) {
        Long recordId = saveOpenApiSyncRecordPreAuthData(reportVO);
        dealVatDataBySyncRecordId(recordId, reportVO.getOperateName(), reportVO.getReportPeriod(), reportVO.getDeliverType(), reportVO.getDeptId());
        updateAsyncRecordDataCount(recordId);
    }

    private void dealPreAuthRemindBySyncRecordId(Long recordId, String operateName, String reportPeriod, String deliverType, Long deptId) {
        List<OpenApiSyncCustomer> syncCustomerList = openApiSyncCustomerService.selectBySyncRecordId(recordId);
        if (ObjectUtils.isEmpty(syncCustomerList)) {
            log.info("本次申报无客户信息,recordId:{}", recordId);
            return;
        }
        OpenApiSyncCustomer syncCustomer = syncCustomerList.get(0);
        if (Objects.isNull(syncCustomer.getPreAuthDeliverId()) || !syncCustomer.getIsSuccess()) {
            log.info("未查询到预认证交付单或有异常,recordId:{}", recordId);
            return;
        }
        asyncService.asyncPreAuthRemind(syncCustomer, operateName, deptId);
    }

    private void dealVatDataBySyncRecordId(Long recordId, String operateName, String reportPeriod, String deliverType, Long deptId) {
        List<OpenApiSyncCustomer> syncCustomerList = openApiSyncCustomerService.selectBySyncRecordId(recordId);
        if (ObjectUtils.isEmpty(syncCustomerList)) {
            log.info("本次申报无客户信息,recordId:{}", recordId);
            return;
        }
        OpenApiSyncCustomer syncCustomer = syncCustomerList.get(0);
        if (Objects.isNull(syncCustomer.getPreAuthDeliverId()) || !syncCustomer.getIsSuccess()) {
            log.info("未查询到预认证交付单或有异常,recordId:{}", recordId);
            return;
        }
        OpenApiSyncVatData vatData = openApiSyncVatDataService.selectBySyncCustomerId(syncCustomer.getId());
        if (Objects.isNull(vatData)) {
            log.info("本次无认证数据,recordId:{}", recordId);
            return;
        }
        asyncService.asyncPreAuthAuth(syncCustomer, vatData, operateName, deptId);
    }

    private Long saveOpenApiSyncRecordVatData(XqyReportVO xqyReportVO) {
        Integer period = StringUtils.isEmpty(xqyReportVO.getReportPeriod()) ? DateUtils.getNowPeriod() : DateUtils.yearMonthToPeriod(xqyReportVO.getReportPeriod());
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(xqyReportVO.getBatchNo());
        record.setSourceType(OpenApiAppRelations.XQY.getId());
        record.setPeriod(period);
        record.setDeliverType(xqyReportVO.getDeliverType());
        record.setCreateBy(xqyReportVO.getOperateName());
        record.setType(xqyReportVO.getOperateType());
        if (ObjectUtils.isEmpty(xqyReportVO.getTaxNumberList())) {
            log.info("鑫启易申报数据异常，本次申报税号列表为空");
            record.setErrorReason("数据异常，本次申报税号列表为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getReportPeriod())) {
            log.info("鑫启易申报数据异常，本次申报所属期为空");
            record.setErrorReason("数据异常，本次申报所属期为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getOperateName())) {
            log.info("鑫启易申报数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        String taxNumber = xqyReportVO.getTaxNumberList().get(0);
        openApiSyncRecordService.save(record);
        CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getPeriod, period)
                .eq(CustomerServicePeriodMonth::getCreditCode, taxNumber), false);
        CustomerDeliver preAuthDeliver = Objects.isNull(periodMonth) ? null :
                customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                                .eq(CustomerDeliver::getIsDel, false)
                                .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodMonth.getId())
                                .eq(CustomerDeliver::getDeliverType, DeliverType.PRE_AUTH.getCode()));
        OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
        customer.setTaxNumber(taxNumber);
        customer.setSycRecordId(record.getId());
        try {
            Map<String, Object> result = remoteThirdpartService.xqyQueryVATData(taxNumber, xqyReportVO.getReportPeriod(), null).getDataThrowException();
            log.info("鑫启易增值税涉税分析查询数据结果:{}，税号为：{}，所属期：{}，操作人：{}", result, taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
            if (!ObjectUtils.isEmpty(result)) {
                String customerName = String.valueOf(result.get("客户名称"));
                customer.setCustomerName(customerName);
                customer.setIsSuccess(true);
                if (Objects.isNull(periodMonth)) {
                    customer.setPreAuthResult("账期不存在");
                    customer.setIsSuccess(false);
                } else {
                    if (Objects.isNull(preAuthDeliver)) {
                        customer.setPreAuthResult("交付单不存在");
                        customer.setIsSuccess(false);
                    } else {
                        customer.setPreAuthDeliverId(preAuthDeliver.getId());
                        if (preAuthDeliver.getHasChanged()) {
                            customer.setPreAuthResult("交付变更待确认");
                            customer.setIsSuccess(false);
                        } else {
                            if (!Objects.equals(preAuthDeliver.getStatus(), DeliverStatus.STATUS_101.getCode())) {
                                customer.setPreAuthResult("交付单状态不符合");
                                customer.setIsSuccess(false);
                            }
                        }
                    }
                }
                openApiSyncCustomerService.save(customer);
                openApiSyncVatDataService.save(convertByVatDataResult(result, record.getId(), customer.getId()));
            } else {
                customer.setPreAuthResult("未查询到数据");
                customer.setIsSuccess(false);
                openApiSyncCustomerService.save(customer);
            }
        } catch (Exception e) {
            log.error("鑫启易增值税涉税分析查询数据异常:{}，税号为：{}，所属期：{}，操作人：{}", e.getMessage(), taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
            customer.setPreAuthResult("增值税涉税分析查询数据异常:" + e.getMessage());
            customer.setIsSuccess(false);
            openApiSyncCustomerService.save(customer);
        }
        return record.getId();
    }

    private Long saveOpenApiSyncRecordPreAuthData(XqyReportVO xqyReportVO) {
        Integer period = StringUtils.isEmpty(xqyReportVO.getReportPeriod()) ? DateUtils.getNowPeriod() : DateUtils.yearMonthToPeriod(xqyReportVO.getReportPeriod());
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(xqyReportVO.getBatchNo());
        record.setSourceType(OpenApiAppRelations.XQY.getId());
        record.setPeriod(period);
        record.setDeliverType(xqyReportVO.getDeliverType());
        record.setCreateBy(xqyReportVO.getOperateName());
        record.setType(xqyReportVO.getOperateType());
        record.setPurchaseInvoiceScope(xqyReportVO.getPurchaseInvoiceScope());
        if (ObjectUtils.isEmpty(xqyReportVO.getTaxNumberList())) {
            log.info("鑫启易申报数据异常，本次申报税号列表为空");
            record.setErrorReason("数据异常，本次申报税号列表为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getReportPeriod())) {
            log.info("鑫启易申报数据异常，本次申报所属期为空");
            record.setErrorReason("数据异常，本次申报所属期为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getOperateName())) {
            log.info("鑫启易申报数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        String taxNumber = xqyReportVO.getTaxNumberList().get(0);
        openApiSyncRecordService.save(record);
        CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getPeriod, period)
                .eq(CustomerServicePeriodMonth::getCreditCode, taxNumber), false);
        CustomerDeliver preAuthDeliver = Objects.isNull(periodMonth) ? null :
                customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodMonth.getId())
                        .eq(CustomerDeliver::getDeliverType, DeliverType.PRE_AUTH.getCode()));
        OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
        customer.setTaxNumber(taxNumber);
        customer.setSycRecordId(record.getId());
        customer.setPreAuthRemind(xqyReportVO.getNotificationText());
        try {
            Map<String, Object> result = remoteThirdpartService.xqyQueryVATData(taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getPurchaseInvoiceScope()).getDataThrowException();
            log.info("鑫启易增值税涉税分析查询数据结果:{}，税号为：{}，所属期：{}，操作人：{}", result, taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
            if (!ObjectUtils.isEmpty(result)) {
                String customerName = String.valueOf(result.get("客户名称"));
                customer.setCustomerName(customerName);
                customer.setIsSuccess(true);
                if (Objects.isNull(periodMonth)) {
                    customer.setPreAuthResult("账期不存在");
                    customer.setIsSuccess(false);
                } else {
                    if (Objects.isNull(preAuthDeliver)) {
                        customer.setPreAuthResult("交付单不存在");
                        customer.setIsSuccess(false);
                    } else {
                        customer.setPreAuthDeliverId(preAuthDeliver.getId());
                        if (preAuthDeliver.getHasChanged()) {
                            customer.setPreAuthResult("交付变更待确认");
                            customer.setIsSuccess(false);
                        }
//                        else {
//                            if (!Objects.equals(preAuthDeliver.getStatus(), DeliverStatus.STATUS_101.getCode())) {
//                                customer.setPreAuthResult("交付单状态不符合");
//                                customer.setIsSuccess(false);
//                            }
//                        }
                    }
                }
                openApiSyncCustomerService.save(customer);
                openApiSyncVatDataService.save(convertByVatDataResult(result, record.getId(), customer.getId()));
            } else {
                customer.setPreAuthResult("未查询到数据");
                customer.setIsSuccess(false);
                openApiSyncCustomerService.save(customer);
            }
        } catch (Exception e) {
            log.error("鑫启易增值税涉税分析查询数据异常:{}，税号为：{}，所属期：{}，操作人：{}", e.getMessage(), taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
            customer.setPreAuthResult("增值税涉税分析查询数据异常:" + e.getMessage());
            customer.setIsSuccess(false);
            openApiSyncCustomerService.save(customer);
        }
        return record.getId();
    }

    private OpenApiSyncVatData convertByVatDataResult(Map<String, Object> result, Long recordId, Long customerId) {
        return new OpenApiSyncVatData()
                .setSycRecordId(recordId)
                .setSyncCustomerId(customerId)
                .setCustomerId(getValueByKey(result, "客户ID"))
                .setCustomerCode(getValueByKey(result, "客户编号"))
                .setCustomerName(getValueByKey(result, "客户名称"))
                .setTaxNumber(getValueByKey(result, "税号"))
                .setTaxpayerQualification(getValueByKey(result, "纳税人资格"))
                .setIsTaxReminderSent(getValueByKey(result, "是否已发送纳税提醒"))
                .setPeriodStart(getValueByKey(result, "所属期起"))
                .setPeriodEnd(getValueByKey(result, "所属期止"))
                .setLastPeriodCarriedForwardTax(getValueByKey(result, "上期留抵税额"))
                .setCurrentMonthOrQuarterSalesAmount(getValueByKey(result, "本月(季)销售金额"))
                .setUnInvoicedIncome(getValueByKey(result, "其中：无票收入"))
                .setCurrentOutputTax(getValueByKey(result, "本期销项税额"))
                .setCurrentSimpleTaxCollection(getValueByKey(result, "本期简易征收税额"))
                .setCurrentCertifiedInputTax(getValueByKey(result, "本期已认证进项税额"))
                .setCurrentPendingInputTax(getValueByKey(result, "本期待确认进项税额"))
                .setCurrentSelectedInputTax(getValueByKey(result, "本期已勾选进项税额"))
                .setPendingInputTaxForCalculation(getValueByKey(result, "用于计税的待确认进项范围"))
                .setCurrentSelectedAndCertifiedInputTax(getValueByKey(result, "本期已勾选并确认的进项税额"))
                .setCurrentRedLetterInputTaxShouldRevert(getValueByKey(result, "本期红字进项应转出税额"))
                .setCurrentExpectedPayableVat(getValueByKey(result, "本期预计应缴增值税"))
                .setCurrentPaidVat(getValueByKey(result, "本期已缴增值税"))
                .setCurrentActualReportedInputTax(getValueByKey(result, "本期实际申报认证进项税额"))
                .setCurrentExpectedRemainingInputTax(getValueByKey(result, "本期预计结余可用进项税额"))
                .setCurrentInvoiceCount(getValueByKey(result, "本期开票张数"))
                .setCumulativeVatBurdenRateToLastPeriod(getValueByKey(result, "截止上期本年增值税税负率"))
                .setLastYearVatBurdenRate(getValueByKey(result, "上年增值税税负率"))
                .setCumulativeVatPaidToLastPeriod(getValueByKey(result, "截止上期本年累计缴纳增值税"))
                .setCumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth(getValueByKey(result, "截止上月本年累计加速加计扣除进项税额"))
                .setCumulativeTurnoverToLastPeriod(getValueByKey(result, "截止上期本年累计营业额"))
                .setCumulativeTurnoverToCurrentMonth(getValueByKey(result, "截止本月本年累计营业额"))
                .setCumulativeOutputTaxToCurrentMonth(getValueByKey(result, "截止本月本年累计销项税额"))
                .setCumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth(getValueByKey(result, "截止本月本年累计加速加计扣除进项税额"))
                .setCumulativeExpectedVatToCurrentMonth(getValueByKey(result, "截止本月本年累计预计缴纳增值税额"))
                .setEstimatedVatBurdenRateToCurrentMonth(getValueByKey(result, "截止本月预计增值税税负率"))
                .setCumulativeActualDeductedInputTaxToCurrentMonth(getValueByKey(result, "截止本月本年累计实际抵扣进项税额"))
                .setCumulativeInputInvoiceAmountExcludingTaxToCurrentMonth(getValueByKey(result, "截止本月本年累计进项发票金额（不含税）"))
                .setCumulativeInputInvoiceAmountIncludingTaxToCurrentMonth(getValueByKey(result, "截止本月本年累计进项发票金额（含税）"))
                .setCumulativeInvoicedAmountLast12Months(getValueByKey(result, "近12个月累计开票金额"))
                .setIsGeneralTaxpayer(getValueByKey(result, "是否一般纳税人"))
                .setIsInputOutputUpdated(getValueByKey(result, "进销项是否更新"))
                .setIsLastPeriodVatFormDownloaded(getValueByKey(result, "是否下载上期增值税申报表"))
                .setCurrentCertifiedInputAmount(getValueByKey(result, "本期已认证进项金额"))
                .setCurrentPendingInputAmount(getValueByKey(result, "本期待确认进项金额"))
                .setCurrentSelectedAndCertifiedInputAmount(getValueByKey(result, "本期已勾选并确认的进项金额"))
                .setFunctionStaff(getValueByKey(result, "职能人员"))
                .setOtherFunctionStaff(getValueByKey(result, "其他职能人员"))
                .setVatCompositeTaxRateSetting(getValueByKey(result, "增值税销项综合税率设置"))
                .setPlannedVatBurdenRate(getValueByKey(result, "增值税计划税负率"))
                .setCurrentMaxInvoiceLimitAmount(getValueByKey(result, "本期当前可开票上限总金额(含税)"))
                .setCurrentRemainingInvoiceLimitAmount(getValueByKey(result, "本期当前剩余可开票上限金额(含税)"))
                .setCurrentSuggestedVatPayment(getValueByKey(result, "本期当前应当或建议缴纳税额"))
                .setCurrentSuggestedCertifiedInputTax(getValueByKey(result, "本期当前应当或建议认证进项税额"))
                .setCurrentSuggestedAdditionalInputTaxAmount(getValueByKey(result, "本期当前建议补充含税进项金额"))
                .setVatDueAfterAdditionalInput(getValueByKey(result, "补充进项后当前应纳税额"))
                .setLastDownloadDate(getValueByKey(result, "最近下载日期"))
                .setLastMonthDownloadDate(getValueByKey(result, "所属月最后下载日期"))
                .setIsVatDeclaredThisPeriod(getValueByKey(result, "本期增值税是否申报"))
                .setPermanentRemark(getValueByKey(result, "永久备注"))
                .setCurrentRemark(getValueByKey(result, "本期备注"));
    }

    private String getValueByKey(Map<String, Object> result, String key) {
        Object obj = result.get(key);
        return Objects.isNull(obj) ? null : obj.toString();
    }

    public void commonNotice(CommonNoticeVO commonNoticeVO) {
        String noticeCode = commonNoticeVO.getNoticeCode();
        OpenApiData openApiData = new OpenApiData().setType(CommonNoticeType.getByNoticeCode(noticeCode).getType())
                .setCustomerName(commonNoticeVO.getCustomerName())
                .setCreditCode(commonNoticeVO.getCreditCode())
                .setTaxNumber(commonNoticeVO.getTaxNumber())
                .setPeriod(commonNoticeVO.getPeriod())
                .setGroupId(commonNoticeVO.getGroupId())
                .setGroupName(commonNoticeVO.getGroupName())
                .setPlatType(commonNoticeVO.getPlatType())
                .setOperType(commonNoticeVO.getOperType())
                .setCustomerServiceId(commonNoticeVO.getCustomerServiceId())
                .setAllParam(JSONObject.toJSONString(commonNoticeVO))
                .setDataJson(JSONObject.toJSONString(commonNoticeVO.getNoticeParameter()));
        openApiData.setCreateBy(commonNoticeVO.getOperator());
        openApiData.setUuid(commonNoticeVO.getUuid());
        openApiData.setDeptId(commonNoticeVO.getDeptId());
        openApiDataService.save(openApiData);
        switch (noticeCode) {
            case "income":
                if (!StringUtils.isEmpty(openApiData.getTaxNumber())) {
                    asyncService.asyncIncomeNotice(openApiData);
                }
                break;
            case "invoice":
                // 暂不处理逻辑
                break;
            case "inAccount":
                // 入账交付单
                if (!StringUtils.isEmpty(openApiData.getTaxNumber()) && !Objects.isNull(openApiData.getPeriod())) {
                    asyncService.asyncInAccountNotice(openApiData);
                }
                break;
            case "inAccountV2":
                // 入账交付单V2
                if (!StringUtils.isEmpty(openApiData.getTaxNumber()) && !Objects.isNull(openApiData.getPeriod())) {
                    asyncService.asyncInAccountNoticeV2(openApiData);
                }
                break;
            case "inAccountV3":
                // 入账交付单V3
                if (!StringUtils.isEmpty(openApiData.getTaxNumber()) && !Objects.isNull(openApiData.getPeriod())) {
                    asyncService.asyncInAccountNoticeV3(openApiData);
                    redisService.deleteObject(String.format(CacheConstants.RPA_GET_PROFIT_KEY, commonNoticeVO.getCustomerServicePeriodMonthId()));
                }
                break;
            case "supplementAccountingFiles":
                // 补充入账交付单附件
                if (!StringUtils.isEmpty(openApiData.getTaxNumber()) && !Objects.isNull(openApiData.getPeriod())) {
                    asyncService.asyncSupplementAccountingFiles(openApiData);
                }
                break;
            case "taxCheck":
                // 税种核定
                if (!StringUtils.isEmpty(openApiData.getTaxNumber())) {
                    asyncService.asyncTaxCheckNotice(openApiData);
                }
                break;
            case "insuranceCheck":
                // 税种核定
                if (!StringUtils.isEmpty(openApiData.getTaxNumber())) {
                    asyncService.asyncInsuranceCheckNotice(openApiData);
                }
                break;
            case "invoiceUpdate":
                // 发票更新
                if (!StringUtils.isEmpty(openApiData.getTaxNumber())) {
                    if (Objects.isNull(commonNoticeVO.getIncomeResult()) && Objects.isNull(commonNoticeVO.getOutputResult())) {
                        asyncService.asyncInvoiceUpdate(openApiData, commonNoticeVO.getSourceName());
                    } else {
                        try {
                            asyncService.asyncInAccountUpdate(commonNoticeVO);
                        } catch (Exception e) {
                            openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                        } finally {
                            redisService.deleteObject(String.format(CacheConstants.RPA_INVOICE_UPDATE_KEY, commonNoticeVO.getCustomerServicePeriodMonthId()));
                        }
                    }
                }
                break;
            case "getBankReceiptFile":
                // 银企-提取
                if (!Objects.isNull(commonNoticeVO.getDeliverId())) {
                    try {
                        customerServiceCashierAccountingService.dealBanksEnterprisesExtract(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        redisService.deleteObject(String.format(CacheConstants.RPA_BANK_FLOW_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "checkBankReceiptFile":
                // 检验文件
                if (!Objects.isNull(commonNoticeVO.getDeliverId())) {
                    try {
                        customerServiceCashierAccountingService.dealFileCheck(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        if (!Objects.isNull(commonNoticeVO.getTaskId())) {
                            redisService.deleteObject("checkFile:checking:" + commonNoticeVO.getTaskId());
                        }
                        redisService.deleteObject(String.format(CacheConstants.RPA_BANK_FLOW_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "generateVoucher":
                // 生成凭证
                if (!Objects.isNull(commonNoticeVO.getDeliverId())) {
                    try {
                        customerServiceCashierAccountingService.dealGenerateVoucherV2(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        redisService.deleteObject(String.format(CacheConstants.RPA_BANK_FLOW_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "bankReceiptPaperFileUpload":
                // 纸质回单上传
                if (!Objects.isNull(commonNoticeVO.getDeliverId())) {
                    try {
                        customerServiceCashierAccountingService.dealBankReceiptPaperFileUpload(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        redisService.deleteObject(String.format(CacheConstants.RPA_BANK_FLOW_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "personalIncomeTtaxDeclaration":
                // 个税申报回调
                if (!Objects.isNull(commonNoticeVO.getDeliverId())) {
                    try {
                        customerDeliverService.dealPersonTaxReport(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        redisService.deleteObject(String.format(CacheConstants.PERSON_TAX_REPORT_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "personalIncomeTtaxDeduction":
                // 个税扣款回调
                if (!Objects.isNull(commonNoticeVO.getDeliverId())) {
                    try {
                        customerDeliverService.dealPersonTaxDeduction(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        redisService.deleteObject(String.format(CacheConstants.PERSON_TAX_DEDUCTION_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "personalIncomeTtaxCheck":
                // 个税检查回调
                if (!Objects.isNull(commonNoticeVO.getDeliverId())) {
                    try {
                        customerDeliverService.dealPersonTaxCheck(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        redisService.deleteObject(String.format(CacheConstants.PERSON_TAX_CHECK_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "personalIncomeTtaxDownload":
                // 个税申报表下载回调
                if (!Objects.isNull(commonNoticeVO.getDeliverId())) {
                    try {
                        customerDeliverService.dealPersonTaxTableDownload(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        redisService.deleteObject(String.format(CacheConstants.PERSON_TAX_TABLE_DOWNLOAD_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "personalIncomeTtaxCheckStatus":
                // 个税状态查询回调
                if (!Objects.isNull(commonNoticeVO.getDeliverId()) && !Objects.isNull(commonNoticeVO.getType())) {
                    try {
                        customerDeliverService.dealPersonTaxStatusSearch(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    } finally {
                        redisService.deleteObject(String.format(CacheConstants.PERSON_TAX_STATUS_SEARCH_KEY, commonNoticeVO.getDeliverId()));
                    }
                }
                break;
            case "personalIncomeTtaxDownloadCheck":
                // 申报表下载查询回调
                if (!ObjectUtils.isEmpty(commonNoticeVO.getList())) {
                    try {
                        customerDeliverService.dealReportTableDownload(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    }
                }
                break;
            case "qualityTesting":
                // 质检
                if (!ObjectUtils.isEmpty(commonNoticeVO.getNoticeParameter())) {
                    try {
//                        qualityCheckingResultService.dealQualityChecking(commonNoticeVO);
                        qualityCheckingResultService.dealQualityCheckingV2(commonNoticeVO);
                    } catch (Exception e) {
                        openApiNoticeRecordService.updateSysDealFailByUuid(openApiData.getUuid(), e.getMessage());
                    }
                }
                break;
            case "accruedSalary":
                // 自动结账
                if (!StringUtils.isEmpty(openApiData.getTaxNumber()) && !Objects.isNull(openApiData.getPeriod())) {
                    asyncService.asyncInAccountAutoSettlement(openApiData);
                    redisService.deleteObject(String.format(CacheConstants.RPA_AUTO_SETTLEMENT, commonNoticeVO.getCustomerServicePeriodMonthId()));
                }
                break;
            default:
                break;
        }
    }

    public void incomeUpdate(XqyReportVO reportVO) {
        Long recordId = saveOpenApiIncomeRecord(reportVO);
        dealBySyncIncomeRecordId(recordId, reportVO.getOperateName(), reportVO.getDeptId());
        updateAsyncRecordDataCount(recordId);
    }

    public void incomeUpdateV2(XqyReportVO xqyReportVO) {
        Long recordId = saveOpenApiIncomeRecordV2(xqyReportVO);
        dealBySyncIncomeRecordIdV2(recordId, xqyReportVO.getOperateName(), xqyReportVO.getDeptId());
        updateAsyncRecordDataCount(recordId);
    }

    private void dealBySyncIncomeRecordIdV2(Long recordId, String operateName, Long deptId) {
        List<OpenApiSyncCustomer> syncCustomerList = openApiSyncCustomerService.selectBySyncRecordId(recordId);
        if (ObjectUtils.isEmpty(syncCustomerList)) {
            log.info("本次申报无客户信息,recordId:{}", recordId);
            return;
        }
        List<OpenApiInvoiceData> openApiInvoiceData = openApiInvoiceDataService.selectListByRecordId(recordId);
        if (Objects.isNull(openApiInvoiceData)) {
            log.info("本次无发票数据,recordId:{}", recordId);
            return;
        }
        Map<Long, OpenApiInvoiceData> invoiceDataMap = openApiInvoiceData.stream().collect(Collectors.toMap(OpenApiInvoiceData::getSyncCustomerId, Function.identity(), (v1, v2) -> v1));
        Map<Long, OpenApiSyncVatData> vatDataMap = openApiSyncVatDataService.selectBySyncRecordId(recordId).stream().collect(Collectors.toMap(OpenApiSyncVatData::getSyncCustomerId, Function.identity(), (v1, v2) -> v1));
        for (OpenApiSyncCustomer syncCustomer : syncCustomerList) {
            if (Objects.isNull(syncCustomer.getIncomeId()) || !syncCustomer.getIsSuccess()) {
                log.info("未查询到收入数据或有异常,recordId:{}", recordId);
                continue;
            }
            OpenApiInvoiceData invoiceData = invoiceDataMap.get(syncCustomer.getId());
            if (Objects.isNull(invoiceData)) {
                log.info("未查询到发票信息,recordId:{}", recordId);
                continue;
            }
            // "销项不含税金额合计"=全量发票开票金额，"销项税额合计"=全量发票开票税额
            // "进项不含税金额合计"=取得发票金额，"进项税额合计"=取得发票税额
            String allTicketAmount = invoiceData.getOutputAmountExcludingTax();
            String allTicketTaxAmount = invoiceData.getOutputTaxAmount();
            String totalInvoiceAmount = invoiceData.getInputAmountExcludingTax();
            String totalInvoiceTaxAmount = invoiceData.getInputTaxAmount();
            OpenApiSyncVatData vatData = vatDataMap.get(syncCustomer.getId());
            String noTicketIncomeAmount = Objects.isNull(vatData) ? null : vatData.getUnInvoicedIncome();

            CustomerServicePeriodMonthIncome update = new CustomerServicePeriodMonthIncome();
            update.setId(syncCustomer.getIncomeId());
            update.setAllTicketTaxAmount(StringUtils.isEmpty(allTicketTaxAmount) ? BigDecimal.ZERO : new BigDecimal(allTicketTaxAmount));
            update.setAllTicketAmount(StringUtils.isEmpty(allTicketAmount) ? BigDecimal.ZERO : new BigDecimal(allTicketAmount));
            update.setTotalInvoiceAmount(StringUtils.isEmpty(totalInvoiceAmount) ? BigDecimal.ZERO : new BigDecimal(totalInvoiceAmount));
            update.setTotalInvoiceTaxAmount(StringUtils.isEmpty(totalInvoiceTaxAmount) ? BigDecimal.ZERO : new BigDecimal(totalInvoiceTaxAmount));
            update.setTicketTime(LocalDateTime.now());
            update.setInputInvoiceCount(invoiceData.getInputInvoiceCount());
            update.setOutputInvoiceCount(invoiceData.getOutputInvoiceCount());
            if (!StringUtils.isEmpty(noTicketIncomeAmount) && !Objects.equals("null", noTicketIncomeAmount)) {
                update.setNoTicketIncomeAmount(new BigDecimal(noTicketIncomeAmount));
            }
            customerServicePeriodMonthIncomeService.modifyPeriodMonthIncomeXqy(update, operateName, deptId);
        }
        // 更新收入
        customerServicePeriodMonthIncomeService.updateCustomerServiceIncome(syncCustomerList.get(0).getCustomerServiceId());
    }

    private Long saveOpenApiIncomeRecordV2(XqyReportVO xqyReportVO) {
        String reportPeriod = xqyReportVO.getStartDate().substring(0, 7);
        xqyReportVO.setReportPeriod(reportPeriod);
        Integer period = Integer.parseInt(reportPeriod.replace("-", ""));
        LocalDate start = LocalDate.parse(xqyReportVO.getStartDate().substring(0, 7) + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate end = LocalDate.parse(xqyReportVO.getEndDate().substring(0, 7) + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(xqyReportVO.getBatchNo());
        record.setSourceType(OpenApiAppRelations.XQY.getId());
        record.setPeriod(period);
        record.setCreateBy(xqyReportVO.getOperateName());
        record.setType(xqyReportVO.getOperateType());
        if (StringUtils.isEmpty(xqyReportVO.getTaxNumber())) {
            log.info("鑫启易发票查询数据异常，本次税号为空");
            record.setErrorReason("数据异常，本次税号为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getOperateName())) {
            log.info("鑫启易发票查询数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        String taxNumber = xqyReportVO.getTaxNumber();
        openApiSyncRecordService.save(record);
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getServiceStatus, ServiceStatus.SERVICE.getCode())
                .eq(CCustomerService::getTaxNumber, taxNumber).last("limit 1"));
        if (Objects.isNull(customerService)) {
            customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                    .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getServiceStatus, ServiceStatus.END.getCode())
                    .eq(CCustomerService::getTaxNumber, taxNumber).last("limit 1"));
        }
        while (!start.isAfter(end)) {
            Integer thisPeriod = Integer.parseInt(start.format(DateTimeFormatter.ofPattern("yyyyMM")));
            String thisStart = start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String thisEnd = start.plusMonths(1).minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            CustomerServicePeriodMonthIncome income = Objects.isNull(customerService) ? null : customerServicePeriodMonthIncomeService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>()
                    .eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerService.getId())
                    .eq(CustomerServicePeriodMonthIncome::getPeriod, thisPeriod), false);
            OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
            customer.setTaxNumber(taxNumber);
            customer.setSycRecordId(record.getId());
            customer.setCustomerServiceId(Objects.isNull(customerService) ? null : customerService.getId());
            try {
                Map<String, Object> result = remoteThirdpartService.xqyInvoiceStatistic(taxNumber, thisStart, thisEnd).getDataThrowException();
                log.info("鑫启易发票查询数据结果:{}，税号为：{}，开始时间：{}，结束时间:{}, 操作人：{}", result, taxNumber, xqyReportVO.getStartDate(), xqyReportVO.getEndDate(), xqyReportVO.getOperateName());
                if (!ObjectUtils.isEmpty(result)) {
                    String customerName = String.valueOf(result.get("客户名称"));
                    customer.setCustomerName(customerName);
                    customer.setIsSuccess(true);
                    if (Objects.isNull(income)) {
                        customer.setIncomeResult("收入记录不存在");
                        customer.setIsSuccess(false);
                    } else {
                        customer.setIncomeId(income.getId());
                    }
                    openApiSyncCustomerService.save(customer);
                    openApiInvoiceDataService.save(convertByInvoiceResult(result, record.getId(), customer.getId()));
                    if (Objects.equals(customerService.getTaxType(), TaxType.COMMONLY.getCode()) || Lists.newArrayList(3, 6, 9, 12).contains(thisPeriod % 100)) {
                        Map<String, Object> vatResult = remoteThirdpartService.xqyQueryVATData(taxNumber, start.format(DateTimeFormatter.ofPattern("yyyy-MM")), null).getDataThrowException();
                        log.info("鑫启易增值税涉税分析查询数据结果:{}，税号为：{}，所属期：{}，操作人：{}", vatResult, taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
                        if (!ObjectUtils.isEmpty(vatResult)) {
                            openApiSyncVatDataService.save(convertByVatDataResult(vatResult, record.getId(), customer.getId()));
                        }
                    }
                } else {
                    customer.setPreAuthResult("未查询到数据");
                    customer.setIsSuccess(false);
                    openApiSyncCustomerService.save(customer);
                }
            } catch (Exception e) {
                log.error("鑫启易增发票查询数据异常:{}，税号为：{}，开始时间：{}，结束时间:{}，操作人：{}", e.getMessage(), taxNumber, xqyReportVO.getStartDate(), xqyReportVO.getEndDate(), xqyReportVO.getOperateName());
                customer.setIncomeResult("发票查询数据异常:" + e.getMessage());
                customer.setIsSuccess(false);
                openApiSyncCustomerService.save(customer);
            }
            start = start.plusMonths(1);
        }
        return record.getId();
    }

    private void dealBySyncIncomeRecordId(Long recordId, String operateName, Long deptId) {
        List<OpenApiSyncCustomer> syncCustomerList = openApiSyncCustomerService.selectBySyncRecordId(recordId);
        if (ObjectUtils.isEmpty(syncCustomerList)) {
            log.info("本次申报无客户信息,recordId:{}", recordId);
            return;
        }
        OpenApiSyncCustomer syncCustomer = syncCustomerList.get(0);
        if (Objects.isNull(syncCustomer.getIncomeId()) || !syncCustomer.getIsSuccess()) {
            log.info("未查询到收入数据或有异常,recordId:{}", recordId);
            return;
        }
        OpenApiInvoiceData invoiceData = openApiInvoiceDataService.selectByRecordId(recordId);
        if (Objects.isNull(invoiceData)) {
            log.info("本次无发票数据,recordId:{}", recordId);
            return;
        }
        // "销项不含税金额合计"=全量发票开票金额，"销项税额合计"=全量发票开票税额
        // "进项不含税金额合计"=取得发票金额，"进项税额合计"=取得发票税额
        String allTicketAmount = invoiceData.getOutputAmountExcludingTax();
        String allTicketTaxAmount = invoiceData.getOutputTaxAmount();
        String totalInvoiceAmount = invoiceData.getInputAmountExcludingTax();
        String totalInvoiceTaxAmount = invoiceData.getInputTaxAmount();
        OpenApiSyncVatData vatData = openApiSyncVatDataService.selectBySyncCustomerId(syncCustomer.getId());
        String noTicketIncomeAmount = Objects.isNull(vatData) ? null : vatData.getUnInvoicedIncome();

        CustomerServicePeriodMonthIncome update = new CustomerServicePeriodMonthIncome();
        update.setId(syncCustomer.getIncomeId());
        update.setAllTicketTaxAmount(StringUtils.isEmpty(allTicketTaxAmount) || Objects.equals("null", allTicketTaxAmount) ? BigDecimal.ZERO : new BigDecimal(allTicketTaxAmount));
        update.setAllTicketAmount(StringUtils.isEmpty(allTicketAmount) || Objects.equals("null", allTicketAmount) ? BigDecimal.ZERO : new BigDecimal(allTicketAmount));
        update.setTotalInvoiceAmount(StringUtils.isEmpty(totalInvoiceAmount) || Objects.equals("null", totalInvoiceAmount) ? BigDecimal.ZERO : new BigDecimal(totalInvoiceAmount));
        update.setTotalInvoiceTaxAmount(StringUtils.isEmpty(totalInvoiceTaxAmount) || Objects.equals("null", totalInvoiceTaxAmount) ? BigDecimal.ZERO : new BigDecimal(totalInvoiceTaxAmount));
        update.setTicketTime(LocalDateTime.now());
        if (!StringUtils.isEmpty(noTicketIncomeAmount) && !Objects.equals("null", noTicketIncomeAmount)) {
            update.setNoTicketIncomeAmount(new BigDecimal(noTicketIncomeAmount));
        }
        customerServicePeriodMonthIncomeService.modifyPeriodMonthIncomeXqy(update, operateName, deptId);

        // 更新收入
        customerServicePeriodMonthIncomeService.updateCustomerServiceIncome(syncCustomer.getCustomerServiceId());
    }

    private Long saveOpenApiIncomeRecord(XqyReportVO xqyReportVO) {
        String reportPeriod = xqyReportVO.getStartDate().substring(0, 7);
        xqyReportVO.setReportPeriod(reportPeriod);
        Integer period = Integer.parseInt(reportPeriod.replace("-", ""));
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(xqyReportVO.getBatchNo());
        record.setSourceType(OpenApiAppRelations.XQY.getId());
        record.setPeriod(period);
        record.setCreateBy(xqyReportVO.getOperateName());
        record.setType(xqyReportVO.getOperateType());
        if (StringUtils.isEmpty(xqyReportVO.getTaxNumber())) {
            log.info("鑫启易发票查询数据异常，本次税号为空");
            record.setErrorReason("数据异常，本次税号为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getOperateName())) {
            log.info("鑫启易发票查询数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        String taxNumber = xqyReportVO.getTaxNumber();
        openApiSyncRecordService.save(record);
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).eq(CCustomerService::getServiceStatus, ServiceStatus.SERVICE.getCode())
                .eq(CCustomerService::getTaxNumber, taxNumber).last("limit 1"));
        CustomerServicePeriodMonthIncome income = Objects.isNull(customerService) ? null : customerServicePeriodMonthIncomeService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>()
                .eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerService.getId())
                .eq(CustomerServicePeriodMonthIncome::getPeriod, period), false);
        OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
        customer.setTaxNumber(taxNumber);
        customer.setSycRecordId(record.getId());
        customer.setCustomerServiceId(Objects.isNull(customerService) ? null : customerService.getId());
        try {
            Map<String, Object> result = remoteThirdpartService.xqyInvoiceStatistic(taxNumber, xqyReportVO.getStartDate(), xqyReportVO.getEndDate()).getDataThrowException();
            log.info("鑫启易发票查询数据结果:{}，税号为：{}，开始时间：{}，结束时间:{}, 操作人：{}", result, taxNumber, xqyReportVO.getStartDate(), xqyReportVO.getEndDate(), xqyReportVO.getOperateName());
            if (!ObjectUtils.isEmpty(result)) {
                String customerName = String.valueOf(result.get("客户名称"));
                customer.setCustomerName(customerName);
                customer.setIsSuccess(true);
                if (Objects.isNull(income)) {
                    customer.setIncomeResult("收入记录不存在");
                    customer.setIsSuccess(false);
                } else {
                    customer.setIncomeId(income.getId());
                }
                openApiSyncCustomerService.save(customer);
                openApiInvoiceDataService.save(convertByInvoiceResult(result, record.getId(), customer.getId()));
                if (Objects.equals(customerService.getTaxType(), TaxType.COMMONLY.getCode()) || Lists.newArrayList(3, 6, 9, 12).contains(period % 100)) {
                    Map<String, Object> vatResult = remoteThirdpartService.xqyQueryVATData(taxNumber, xqyReportVO.getReportPeriod(), null).getDataThrowException();
                    log.info("鑫启易增值税涉税分析查询数据结果:{}，税号为：{}，所属期：{}，操作人：{}", vatResult, taxNumber, xqyReportVO.getReportPeriod(), xqyReportVO.getOperateName());
                    if (!ObjectUtils.isEmpty(vatResult)) {
                        openApiSyncVatDataService.save(convertByVatDataResult(vatResult, record.getId(), customer.getId()));
                    }
                }
            } else {
                customer.setPreAuthResult("未查询到数据");
                customer.setIsSuccess(false);
                openApiSyncCustomerService.save(customer);
            }
        } catch (Exception e) {
            log.error("鑫启易增发票查询数据异常:{}，税号为：{}，开始时间：{}，结束时间:{}，操作人：{}", e.getMessage(), taxNumber, xqyReportVO.getStartDate(), xqyReportVO.getEndDate(), xqyReportVO.getOperateName());
            customer.setIncomeResult("发票查询数据异常:" + e.getMessage());
            customer.setIsSuccess(false);
            openApiSyncCustomerService.save(customer);
        }
        return record.getId();
    }

    private OpenApiInvoiceData convertByInvoiceResult(Map<String, Object> result, Long recordId, Long syncCustomerId) {
        return new OpenApiInvoiceData().setSycRecordId(recordId)
                .setSyncCustomerId(syncCustomerId)
                .setCustomerId(getValueByKey(result, "客户ID"))
                .setTaxNumber(getValueByKey(result, "税号"))
                .setCustomerName(getValueByKey(result, "客户名称"))
                .setTaxpayerQualification(getValueByKey(result, "纳税人资格"))
                .setInvoiceStartPeriod(getValueByKey(result, "开票期起"))
                .setInvoiceEndPeriod(getValueByKey(result, "开票期止"))
                .setOutputAmountExcludingTax(getValueByKey(result, "销项不含税金额合计"))
                .setOutputTaxAmount(getValueByKey(result, "销项税额合计"))
                .setOutputTotalAmount(getValueByKey(result, "销项价税合计"))
                .setOutputInvoiceCount(getValueByKey(result, "销项开票张数"))
                .setOutputInvoiceList(getValueByKey(result, "销项发票列表"))
                .setInputAmountExcludingTax(getValueByKey(result, "进项不含税金额合计"))
                .setInputTaxAmount(getValueByKey(result, "进项税额合计"))
                .setInputTotalAmount(getValueByKey(result, "进项价税合计"))
                .setInputInvoiceCount(getValueByKey(result, "进项发票张数"))
                .setInputInvoiceList(getValueByKey(result, "进项发票列表"))
                .setLastDownloadDate(getValueByKey(result, "截止月发票下载日期"));
    }

    public void xqyAnnualReport(XqyReportVO xqyReportVO) {
        xqyReportVO.setDeliverType(DeliverType.ANNUAL_REPORT.getCode().toString());
        xqyReportVO.setReportPeriod(Integer.parseInt(xqyReportVO.getBelongYear()) + "-12");
        Long recordId = saveOpenApiSyncAnnualRecord(xqyReportVO);
        dealAnnualReportBySyncRecordId(recordId, xqyReportVO.getOperateName(), xqyReportVO.getReportPeriod(), xqyReportVO.getDeliverType(), xqyReportVO.getNotificationText(), xqyReportVO.getDeptId());
        updateAsyncRecordDataCount(recordId);
    }

    private void dealAnnualReportBySyncRecordId(Long recordId, String operateName, String reportPeriod, String deliverType, String remark, Long deptId) {
        List<OpenApiSyncCustomer> syncCustomerList = openApiSyncCustomerService.selectBySyncRecordId(recordId);
        if (ObjectUtils.isEmpty(syncCustomerList)) {
            log.info("本次申报无客户信息,recordId:{}", recordId);
            return;
        }
        OpenApiSyncCustomer syncCustomer = syncCustomerList.get(0);
        if (Objects.isNull(syncCustomer.getPreAuthDeliverId()) || !syncCustomer.getIsSuccess()) {
            log.info("未查询到预认证交付单或有异常,recordId:{}", recordId);
            return;
        }
        ReportV2VO vo = ReportV2VO.builder()
                .id(syncCustomer.getPreAuthDeliverId())
                .reportStatus(1)
                .reportRemark(remark)
                .operName(operateName)
                .deptId(deptId)
                .build();
        customerDeliverService.xqyAnuualReportComplete(vo);
    }

    private Long saveOpenApiSyncAnnualRecord(XqyReportVO xqyReportVO) {
        Integer period = StringUtils.isEmpty(xqyReportVO.getReportPeriod()) ? DateUtils.getNowPeriod() : DateUtils.yearMonthToPeriod(xqyReportVO.getReportPeriod());
        OpenApiSyncRecord record = new OpenApiSyncRecord();
        record.setBatchNo(xqyReportVO.getBatchNo());
        record.setSourceType(OpenApiAppRelations.XQY.getId());
        record.setPeriod(period);
        record.setDeliverType(xqyReportVO.getDeliverType());
        record.setCreateBy(xqyReportVO.getOperateName());
        record.setType(xqyReportVO.getOperateType());
        if (ObjectUtils.isEmpty(xqyReportVO.getTaxNumberList())) {
            log.info("鑫启易申报数据异常，本次申报税号列表为空");
            record.setErrorReason("数据异常，本次申报税号列表为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getReportPeriod())) {
            log.info("鑫启易申报数据异常，本次申报所属期为空");
            record.setErrorReason("数据异常，本次申报所属期为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (StringUtils.isEmpty(xqyReportVO.getOperateName())) {
            log.info("鑫启易申报数据异常，本次申报操作人为空");
            record.setErrorReason("数据异常，本次申报操作人为空");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        if (!xqyReportVO.getIsCompleted()) {
            log.info("鑫启易工商年报完成异常，isCompleted=false");
            record.setErrorReason("工商年报未完成，isCompleted=false");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        String taxNumber = xqyReportVO.getTaxNumberList().get(0);
        openApiSyncRecordService.save(record);
        CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthService.getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getPeriod, period)
                .eq(CustomerServicePeriodMonth::getTaxNumber, taxNumber), false);
        if (Objects.isNull(periodMonth)) {
            log.info("鑫启易工商年报完成异常，账期不存在");
            record.setErrorReason("数据异常，账期不存在");
            openApiSyncRecordService.save(record);
            return record.getId();
        }
        CustomerDeliver annualReportDeliver = customerDeliverService.getOne(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getCustomerServicePeriodMonthId, periodMonth.getId())
                        .eq(CustomerDeliver::getDeliverType, DeliverType.ANNUAL_REPORT.getCode()));
        OpenApiSyncCustomer customer = new OpenApiSyncCustomer();
        customer.setTaxNumber(taxNumber);
        customer.setSycRecordId(record.getId());
        customer.setPreAuthRemind(xqyReportVO.getNotificationText());
        String customerName = periodMonth.getCustomerName();
        customer.setCustomerName(customerName);
        customer.setIsSuccess(true);
        if (Objects.isNull(annualReportDeliver)) {
            customer.setPreAuthResult("交付单不存在");
            customer.setIsSuccess(false);
        } else {
            customer.setPreAuthDeliverId(annualReportDeliver.getId());
            if (annualReportDeliver.getHasChanged()) {
                customer.setPreAuthResult("交付变更待确认");
                customer.setIsSuccess(false);
            } else {
                if (!Lists.newArrayList(DeliverStatus.STATUS_1.getCode(), DeliverStatus.STATUS_0.getCode()).contains(annualReportDeliver.getStatus())) {
                    customer.setPreAuthResult("交付单状态不符，当前状态为：" + DeliverStatus.getDeliverStatusByCode(annualReportDeliver.getStatus()).getName());
                    customer.setIsSuccess(false);
                }
            }
        }
        openApiSyncCustomerService.save(customer);
        return record.getId();
    }
}
