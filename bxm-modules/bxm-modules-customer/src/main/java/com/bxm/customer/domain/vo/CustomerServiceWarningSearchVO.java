package com.bxm.customer.domain.vo;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceWarningSearchVO extends BaseVO {

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("会计小组id")
    private Long deptId;

    private String deptIds;

    @ApiModelProperty("部门类型，1-顾问，2-会计")
    private Integer deptType;

    @ApiModelProperty("标签吗")
    private String tagName;

    @ApiModelProperty("标签是否包含，0-否，1-是")
    private Integer tagIncludeFlag;

    @ApiModelProperty("顾问部门或员工名")
    private String advisorDeptEmployeeName;

    @ApiModelProperty("会计部门或员工名")
    private String accountingDeptEmployeeName;
}