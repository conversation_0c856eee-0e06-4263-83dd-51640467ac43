package com.bxm.customer.domain.dto.tag;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class TagSelectDTO {

    @ApiModelProperty("系统标签列表")
    private List<TagV2DTO> systemTagList;

    @ApiModelProperty("集团标签列表")
    private List<TagV2DTO> deptTagList;

    public TagSelectDTO() {
        this.systemTagList = Lists.newArrayList();
        this.deptTagList = Lists.newArrayList();
    }
}
