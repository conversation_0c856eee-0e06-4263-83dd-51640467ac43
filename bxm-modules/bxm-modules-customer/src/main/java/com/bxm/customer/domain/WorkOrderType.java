package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 工单类型配置对象 c_work_order_type
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@ApiModel("工单类型配置对象")
@Accessors(chain = true)
@TableName("c_work_order_type")
public class WorkOrderType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 工单类型 */
    @Excel(name = "工单类型")
    @TableField("work_order_type")
    @ApiModelProperty(value = "工单类型")
    private Integer workOrderType;

    /** 工单类型名称 */
    @Excel(name = "工单类型名称")
    @TableField("work_order_type_name")
    @ApiModelProperty(value = "工单类型名称")
    private String workOrderTypeName;

    /** 分配类型，1-分配给指定组织，2-分配给服务会计，3-分配给服务顾问，4-按发起方分配，工厂发起顾问接收，业务发起工厂接收 */
    @Excel(name = "分配类型，1-分配给指定组织，2-分配给服务会计，3-分配给服务顾问，4-按发起方分配，工厂发起顾问接收，业务发起工厂接收")
    @TableField("dispatch_type")
    @ApiModelProperty(value = "分配类型，1-分配给指定组织，2-分配给服务会计，3-分配给服务顾问，4-按发起方分配，工厂发起顾问接收，业务发起工厂接收")
    private Integer dispatchType;

    /** 分配给指定的组织id */
    @Excel(name = "分配给指定的组织id")
    @TableField("dispatch_dept_id")
    @ApiModelProperty(value = "分配给指定的组织id")
    private Long dispatchDeptId;

    /** 分配给指定的用户id */
    @Excel(name = "分配给指定的用户id")
    @TableField("dispatch_user_id")
    @ApiModelProperty(value = "分配给指定的用户id")
    private Long dispatchUserId;

    /** 客户是否必填 */
    @Excel(name = "客户是否必填")
    @TableField("is_need_customer")
    @ApiModelProperty(value = "客户是否必填")
    private Boolean isNeedCustomer;

    /** 账期是否必填 */
    @Excel(name = "账期是否必填")
    @TableField("is_need_period")
    @ApiModelProperty(value = "账期是否必填")
    private Boolean isNeedPeriod;

    /** 备注是否必填 */
    @Excel(name = "备注是否必填")
    @TableField("is_need_remark")
    @ApiModelProperty(value = "备注是否必填")
    private Boolean isNeedRemark;

    /** 默认工单备注 */
    @Excel(name = "默认工单备注")
    @TableField("default_remark")
    @ApiModelProperty(value = "默认工单备注")
    private String defaultRemark;

    /** 是否显示 */
    @Excel(name = "是否显示")
    @TableField("is_show")
    @ApiModelProperty(value = "是否显示")
    private Boolean isShow;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    /** 是否显示账务交付单 */
    @Excel(name = "是否显示账务交付单")
    @TableField("is_show_accounting_cashier_deliver")
    @ApiModelProperty(value = "是否显示账务交付单")
    private Boolean isShowAccountingCashierDeliver;
}
