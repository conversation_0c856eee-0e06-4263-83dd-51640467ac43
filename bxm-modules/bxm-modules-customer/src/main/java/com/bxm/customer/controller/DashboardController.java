package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.customer.domain.DashboardConfig;
import com.bxm.customer.service.DashboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/dashboard")
@Api(tags = "报表相关")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    @GetMapping("/getDashboardConfigList")
    @ApiOperation("获取报表配置列表")
    public Result<List<DashboardConfig>> getDashboardConfigList(@RequestHeader("deptId") Long deptId) {
        return Result.ok(dashboardService.getDashboardConfigList(deptId));
    }
}
