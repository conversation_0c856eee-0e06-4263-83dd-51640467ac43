package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CTag;
import com.bxm.customer.domain.dto.tag.TagSelectDTO;

/**
 * 标签Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface ICTagService extends IService<CTag>
{
    /**
     * 查询标签
     * 
     * @param id 标签主键
     * @return 标签
     */
    public CTag selectCTagById(Long id);

    /**
     * 查询标签列表
     * 
     * @param cTag 标签
     * @return 标签集合
     */
    public List<CTag> selectCTagList(CTag cTag);

    /**
     * 新增标签
     * 
     * @param cTag 标签
     * @return 结果
     */
    public int insertCTag(CTag cTag);

    /**
     * 修改标签
     * 
     * @param cTag 标签
     * @return 结果
     */
    public int updateCTag(CTag cTag);

    /**
     * 批量删除标签
     * 
     * @param ids 需要删除的标签主键集合
     * @return 结果
     */
    public int deleteCTagByIds(Long[] ids);

    /**
     * 删除标签信息
     * 
     * @param id 标签主键
     * @return 结果
     */
    public int deleteCTagById(Long id);

    CTag selectByTagTypeAndTagName(Integer tagType, String tagName);

    TagSelectDTO tagSelectList(Long deptId, Integer businessType, Long businessId);
}
