package com.bxm.customer.domain.vo.accoutingCashier;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierOperateVO {

    @ApiModelProperty("批量操作的id列表")
    private List<Long> ids;

    @ApiModelProperty("单个操作的id")
    private Long id;

    @ApiModelProperty("账务类型，1-入账，2-流水，3-改账")
    private Integer type;

    @ApiModelProperty("入账交付相关信息（仅交付操作且类型为入账需要传）")
    private InAccountDeliverVO inAccountDeliverInfo;

    @ApiModelProperty("交付结果，1-正常，2-无账务/无流水，3-无需交付，4-异常（仅交付操作时需要传）")
    private Integer deliverResult;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    private Integer materialIntegrity;

    @ApiModelProperty("备注（仅交付和退回操作时需要传）")
    private String remark;

    @ApiModelProperty("附件列表（仅交付和退回操作时需要传）")
    private List<CommonFileVO> files;

    @ApiModelProperty("提交前状态，4-待重提，6-交付待提交（单个提交不需要传这个字段）")
    private Integer preStatus;

    @ApiModelProperty("对账单余额")
    private BigDecimal statementBalance;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private String operName;

    @ApiModelProperty(hidden = true)
    private Boolean isCoverFiles;

    @ApiModelProperty(hidden = true)
    private Boolean isInner;
}
