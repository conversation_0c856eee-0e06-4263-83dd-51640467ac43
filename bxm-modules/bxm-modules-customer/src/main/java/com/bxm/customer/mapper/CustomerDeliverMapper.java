package com.bxm.customer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.CustomerDeliverDTO;
import com.bxm.customer.domain.dto.CustomerDeliverForCustomerServiceDetailSourceDTO;
import com.bxm.customer.domain.vo.CustomerDeliverSearchVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerDeliver;
import org.apache.ibatis.annotations.Param;

/**
 * 交付Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@Mapper
public interface CustomerDeliverMapper extends BaseMapper<CustomerDeliver>
{
    /**
     * 查询交付
     * 
     * @param id 交付主键
     * @return 交付
     */
    public CustomerDeliver selectCustomerDeliverById(Long id);

    /**
     * 查询交付列表
     * 
     * @param customerDeliver 交付
     * @return 交付集合
     */
    public List<CustomerDeliver> selectCustomerDeliverList(CustomerDeliver customerDeliver);

    /**
     * 新增交付
     * 
     * @param customerDeliver 交付
     * @return 结果
     */
    public int insertCustomerDeliver(CustomerDeliver customerDeliver);

    /**
     * 修改交付
     * 
     * @param customerDeliver 交付
     * @return 结果
     */
    public int updateCustomerDeliver(CustomerDeliver customerDeliver);

    /**
     * 删除交付
     * 
     * @param id 交付主键
     * @return 结果
     */
    public int deleteCustomerDeliverById(Long id);

    /**
     * 批量删除交付
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerDeliverByIds(Long[] ids);

    List<CustomerDeliverDTO> selectDeliverList(IPage<CustomerDeliverDTO> result, @Param("vo") CustomerDeliverSearchVO vo,
                                               @Param("customerServiceIds") List<Long> customerServiceIds,
                                               @Param("userDept") UserDeptDTO userDept,
                                               @Param("isAdmin") Integer isAdmin, @Param("ids") List<Long> ids,
                                               @Param("periodIds") List<Long> periodIds,
                                               @Param("queryDeptIds") List<Long> queryDeptIds);

    List<CustomerDeliverDTO> selectDeliverList(@Param("vo") CustomerDeliverSearchVO vo,
                                               @Param("customerServiceIds") List<Long> customerServiceIds,
                                               @Param("userDept") UserDeptDTO userDept,
                                               @Param("isAdmin") Integer isAdmin, @Param("ids") List<Long> ids,
                                               @Param("periodIds") List<Long> periodIds,
                                               @Param("queryDeptIds") List<Long> queryDeptIds);

    List<CommonDeptCountDTO> selectDeliverAccountDeptCountList(@Param("vo") CustomerDeliverSearchVO vo,
                                                               @Param("customerServiceIds") List<Long> customerServiceIds,
                                                               @Param("userDept") UserDeptDTO userDept,
                                                               @Param("isAdmin") Integer isAdmin);

    List<CommonDeptCountDTO> selectDeliverAdvisorDeptCountList(@Param("vo") CustomerDeliverSearchVO vo,
                                                               @Param("customerServiceIds") List<Long> customerServiceIds,
                                                               @Param("userDept") UserDeptDTO userDept,
                                                               @Param("isAdmin") Integer isAdmin);

    List<CommonDeptCountDTO> selectDeliverCustomerServiceAdvisorDeptCountList(@Param("vo") CustomerDeliverSearchVO vo,
                                                               @Param("userDept") UserDeptDTO userDept,
                                                               @Param("isAdmin") Integer isAdmin);

    List<CommonDeptCountDTO> selectDeliverCustomerServiceAccountDeptCountList(@Param("vo") CustomerDeliverSearchVO vo,
                                                               @Param("userDept") UserDeptDTO userDept,
                                                               @Param("isAdmin") Integer isAdmin);

    List<CommonDeptCountDTO> selectDeliverPeriodAdvisorDeptCountList(@Param("vo") CustomerDeliverSearchVO vo,
                                                               @Param("userDept") UserDeptDTO userDept,
                                                               @Param("isAdmin") Integer isAdmin);

    List<CommonDeptCountDTO> selectDeliverPeriodAccountDeptCountList(@Param("vo") CustomerDeliverSearchVO vo,
                                                               @Param("userDept") UserDeptDTO userDept,
                                                               @Param("isAdmin") Integer isAdmin);

    /**
     * 根据服务获取交付记录，简易字段
     */
    List<CustomerDeliverForCustomerServiceDetailSourceDTO> selectCustomerDeliverForCustomerServiceDetailByCustomerServiceId(@Param("customerServiceId") Long customerServiceId);

    List<CustomerDeliver> selectByDeliverTypeAndPeriodAndMonthConditionSql(@Param("deliverType") Integer deliverType, @Param("period") Integer period, @Param("monthConditionSql") String monthConditionSql);

    List<CustomerDeliver> selectByDeliverTypeAndPeriodAndMonthCondition(@Param("deliverType") Integer deliverType, @Param("userDeptDTO") UserDeptDTO userDeptDTO, @Param("queryDeptId") Long queryDeptId, @Param("queryDeptIds") List<Long> queryDeptIds, @Param("queryDeptType") Integer queryDeptType);

    List<CustomerDeliver> selectCompleteByDeliverTypeAndPeriodAndMonthConditionSql(@Param("deliverType") Integer deliverType, @Param("period") Integer period, @Param("monthConditionSql") String monthConditionSql);
}
