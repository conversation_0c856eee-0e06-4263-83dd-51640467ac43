package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.quality.QualityCheckingType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.dto.businessTask.BusinessTaskAccountingDeptCountDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO;
import com.bxm.customer.domain.dto.tag.TagSelectDTO;
import com.bxm.customer.domain.dto.workOrder.InitiateUndertakeDeptDTO;
import com.bxm.customer.domain.vo.CustomerDeliverSearchVO;
import com.bxm.customer.domain.vo.CustomerServicePeriodMonthSearchVO;
import com.bxm.customer.domain.vo.CustomerServicePeriodYearSearchVO;
import com.bxm.customer.domain.vo.CustomerServiceSearchVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingRecordVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderDataSearchVO;
import com.bxm.customer.domain.vo.workOrder.QualityExceptionMiniListSearchVO;
import com.bxm.customer.mapper.OpenApiSyncItemMapper;
import com.bxm.customer.mapper.QualityCheckingItemMapper;
import com.bxm.customer.mapper.QualityCheckingRecordMapper;
import com.bxm.customer.mapper.QualityCheckingResultMapper;
import com.bxm.customer.properties.YearProperties;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequestMapping("/select")
@Api(tags = "统一下拉框选择数据源")
@RestController
public class SelectController {

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    private ICustomerDeliverService customerDeliverService;

    @Autowired
    private IBankService bankService;

    @Autowired
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Autowired
    private SettlementOrderService settlementOrderService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private YearProperties yearProperties;

    @Autowired
    private IBusinessTaskService businessTaskService;

    @Autowired
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @Autowired
    private IMaterialDeliverService materialDeliverService;

    @Autowired
    private IWorkOrderService workOrderService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private OpenApiSyncItemMapper openApiSyncItemMapper;

    @Autowired
    private QualityCheckingItemMapper qualityCheckingItemMapper;

    @Autowired
    private QualityCheckingResultMapper qualityCheckingResultMapper;

    @Autowired
    private QualityCheckingRecordMapper qualityCheckingRecordMapper;

    @Autowired
    private ICTagService tagService;

    @GetMapping("/serviceSelect")
    @ApiOperation("获取当前用户可管理客户下拉列表")
    public Result<List<CCustomerService>> serviceSelect(@RequestParam("keyWord") @ApiParam("搜索关键字") String keyWord,
                                                        @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerServiceService.serviceSelect(keyWord, deptId));
    }

    @GetMapping("/serviceSelectForDeliver")
    @ApiOperation("获取当前用户可管理客户下拉列表（税务交付）")
    public Result<List<CCustomerService>> serviceSelectForDeliver(@RequestParam("keyWord") @ApiParam("搜索关键字") String keyWord,
                                                        @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerServiceService.serviceSelectForDeliver(keyWord, deptId));
    }

    @GetMapping("/serviceSelectNoDataScope")
    @ApiOperation("获取客户下拉列表(当前集团下,无数据权限)")
    public Result<List<CCustomerService>> serviceSelectNoDataScope(@RequestParam("keyWord") @ApiParam("搜索关键字") String keyWord,
                                                        @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerServiceService.serviceSelectNoDataScope(keyWord, deptId));
    }

    @GetMapping("/periodSelect")
    @ApiOperation("获取服务对应账期下拉列表（交付）")
    public Result<List<CustomerServicePeriodMonth>> periodSelect(@RequestParam("customerServiceId") @ApiParam("服务id") Long customerServiceId,
                                                                 @RequestParam(value = "tabType", required = false) Integer tabType) {
        return Result.ok(customerServicePeriodMonthService.periodSelect(customerServiceId, tabType));
    }

    @GetMapping("/periodSelectForDeliver")
    @ApiOperation("获取服务对应账期下拉列表（交付）")
    public Result<List<CustomerServicePeriodMonth>> periodSelectForDeliver(@RequestParam("customerServiceId") @ApiParam("服务id") Long customerServiceId,
                                                                 @RequestParam(value = "tabType", required = false) Integer tabType, @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerServicePeriodMonthService.periodSelectForDeliver(customerServiceId, tabType, deptId));
    }

    @GetMapping("/periodSelectForDeliverV2")
    @ApiOperation("获取服务对应账期下拉列表（交付）")
    public Result<List<CustomerServicePeriodMonth>> periodSelectForDeliverV2(@RequestParam("customerServiceId") @ApiParam("服务id") Long customerServiceId,
                                                                           @RequestParam(value = "tabType", required = false) Integer tabType, @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerServicePeriodMonthService.periodSelectForDeliverV2(customerServiceId, tabType, deptId));
    }

    @GetMapping("/incomePeriodSelect")
    @ApiOperation("获取服务对应账期下拉列表（收入）")
    public Result<List<CustomerServicePeriodMonth>> incomePeriodSelect(@RequestParam("customerServiceId") @ApiParam("服务id") Long customerServiceId) {
        return Result.ok(customerServicePeriodMonthService.incomePeriodSelect(customerServiceId));
    }

    @GetMapping("/periodSelectForAccountingCashier")
    @ApiOperation("获取服务对应账期下拉列表（账务）")
    public Result<List<CustomerServicePeriodMonth>> periodSelectForAccountingCashier(@RequestParam("customerServiceId") @ApiParam("服务id") Long customerServiceId,
                                                                                     @RequestParam("accountingCashierType") @ApiParam("账务类型，1-入账，2-流水，3-改账") Integer accountingCashierType) {
        return Result.ok(customerServicePeriodMonthService.periodSelectForAccountingCashier(customerServiceId, accountingCashierType));
    }

    @GetMapping("/getPeriodSelect")
    @ApiOperation("获取账期选择下拉框")
    private Result<List<Integer>> getPeriodSelect(@RequestParam("deliverType") @ApiParam("交付类型，1-医社保，2-个税（工资薪金），3-国税，4-预认证，5-收入") Integer deliverType) {
        return Result.ok(customerDeliverService.getPeriodSelect(deliverType));
    }

    @GetMapping("/getDeliverDeptSelectList")
    @ApiOperation("获取交付顾问，会计下拉框数据源")
    public Result<BusinessDeptSelectDTO> getDeliverDeptSelectList(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId) {
        List<CommonDeptCountDTO> accountingDeptCountList = customerDeliverService.remoteDeliverAccountingDeptCountList(deptId, vo);
        List<CommonDeptCountDTO> advisorDeptCountList = customerDeliverService.remoteDeliverAdvisorDeptCountList(deptId, vo);
        if (ObjectUtils.isEmpty(accountingDeptCountList) && ObjectUtils.isEmpty(advisorDeptCountList)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() :
                accountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() :
                advisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() : accountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() : advisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getDeliverDeptSelectListV2")
    @ApiOperation("获取服务交付顾问/会计，账期顾问/会计下拉框数据源")
    public Result<DeliverDeptSelectDTO> getDeliverDeptSelectListV2(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId) {
        if (!Objects.isNull(vo.getYear())) {
            // 查年份目前就是等于查202412账期的 年报 和 汇算 的交付单，用这种方式可以提升查询效率，降低数据库压力
            vo.setYear(vo.getYear() * 100 + 12);
        }
        List<CommonDeptCountDTO> customerServiceAdvisorDeptCountList = customerDeliverService.deliverCustomerServiceAdvisorDeptCountList(deptId, vo);
        List<CommonDeptCountDTO> customerServiceAccountingDeptCountList = customerDeliverService.deliverCustomerServiceAccountingDeptCountList(deptId, vo);
        List<CommonDeptCountDTO> periodAdvisorDeptCountList = customerDeliverService.deliverPeriodAdvisorDeptCountList(deptId, vo);
        List<CommonDeptCountDTO> periodAccountingDeptCountList = customerDeliverService.deliverPeriodAccountingDeptCountList(deptId, vo);
        if (ObjectUtils.isEmpty(customerServiceAdvisorDeptCountList) && ObjectUtils.isEmpty(customerServiceAccountingDeptCountList)
                && ObjectUtils.isEmpty(periodAdvisorDeptCountList) && ObjectUtils.isEmpty(periodAccountingDeptCountList)) {
            return Result.ok(new DeliverDeptSelectDTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(customerServiceAdvisorDeptCountList) ? Lists.newArrayList() :
                customerServiceAdvisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(customerServiceAccountingDeptCountList) ? Lists.newArrayList() :
                customerServiceAccountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(periodAdvisorDeptCountList) ? Lists.newArrayList() :
                periodAdvisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(periodAccountingDeptCountList) ? Lists.newArrayList() :
                periodAccountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(DeliverDeptSelectDTO.builder()
                .customerServiceAdvisorDeptList(ObjectUtils.isEmpty(customerServiceAdvisorDeptCountList) ? Lists.newArrayList() : customerServiceAdvisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .customerServiceAccountingDeptList(ObjectUtils.isEmpty(customerServiceAccountingDeptCountList) ? Lists.newArrayList() : customerServiceAccountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .periodAdvisorDeptList(ObjectUtils.isEmpty(periodAdvisorDeptCountList) ? Lists.newArrayList() : periodAdvisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .periodAccountingDeptList(ObjectUtils.isEmpty(periodAccountingDeptCountList) ? Lists.newArrayList() : periodAccountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getDeliverMiniListDeptSelectList")
    @ApiOperation("获取交付miniList顾问，会计下拉框数据源")
    public Result<BusinessDeptSelectDTO> getDeliverMiniListDeptSelectList(@RequestHeader("deptId") Long deptId,
                                                                          @RequestParam(value = "deptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                                          @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id") String deptIdStr,
                                                                          @RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得）") Integer deliverType,
                                                                          @RequestParam("deliverStatus") @ApiParam("交付状态，-1-待创建，0-待申报，1-申报待提交，2-待确认，3-待扣款，4-扣款待提交，5-待重题，6-申报异常，7-扣款异常，8-冻结待交付，9-待反馈，10-待认证，11-认证异常") Integer deliverStatus,
                                                                          @RequestParam(value = "keyWord", required = false) @ApiParam("客户名称") String customerName,
                                                                          @RequestParam(value = "tagName", required = false) @ApiParam("标签名称") String tagName,
                                                                          @RequestParam(value = "tagIncludeFlag", required = false) @ApiParam("是否包含标签，0-否，1-是") Integer tagIncludeType,
                                                                          @RequestParam(value = "advisorDeptId", required = false) @ApiParam("顾问部门id") Long advisorDeptId,
                                                                          @RequestParam(value = "accountingDeptId", required = false) @ApiParam("会计部门id") Long accountingDeptId) {
        List<CustomerDeliverMiniDTO> allData = customerServiceService.remoteCustomerDeliverMiniList(deptId, queryDeptId, deptIdStr, deliverType, deliverStatus, customerName, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
        if (ObjectUtils.isEmpty(allData)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        Map<Long, List<CustomerDeliverMiniDTO>> advisorMap = allData.stream().filter(d -> !Objects.isNull(d.getAdvisorDeptId())).collect(Collectors.groupingBy(CustomerDeliverMiniDTO::getAdvisorDeptId));
        Map<Long, List<CustomerDeliverMiniDTO>> accountingMao = allData.stream().filter(d -> !Objects.isNull(d.getAccountingDeptId())).collect(Collectors.groupingBy(CustomerDeliverMiniDTO::getAccountingDeptId));
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(advisorMap.keySet());
        deptIds.addAll(accountingMao.keySet());
        List<SysDept> deptList = remoteDeptService.getAllDept().getDataThrowException();
        Map<Long, SysDept> deptMap = ObjectUtils.isEmpty(deptList) ? Maps.newHashMap() : deptList.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        return Result.ok(BusinessDeptSelectDTO.builder()
                .advisorDeptList(ObjectUtils.isEmpty(advisorMap) ? Lists.newArrayList() : advisorMap.entrySet().stream().map(e ->
                       {
                           SysDept sysDept = deptMap.get(e.getKey());
                           List<SysEmployee> employees = employeeMap.get(e.getKey());
                           return BusinessDeptDTO.builder()
                                   .deptId(e.getKey())
                                   .deptName(Objects.isNull(sysDept) ? "" : sysDept.getDeptName())
                                   .deptType(1)
                                   .employeeNames(ObjectUtils.isEmpty(employees) ? "" : employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                   .dataCount((long) e.getValue().size())
                                   .build();
                       }).collect(Collectors.toList()))
                .accountantDeptList(ObjectUtils.isEmpty(accountingMao) ? Lists.newArrayList() : accountingMao.entrySet().stream().map(e ->
                {
                    SysDept sysDept = deptMap.get(e.getKey());
                    List<SysEmployee> employees = employeeMap.get(e.getKey());
                    return BusinessDeptDTO.builder()
                            .deptId(e.getKey())
                            .deptName(Objects.isNull(sysDept) ? "" : sysDept.getDeptName())
                            .deptType(2)
                            .employeeNames(ObjectUtils.isEmpty(employees) ? "" : employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                            .dataCount((long) e.getValue().size())
                            .build();
                }).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getIncomeMiniListDeptSelectList")
    @ApiOperation("获取收入miniList顾问，会计下拉框数据源")
    public Result<BusinessDeptSelectDTO> getIncomeMiniListDeptSelectList(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome,
                                                                         @RequestHeader("deptId") Long deptId) {
        List<CustomerServicePeriodMonthIncome> allData = customerServicePeriodMonthIncomeService.incomeList(customerServicePeriodMonthIncome, deptId, 1, -1).getRecords();
        if (ObjectUtils.isEmpty(allData)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        Map<Long, List<CustomerServicePeriodMonthIncome>> advisorMap = allData.stream().filter(d -> !Objects.isNull(d.getAdvisorDeptId())).collect(Collectors.groupingBy(CustomerServicePeriodMonthIncome::getAdvisorDeptId));
        Map<Long, List<CustomerServicePeriodMonthIncome>> accountingMao = allData.stream().filter(d -> !Objects.isNull(d.getAccountingDeptId())).collect(Collectors.groupingBy(CustomerServicePeriodMonthIncome::getAccountingDeptId));
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(advisorMap.keySet());
        deptIds.addAll(accountingMao.keySet());
        List<SysDept> deptList = remoteDeptService.getAllDept().getDataThrowException();
        Map<Long, SysDept> deptMap = ObjectUtils.isEmpty(deptList) ? Maps.newHashMap() : deptList.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        return Result.ok(BusinessDeptSelectDTO.builder()
                .advisorDeptList(ObjectUtils.isEmpty(advisorMap) ? Lists.newArrayList() : advisorMap.entrySet().stream().map(e ->
                {
                    SysDept sysDept = deptMap.get(e.getKey());
                    List<SysEmployee> employees = employeeMap.get(e.getKey());
                    return BusinessDeptDTO.builder()
                            .deptId(e.getKey())
                            .deptName(Objects.isNull(sysDept) ? "" : sysDept.getDeptName())
                            .deptType(1)
                            .employeeNames(ObjectUtils.isEmpty(employees) ? "" : employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                            .dataCount((long) e.getValue().size())
                            .build();
                }).collect(Collectors.toList()))
                .accountantDeptList(ObjectUtils.isEmpty(accountingMao) ? Lists.newArrayList() : accountingMao.entrySet().stream().map(e ->
                {
                    SysDept sysDept = deptMap.get(e.getKey());
                    List<SysEmployee> employees = employeeMap.get(e.getKey());
                    return BusinessDeptDTO.builder()
                            .deptId(e.getKey())
                            .deptName(Objects.isNull(sysDept) ? "" : sysDept.getDeptName())
                            .deptType(2)
                            .employeeNames(ObjectUtils.isEmpty(employees) ? "" : employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                            .dataCount((long) e.getValue().size())
                            .build();
                }).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getCustomerServiceListDeptSelectList")
    @ApiOperation("获取服务列表顾问，会计下拉框数据源")
    public Result<BusinessDeptSelectDTO> getCustomerServiceListDeptSelectList(@RequestHeader("deptId") Long deptId,
                                                                              CustomerServiceSearchVO vo) {
        List<CommonDeptCountDTO> accountingDeptCountList = customerServiceService.customerServiceAccountingCountList(deptId, vo);
        List<CommonDeptCountDTO> advisorDeptCountList = customerServiceService.customerServiceAdvisorCountList(deptId, vo);
        if (ObjectUtils.isEmpty(accountingDeptCountList) && ObjectUtils.isEmpty(advisorDeptCountList)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() :
                accountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() :
                advisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() : accountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() : advisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getCustomerServiceYearListDeptSelectList")
    @ApiOperation("获取年度账期列表顾问，会计下拉框数据源")
    public Result<BusinessDeptSelectDTO> getCustomerServiceYearListDeptSelectList(@RequestHeader("deptId") Long deptId,
                                                                                  CustomerServicePeriodYearSearchVO vo) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<CommonDeptCountDTO> accountingDeptCountList = customerServiceService.customerServiceYearAccountingCountList(userDeptDTO);
        List<CommonDeptCountDTO> advisorDeptCountList = customerServiceService.customerServiceYearAdvisorCountList(userDeptDTO);
        if (ObjectUtils.isEmpty(accountingDeptCountList) && ObjectUtils.isEmpty(advisorDeptCountList)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() :
                accountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() :
                advisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() : accountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() : advisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getCustomerServicePeriodListDeptSelectList")
    @ApiOperation("获取账期列表顾问，会计下拉框数据源")
    public Result<BusinessDeptSelectDTO> getCustomerServicePeriodListDeptSelectList(@RequestHeader("deptId") Long deptId, CustomerServicePeriodMonthSearchVO vo) {
        List<CommonDeptCountDTO> accountingDeptCountList = customerServiceService.customerServicePeriodMonthAccountingDeptCountList(deptId, vo);
        List<CommonDeptCountDTO> advisorDeptCountList = customerServiceService.customerServicePeriodMonthAdvisorDeptCountList(deptId, vo);
        if (ObjectUtils.isEmpty(accountingDeptCountList) && ObjectUtils.isEmpty(advisorDeptCountList)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() :
                accountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() :
                advisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() : accountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() : advisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getSettlementOrderDataDeptSelectByBatchNo")
    @ApiOperation("获取结算单明细顾问，会计下拉框数据源（创建时候的明细列表）")
    public Result<SettlementBusinessDeptSelectDTO> getSettlementOrderDataDeptSelectByBatchNo(@RequestHeader("deptId") Long deptId, SettlementOrderDataSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<SettlementOrderDataDTO> allData = settlementOrderService.settlementOrderDataListByConditions(vo).getRecords();
        if (ObjectUtils.isEmpty(allData)) {
            return Result.ok(new SettlementBusinessDeptSelectDTO());
        }
        Map<Long, List<SettlementOrderDataDTO>> customerAdvisorMap = allData.stream().filter(d -> !Objects.isNull(d.getCustomerServiceAdvisorDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getCustomerServiceAdvisorDeptId));
        Map<Long, List<SettlementOrderDataDTO>> customerAccountingMap = allData.stream().filter(d -> !Objects.isNull(d.getCustomerServiceAccountingDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getCustomerServiceAccountingDeptId));
        Map<Long, List<SettlementOrderDataDTO>> periodAdvisorMap = allData.stream().filter(d -> !Objects.isNull(d.getPeriodAdvisorDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getPeriodAdvisorDeptId));
        Map<Long, List<SettlementOrderDataDTO>> periodAccountingMap = allData.stream().filter(d -> !Objects.isNull(d.getPeriodAccountingDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getPeriodAccountingDeptId));
        Map<Long, List<SettlementOrderDataDTO>> createDeptMap = allData.stream().filter(d -> !Objects.isNull(d.getCreateDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getCreateDeptId));
        return Result.ok(SettlementBusinessDeptSelectDTO.builder()
                .customerAdvisorDeptList(ObjectUtils.isEmpty(customerAdvisorMap) ? Lists.newArrayList() : customerAdvisorMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getCustomerServiceAdvisorDeptName())
                                .deptType(1)
                                .employeeNames(e.getValue().get(0).getCustomerServiceAdvisorEmployeeName())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .customerAccountantDeptList(ObjectUtils.isEmpty(customerAccountingMap) ? Lists.newArrayList() : customerAccountingMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getCustomerServiceAccountingDeptName())
                                .deptType(2)
                                .employeeNames(e.getValue().get(0).getCustomerServiceAccountingEmployeeName())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .periodAdvisorDeptList(ObjectUtils.isEmpty(periodAdvisorMap) ? Lists.newArrayList() : periodAdvisorMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getPeriodAdvisorDeptName())
                                .deptType(1)
                                .employeeNames(e.getValue().get(0).getPeriodAdvisorEmployeeName())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .periodAccountantDeptList(ObjectUtils.isEmpty(periodAccountingMap) ? Lists.newArrayList() : periodAccountingMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getPeriodAccountingDeptName())
                                .deptType(2)
                                .employeeNames(e.getValue().get(0).getPeriodAccountingEmployeeName())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .createDeptList(ObjectUtils.isEmpty(createDeptMap) ? Lists.newArrayList() : createDeptMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getCreateDeptName())
                                .employeeNames(e.getValue().get(0).getCreateBy())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getBusinessTaskListDeptSelectList")
    @ApiOperation("获取任务，会计下拉框数据源")
    public Result<BusinessDeptSelectDTO> getBusinessTaskListDeptSelectList(@RequestHeader("deptId") Long deptId, BusinessTaskVO vo, @RequestParam("tabType") @ApiParam("菜单类型，1-账期任务，2-任务管理，3-我的任务") Integer tabType) {
        List<BusinessTaskAccountingDeptCountDTO> records;
        List<BusinessTaskAccountingDeptCountDTO> businessDeptRecords;
        if (tabType == 1) {
            records = businessTaskService.businessTaskCountForPeriod(deptId, vo);
            businessDeptRecords = businessTaskService.businessTaskBusinessDeptCountForPeriod(deptId, vo);
        } else if (tabType == 2) {
            records = businessTaskService.businessTaskCountForManager(deptId, vo);
            businessDeptRecords = businessTaskService.businessTaskBusinessDeptCountForManager(deptId, vo);
        } else {
            records = businessTaskService.businessTaskCountForMy(deptId, vo);
            businessDeptRecords = businessTaskService.businessTaskBusinessDeptCountForMy(deptId, vo);
        }
        List<Long> deptIds = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(records)) {
            deptIds.addAll(records.stream().map(BusinessTaskAccountingDeptCountDTO::getAccountingDeptId).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(businessDeptRecords)) {
            deptIds.addAll(businessDeptRecords.stream().map(BusinessTaskAccountingDeptCountDTO::getAccountingDeptId).collect(Collectors.toList()));
        }
        Map<Long, String> deptMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getAccountingDeptId())
                                .deptName(deptMap.getOrDefault(e.getAccountingDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getAccountingDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .businessDeptList(ObjectUtils.isEmpty(businessDeptRecords) ? Lists.newArrayList() : businessDeptRecords.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getAccountingDeptId())
                                .deptName(deptMap.getOrDefault(e.getAccountingDeptId(), ""))
                                .deptType(3)
                                .employeeNames("")
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getAccountingCashierListDeptSelectList")
    @ApiOperation("获取账务列表，顾问会计下拉框数据源")
    public Result<BusinessDeptSelectV2DTO> getAccountingCashierListDeptSelectList(@RequestHeader("deptId") Long deptId, @RequestParam("type") @ApiParam("1-入账，2-流水，3-改账") Integer type) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<CommonDeptCountDTO> customerAccountingDeptCountList = customerServiceCashierAccountingService.accountingCashierCustomerAccountingDeptCountList(userDeptDTO, type);
        List<CommonDeptCountDTO> periodAccountingDeptCountList = customerServiceCashierAccountingService.accountingCashierPeriodAccountingDeptCountList(userDeptDTO, type);
        List<CommonDeptCountDTO> customerAdvisorDeptCountList = customerServiceCashierAccountingService.accountingCashierCustomerAdvisorDeptCountList(userDeptDTO, type);
        List<CommonDeptCountDTO> periodAdvisorDeptCountList = customerServiceCashierAccountingService.accountingCashierPeriodAdvisorDeptCountList(userDeptDTO, type);
        if (ObjectUtils.isEmpty(customerAccountingDeptCountList) && ObjectUtils.isEmpty(periodAccountingDeptCountList) && ObjectUtils.isEmpty(customerAdvisorDeptCountList) && ObjectUtils.isEmpty(periodAdvisorDeptCountList)) {
            return Result.ok(new BusinessDeptSelectV2DTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(customerAccountingDeptCountList) ? Lists.newArrayList() :
                customerAccountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(periodAccountingDeptCountList) ? Lists.newArrayList() :
                periodAccountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(customerAdvisorDeptCountList) ? Lists.newArrayList() :
                customerAdvisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(periodAdvisorDeptCountList) ? Lists.newArrayList() :
                periodAdvisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectV2DTO.builder()
                .customerServiceAccoutningDeptList(ObjectUtils.isEmpty(customerAccountingDeptCountList) ? Lists.newArrayList() : customerAccountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .periodAccoutningDeptList(ObjectUtils.isEmpty(periodAccountingDeptCountList) ? Lists.newArrayList() : periodAccountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .customerServiceAdvisorDeptList(ObjectUtils.isEmpty(customerAdvisorDeptCountList) ? Lists.newArrayList() : customerAdvisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .periodAdvisorDeptList(ObjectUtils.isEmpty(periodAdvisorDeptCountList) ? Lists.newArrayList() : periodAdvisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getAccountingCashierMiniListDeptSelectList")
    @ApiOperation("获取账务miniList，顾问会计下拉框数据源")
    public Result<BusinessDeptSelectV2DTO> getAccountingCashierMiniListDeptSelectList(@RequestHeader("deptId") Long deptId, @RequestParam(value = "queryDeptId", required = false) @ApiParam("组织范围") Long queryDeptId,
                                                                                    @RequestParam(value = "deptIds", required = false) @ApiParam("组织范围") String deptIdStr,
                                                                                    @RequestParam("accountingCashierType") @ApiParam("1-入账，2-流水，3-改账") Integer accountingCashierType,
                                                                                    @RequestParam("statisticType") @ApiParam("统计类型，0-未开户,1-银行部分缺，2-待创建，3-待重提，4-待交付，5-异常，6-有变更，7-缺材料，8-交付待提交，9-待顾问创建，10-待回单中心创建，11-银企待创建") Integer statisticType) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIdStr);
        List<CommonDeptCountDTO> accountingDeptCountList = customerServiceCashierAccountingService.accountingCashierMiniListAccountingDeptCountList(userDeptDTO, queryDeptIds, accountingCashierType, statisticType);
        List<CommonDeptCountDTO> advisorDeptCountList = customerServiceCashierAccountingService.accountingCashierMiniListAdvisorDeptCountList(userDeptDTO, queryDeptIds, accountingCashierType, statisticType);
        if (ObjectUtils.isEmpty(accountingDeptCountList) && ObjectUtils.isEmpty(advisorDeptCountList)) {
            return Result.ok(new BusinessDeptSelectV2DTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() :
                accountingDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() :
                advisorDeptCountList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectV2DTO.builder()
                .periodAccoutningDeptList(ObjectUtils.isEmpty(accountingDeptCountList) ? Lists.newArrayList() : accountingDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .periodAdvisorDeptList(ObjectUtils.isEmpty(advisorDeptCountList) ? Lists.newArrayList() : advisorDeptCountList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/getMaterialDeliverDeptSelectList")
    @ApiOperation("获取材料交接，提交小组下拉框数据源（从miniList获取时，需要传默认的参数；从材料交接列表进来时，不需要传参数）")
    public Result<List<BusinessDeptDTO>> getMaterialDeliverDeptSelectList(@RequestHeader("deptId") Long deptId,
                                                                          @RequestParam(value = "materialDeliverType", required = false) @ApiParam("1-银行流水，2-普通入账，3-凭票入账") Integer materialDeliverType,
                                                                          @RequestParam(value = "materialDeliverAnalysisStatus", required = false) @ApiParam("解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止，从miniList来的必传") Integer materialDeliverAnalysisStatus,
                                                                          @RequestParam(value = "materialDeliverAnalysisResult", required = false) @ApiParam("解析结果，1-正常，2-异常，从miniList来的必传") Integer materialDeliverAnalysisResult,
                                                                          @RequestParam(value = "materialDeliverPushStatus", required = false) @ApiParam("推送状态，1-待推送，2-已推送, 从miniList来的必传") Integer materialDeliverPushStatus) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<CommonDeptCountDTO> deptCountDTOS = materialDeliverService.getMaterialDeliverDeptSelectList(userDeptDTO, materialDeliverType, materialDeliverAnalysisStatus, materialDeliverAnalysisResult, materialDeliverPushStatus);
        if (ObjectUtils.isEmpty(deptCountDTOS)) {
            return Result.ok(Collections.emptyList());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(deptCountDTOS) ? Lists.newArrayList() :
                deptCountDTOS.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(ObjectUtils.isEmpty(deptCountDTOS) ? Lists.newArrayList() : deptCountDTOS.stream().map(e ->
                BusinessDeptDTO.builder()
                        .deptId(e.getDeptId())
                        .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                        .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                        .dataCount(e.getDataCount())
                        .build()).collect(Collectors.toList()));
    }

    @GetMapping("/getSettlementOrderDataDeptSelectBySettlementOrderId")
    @ApiOperation("获取结算单明细顾问，会计下拉框数据源（查看详情时的明细列表）")
    public Result<SettlementBusinessDeptSelectDTO> getSettlementOrderDataDeptSelectBySettlementOrderId(@RequestHeader("deptId") Long deptId, SettlementOrderDataSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<SettlementOrderDataDTO> allData = settlementOrderService.settlementOrderDataListBySettlementOrderId(vo).getRecords();
        if (ObjectUtils.isEmpty(allData)) {
            return Result.ok(new SettlementBusinessDeptSelectDTO());
        }
        Map<Long, List<SettlementOrderDataDTO>> customerAdvisorMap = allData.stream().filter(d -> !Objects.isNull(d.getCustomerServiceAdvisorDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getCustomerServiceAdvisorDeptId));
        Map<Long, List<SettlementOrderDataDTO>> customerAccountingMap = allData.stream().filter(d -> !Objects.isNull(d.getCustomerServiceAccountingDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getCustomerServiceAccountingDeptId));
        Map<Long, List<SettlementOrderDataDTO>> periodAdvisorMap = allData.stream().filter(d -> !Objects.isNull(d.getPeriodAdvisorDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getPeriodAdvisorDeptId));
        Map<Long, List<SettlementOrderDataDTO>> periodAccountingMap = allData.stream().filter(d -> !Objects.isNull(d.getPeriodAccountingDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getPeriodAccountingDeptId));
        Map<Long, List<SettlementOrderDataDTO>> createDeptMap = allData.stream().filter(d -> !Objects.isNull(d.getCreateDeptId())).collect(Collectors.groupingBy(SettlementOrderDataDTO::getCreateDeptId));
        return Result.ok(SettlementBusinessDeptSelectDTO.builder()
                .customerAdvisorDeptList(ObjectUtils.isEmpty(customerAdvisorMap) ? Lists.newArrayList() : customerAdvisorMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getCustomerServiceAdvisorDeptName())
                                .deptType(1)
                                .employeeNames(e.getValue().get(0).getCustomerServiceAdvisorEmployeeName())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .customerAccountantDeptList(ObjectUtils.isEmpty(customerAccountingMap) ? Lists.newArrayList() : customerAccountingMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getCustomerServiceAccountingDeptName())
                                .deptType(2)
                                .employeeNames(e.getValue().get(0).getCustomerServiceAccountingEmployeeName())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .periodAdvisorDeptList(ObjectUtils.isEmpty(periodAdvisorMap) ? Lists.newArrayList() : periodAdvisorMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getPeriodAdvisorDeptName())
                                .deptType(1)
                                .employeeNames(e.getValue().get(0).getPeriodAdvisorEmployeeName())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .periodAccountantDeptList(ObjectUtils.isEmpty(periodAccountingMap) ? Lists.newArrayList() : periodAccountingMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getPeriodAccountingDeptName())
                                .deptType(2)
                                .employeeNames(e.getValue().get(0).getPeriodAccountingEmployeeName())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .createDeptList(ObjectUtils.isEmpty(createDeptMap) ? Lists.newArrayList() : createDeptMap.entrySet().stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getKey())
                                .deptName(e.getValue().get(0).getCreateDeptName())
                                .employeeNames(e.getValue().get(0).getCreateBy())
                                .dataCount((long) e.getValue().size())
                                .build()).collect(Collectors.toList()))
                .build());
    }
    @GetMapping("/workOrderInitiateUndertakeDeptSelect")
    @ApiOperation("获取工单列表，发起方、承接方下拉数据")
    public Result<InitiateUndertakeDeptDTO> workOrderInitiateUndertakeDeptSelect(@RequestHeader("deptId") Long deptId,
                                                                                 @RequestParam("tabType") @ApiParam("tab类型，1-我方发起，2-我方承接，3-待承接，4-待处理") Integer tabType) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<CommonDeptCountDTO> initiateDeptList = workOrderService.workOrderInitiateDeptList(userDeptDTO, tabType);
        List<CommonDeptCountDTO> undertakeDeptList = workOrderService.workOrderUndertakeDeptList(userDeptDTO, tabType);
        if (ObjectUtils.isEmpty(initiateDeptList) && ObjectUtils.isEmpty(undertakeDeptList)) {
            return Result.ok(new InitiateUndertakeDeptDTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(initiateDeptList) ? Lists.newArrayList() :
                initiateDeptList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(undertakeDeptList) ? Lists.newArrayList() :
                undertakeDeptList.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(InitiateUndertakeDeptDTO.builder()
                .initiateDeptList(ObjectUtils.isEmpty(initiateDeptList) ? Lists.newArrayList() : initiateDeptList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .undertakeDeptList(ObjectUtils.isEmpty(undertakeDeptList) ? Lists.newArrayList() : undertakeDeptList.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/customerServiceIncomeInfoDeptSelect")
    @ApiOperation("收入-预警统计获取顾问、会计下拉数据")
    public Result<BusinessDeptSelectDTO> customerServiceIncomeInfoDeptSelect(@RequestHeader("deptId") Long deptId) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<CommonDeptCountDTO> advisorDeptCount = customerServiceService.customerServiceIncomeAdvisorDeptCount(userDeptDTO);
        List<CommonDeptCountDTO> accountingDeptCount = customerServiceService.customerServiceIncomeAccountingDeptCount(userDeptDTO);
        if (ObjectUtils.isEmpty(advisorDeptCount) && ObjectUtils.isEmpty(accountingDeptCount)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() :
                advisorDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() :
                accountingDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() : accountingDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() : advisorDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/customerServiceYearIncomeInfoDeptSelect")
    @ApiOperation("收入-年度统计获取顾问、会计下拉数据")
    public Result<BusinessDeptSelectDTO> customerServiceYearIncomeInfoDeptSelect(@RequestHeader("deptId") Long deptId) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<CommonDeptCountDTO> advisorDeptCount = customerServiceService.customerServiceYearIncomeAdvisorDeptCount(userDeptDTO);
        List<CommonDeptCountDTO> accountingDeptCount = customerServiceService.customerServiceYearIncomeAccountingDeptCount(userDeptDTO);
        if (ObjectUtils.isEmpty(advisorDeptCount) && ObjectUtils.isEmpty(accountingDeptCount)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() :
                advisorDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIds.addAll(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() :
                accountingDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() : accountingDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() : advisorDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/syncItemDeptSelect")
    @ApiOperation("待申报（按税种）/待扣款（按税种）获取顾问、会计下拉数据")
    public Result<BusinessDeptSelectDTO> syncItemDeptSelect(@RequestHeader("deptId") Long deptId,
                                                            @RequestParam("type") @ApiParam("类型，1-待申报（按税种），2-待扣款（按税种）") Integer type,
                                                            @RequestParam(value = "deptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                            @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织ids") String deptIds) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIds);
        Integer period = DateUtils.getPrePeriod();
        Integer nowPeriod = DateUtils.getNowPeriod();
        Integer month = nowPeriod % 100;
        List<String> reportType = null;
        if (Lists.newArrayList(2, 3, 5, 6, 8, 9, 11, 12).contains(month)) {
            reportType = Lists.newArrayList("月", "次");
        } else if (month == 4 || month == 10) {
            reportType = Lists.newArrayList("月", "次", "季");
        } else if (month == 7) {
            reportType = Lists.newArrayList("月", "次", "季", "半年");
        }
        List<CommonDeptCountDTO> advisorDeptCount = openApiSyncItemMapper.syncItemAdvisorDeptList(userDeptDTO, type, queryDeptIds, period, nowPeriod, reportType);
        List<CommonDeptCountDTO> accountingDeptCount = openApiSyncItemMapper.syncItemAccountingDeptList(userDeptDTO, type, queryDeptIds, period, nowPeriod, reportType);
        if (ObjectUtils.isEmpty(advisorDeptCount) && ObjectUtils.isEmpty(accountingDeptCount)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIdList = Lists.newArrayList();
        deptIdList.addAll(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() :
                advisorDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIdList.addAll(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() :
                accountingDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() : accountingDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() : advisorDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/bankSelect")
    @ApiOperation("银行下拉框数据")
    public Result<List<Bank>> bankSelect(@RequestParam("bankName") @ApiParam("银行名称搜索") String bankName) {
        return Result.ok(bankService.bankSelect(bankName));
    }

    @GetMapping("/taxCheckList")
    @ApiOperation("获取税种核定下拉框数据")
    public Result<List<String>> taxCheckList() {
        return Result.ok(customerServiceService.taxCheckList());
    }

    @GetMapping("/getMonth")
    @ApiOperation("获取月份")
    public Result<GetMonthDTO> getMonth(
            @RequestParam(value = "diff", required = false) @ApiParam("差额，0表示当前月，1表示下个月，-1表示上个月，以此类推") Integer diff
    ) {
        return Result.ok(customerServiceService.getMonth(diff));
    }

    @GetMapping("/getYearList")
    @ApiOperation("获取年份下拉框数据")
    public Result<List<String>> getYearList() {
        return Result.ok(Arrays.asList(yearProperties.getList().split(",")));
    }

    @PostMapping("/customerListByBatchCustomerName")
    @ApiOperation("客户名称批量导出查询")
    public Result<CustomerBatchSearchResultDTO> customerListByBatchCustomerName(@RequestBody List<String> customerNames) {
        return Result.ok(customerServiceService.customerListByBatchCustomerName(customerNames));
    }

    @GetMapping("/getAllBankNames")
    @ApiOperation("获取所有银行名称")
    public Result<List<String>> getAllBankNames() {
        return Result.ok(bankService.getAllBankNames());
    }

    @GetMapping("/customerServiceBankList")
    @ApiOperation("客户银行账号列表下拉数据")
    public Result<List<CustomerServiceBankAccount>> customerServiceBankList(@RequestParam("customerServiceId") @ApiParam("客户id") Long customerServiceId,
                                                                            @RequestParam(value = "period", required = false) @ApiParam("账期,yyyyMM") Integer period) {
        return Result.ok(customerServiceService.customerServiceAccountingCashierBankList(customerServiceId, period));
    }

    @GetMapping("/materialFileBankSelect")
    @ApiOperation("账期材料列表-银行选择")
    public Result<List<String>> materialFileBankSelect(@RequestParam("customerServicePeriodMonthId") Long customerServicePeriodMonthId) {
        return Result.ok(customerServiceCashierAccountingService.materialFileBankSelect(customerServicePeriodMonthId));
    }

    @GetMapping("/materialDetailErrorMsgList")
    @ApiOperation("交接单明细异常原因下拉数据")
    public Result<List<String>> materialDetailErrorMsgList(@RequestParam("materialDeliverId") Long materialDeliverId) {
        return Result.ok(customerServicePeriodMonthService.materialFilePeriodSelect(materialDeliverId));
    }

    @GetMapping("/qualityCheckingItemSelect")
    @ApiOperation("质检事项下拉数据，文本匹配由前端实现")
    public Result<List<QualityCheckingItem>> qualityCheckingItemSelect(@RequestHeader("deptId") Long deptId,
                                                                       @RequestParam(value = "qualityCheckingType", required = false) @ApiParam("质检类型，1-账务问题，2-风险提示") Integer qualityCheckingType,
                                                                       @RequestParam(value = "qualityCheckingCycle", required = false) @ApiParam("质检周期，1-单期，2-累计") Integer qualityCheckingCycle) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (sysDept.getDeptType() == 1) {
            qualityCheckingType = QualityCheckingType.RISK_WARNING.getCode();
        }
        return Result.ok(qualityCheckingItemMapper.qualityCheckingItemSelect(qualityCheckingType, qualityCheckingCycle));
    }

    @GetMapping("/qualityCheckingMiniListDeptSelect")
    @ApiOperation("工作台-质检获取顾问、会计下拉数据")
    public Result<BusinessDeptSelectDTO> qualityCheckingMiniListDeptSelect(@RequestHeader("deptId") Long deptId,
                                                                           @RequestParam(value = "queryDeptId", required = false) @ApiParam("选择的组织id") Long queryDeptId,
                                                                           @RequestParam(value = "deptIds", required = false) @ApiParam("选择的组织id（多选）") String deptIds,
                                                                           @RequestParam("qualityCheckingType") @ApiParam("质检类型，1-账务问题，2-风险提示") Integer qualityCheckingType) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIds);
        List<CommonDeptCountDTO> advisorDeptCount = qualityCheckingResultMapper.qualityCheckingExceptionAdvisorDeptList(userDeptDTO, qualityCheckingType, queryDeptIds);
        List<CommonDeptCountDTO> accountingDeptCount = qualityCheckingResultMapper.qualityCheckingExceptionAccountingDeptList(userDeptDTO, qualityCheckingType, queryDeptIds);
        if (ObjectUtils.isEmpty(advisorDeptCount) && ObjectUtils.isEmpty(accountingDeptCount)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIdList = Lists.newArrayList();
        deptIdList.addAll(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() :
                advisorDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIdList.addAll(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() :
                accountingDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() : accountingDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() : advisorDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/qualityCheckingResultDeptSelect")
    @ApiOperation("质检结果-获取顾问、会计下拉数据，进入时调用一次")
    public Result<BusinessDeptSelectDTO> qualityCheckingResultDeptSelect(@RequestHeader("deptId") Long deptId, QualityCheckingResultVO vo) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<CommonDeptCountDTO> advisorDeptCount = qualityCheckingResultMapper.qualityCheckingResultAdvisorDeptList(userDeptDTO, vo);
        List<CommonDeptCountDTO> accountingDeptCount = qualityCheckingResultMapper.qualityCheckingResultAccountingDeptList(userDeptDTO, vo);
        if (ObjectUtils.isEmpty(advisorDeptCount) && ObjectUtils.isEmpty(accountingDeptCount)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIdList = Lists.newArrayList();
        deptIdList.addAll(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() :
                advisorDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIdList.addAll(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() :
                accountingDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() : accountingDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() : advisorDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/qualityCheckingRecordDeptSelect")
    @ApiOperation("质检记录-获取顾问、会计下拉数据，进入时调用一次")
    public Result<BusinessDeptSelectDTO> qualityCheckingRecordDeptSelect(@RequestHeader("deptId") Long deptId, QualityCheckingRecordVO vo) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        List<CommonDeptCountDTO> advisorDeptCount = qualityCheckingRecordMapper.qualityCheckingRecordAdvisorDeptList(userDeptDTO, vo);
        List<CommonDeptCountDTO> accountingDeptCount = qualityCheckingRecordMapper.qualityCheckingRecordAccountingDeptList(userDeptDTO, vo);
        if (ObjectUtils.isEmpty(advisorDeptCount) && ObjectUtils.isEmpty(accountingDeptCount)) {
            return Result.ok(new BusinessDeptSelectDTO());
        }
        List<Long> deptIdList = Lists.newArrayList();
        deptIdList.addAll(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() :
                advisorDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        deptIdList.addAll(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() :
                accountingDeptCount.stream().map(CommonDeptCountDTO::getDeptId).collect(Collectors.toList()));
        Map<Long, String> deptMap = remoteDeptService.getByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        return Result.ok(BusinessDeptSelectDTO.builder()
                .accountantDeptList(ObjectUtils.isEmpty(accountingDeptCount) ? Lists.newArrayList() : accountingDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(2)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .advisorDeptList(ObjectUtils.isEmpty(advisorDeptCount) ? Lists.newArrayList() : advisorDeptCount.stream().map(e ->
                        BusinessDeptDTO.builder()
                                .deptId(e.getDeptId())
                                .deptName(deptMap.getOrDefault(e.getDeptId(), ""))
                                .deptType(1)
                                .employeeNames(employeeMap.getOrDefault(e.getDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")))
                                .dataCount(e.getDataCount())
                                .build()).collect(Collectors.toList()))
                .build());
    }

    @GetMapping("/tagSelectList")
    @ApiOperation("统一的标签池选择数据（如果是从业务详情（如客户详情）中获取，还需要传businessType和businessId）")
    public Result<TagSelectDTO> tagSelectList(@RequestHeader("deptId") Long deptId,
                                              @RequestParam(required = false, value = "businessType") @ApiParam("业务类型，1-服务，2-账期，3-新户流转") Integer businessType,
                                              @RequestParam(required = false, value = "businessId") @ApiParam("业务id，对应业务类型的唯一主键id") Long businessId) {
        return Result.ok(tagService.tagSelectList(deptId, businessType, businessId));
    }
}
