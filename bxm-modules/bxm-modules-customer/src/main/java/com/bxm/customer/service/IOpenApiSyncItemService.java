package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.OpenApiSyncItem;
import com.bxm.customer.domain.vo.CommonNoticeVO;
import com.bxm.thirdpart.api.domain.ReportDeductionGetDTO;

/**
 * 第三方申报同步客户详情Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IOpenApiSyncItemService extends IService<OpenApiSyncItem>
{
    /**
     * 查询第三方申报同步客户详情
     * 
     * @param id 第三方申报同步客户详情主键
     * @return 第三方申报同步客户详情
     */
    public OpenApiSyncItem selectOpenApiSyncItemById(Long id);

    /**
     * 查询第三方申报同步客户详情列表
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 第三方申报同步客户详情集合
     */
    public List<OpenApiSyncItem> selectOpenApiSyncItemList(OpenApiSyncItem openApiSyncItem);

    /**
     * 新增第三方申报同步客户详情
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 结果
     */
    public int insertOpenApiSyncItem(OpenApiSyncItem openApiSyncItem);

    /**
     * 修改第三方申报同步客户详情
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 结果
     */
    public int updateOpenApiSyncItem(OpenApiSyncItem openApiSyncItem);

    /**
     * 批量删除第三方申报同步客户详情
     * 
     * @param ids 需要删除的第三方申报同步客户详情主键集合
     * @return 结果
     */
    public int deleteOpenApiSyncItemByIds(Long[] ids);

    /**
     * 删除第三方申报同步客户详情信息
     * 
     * @param id 第三方申报同步客户详情主键
     * @return 结果
     */
    public int deleteOpenApiSyncItemById(Long id);

    List<OpenApiSyncItem> selectBySyncRecordId(Long recordId);

    List<OpenApiSyncItem> selectBySyncCustomerId(Long syncCustomerId);

    void dealReportDeductionList(ReportDeductionGetDTO reportDeductionResult, Long syncRecordId, Long customerServiceId);
}
