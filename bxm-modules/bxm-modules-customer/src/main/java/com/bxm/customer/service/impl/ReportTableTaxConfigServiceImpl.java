package com.bxm.customer.service.impl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.OpenApiSyncItem;
import com.bxm.thirdpart.api.domain.KouKuanDTO;
import com.bxm.thirdpart.api.domain.ShenBaoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.ReportTableTaxConfigMapper;
import com.bxm.customer.domain.ReportTableTaxConfig;
import com.bxm.customer.service.IReportTableTaxConfigService;
import org.springframework.util.ObjectUtils;

/**
 * 申报税种对应Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class ReportTableTaxConfigServiceImpl extends ServiceImpl<ReportTableTaxConfigMapper, ReportTableTaxConfig> implements IReportTableTaxConfigService
{
    @Autowired
    private ReportTableTaxConfigMapper reportTableTaxConfigMapper;

    /**
     * 查询申报税种对应
     * 
     * @param id 申报税种对应主键
     * @return 申报税种对应
     */
    @Override
    public ReportTableTaxConfig selectReportTableTaxConfigById(Long id)
    {
        return reportTableTaxConfigMapper.selectReportTableTaxConfigById(id);
    }

    /**
     * 查询申报税种对应列表
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 申报税种对应
     */
    @Override
    public List<ReportTableTaxConfig> selectReportTableTaxConfigList(ReportTableTaxConfig reportTableTaxConfig)
    {
        return reportTableTaxConfigMapper.selectReportTableTaxConfigList(reportTableTaxConfig);
    }

    /**
     * 新增申报税种对应
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 结果
     */
    @Override
    public int insertReportTableTaxConfig(ReportTableTaxConfig reportTableTaxConfig)
    {
        reportTableTaxConfig.setCreateTime(DateUtils.getNowDate());
        return reportTableTaxConfigMapper.insertReportTableTaxConfig(reportTableTaxConfig);
    }

    /**
     * 修改申报税种对应
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 结果
     */
    @Override
    public int updateReportTableTaxConfig(ReportTableTaxConfig reportTableTaxConfig)
    {
        reportTableTaxConfig.setUpdateTime(DateUtils.getNowDate());
        return reportTableTaxConfigMapper.updateReportTableTaxConfig(reportTableTaxConfig);
    }

    /**
     * 批量删除申报税种对应
     * 
     * @param ids 需要删除的申报税种对应主键
     * @return 结果
     */
    @Override
    public int deleteReportTableTaxConfigByIds(Long[] ids)
    {
        return reportTableTaxConfigMapper.deleteReportTableTaxConfigByIds(ids);
    }

    /**
     * 删除申报税种对应信息
     * 
     * @param id 申报税种对应主键
     * @return 结果
     */
    @Override
    public int deleteReportTableTaxConfigById(Long id)
    {
        return reportTableTaxConfigMapper.deleteReportTableTaxConfigById(id);
    }

    @Override
    public List<OpenApiSyncItem> dealReportList(List<ShenBaoDTO> shenBaoList, List<OpenApiSyncItem> itemList) {
        if (ObjectUtils.isEmpty(shenBaoList)) {
            return itemList;
        }
        List<ReportTableTaxConfig> configs = list();
        for (ShenBaoDTO shenBao : shenBaoList) {
            String reportTableName = shenBao.getName();
            if (StringUtils.isEmpty(reportTableName)) {
                continue;
            }
            List<ReportTableTaxConfig> thisConfigs = configs.stream().filter(row -> Objects.equals(row.getTableName(), reportTableName)).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(thisConfigs)) {
                continue;
            }
            for (ReportTableTaxConfig config : thisConfigs) {
                Predicate<OpenApiSyncItem> predicate = row -> true; // 初始为恒成立的条件
                if (!StringUtils.isEmpty(config.getCategoryName())) {
                    predicate = predicate.and(row -> Objects.equals(row.getItemCategoryName(), config.getCategoryName()));
                }
                if (!StringUtils.isEmpty(config.getItemName())) {
                    predicate = predicate.and(row -> Objects.equals(row.getItemName(), config.getItemName()));
                }
                if (!StringUtils.isEmpty(config.getReportType())) {
                    predicate = predicate.and(row -> Objects.equals(row.getReportType(), config.getReportType()));
                }
                itemList.stream().filter(predicate)
                        .forEach(row -> {
                            row.setIsReport("是");
                            row.setIsPaid("是");
                            row.setReportDate(shenBao.getNssbrq());
                            row.setActualPayTaxAmount("0");
                            row.setTaxPeriodStart(shenBao.getSkssqq());
                            row.setTaxPeriodEnd(shenBao.getSkssqz());
                        });
            }
        }
        return itemList;
    }

    @Override
    public List<OpenApiSyncItem> dealDeductionList(List<KouKuanDTO> kouKuanList, List<OpenApiSyncItem> itemList) {
        if (ObjectUtils.isEmpty(kouKuanList)) {
            return itemList;
        }
        OpenApiSyncItem openApiSyncItem = itemList.get(0);
        Integer prePeriod = DateUtils.getPrePeriod();
        String nowDate = DateUtils.periodToYeaMonth(DateUtils.getNowPeriod());
        for (KouKuanDTO kouKuan : kouKuanList) {
            String categoryName = kouKuan.getZsxm();
            String itemName = kouKuan.getZspm();
            if (StringUtils.isEmpty(categoryName) && StringUtils.isEmpty(itemName)) {
                continue;
            }
            Predicate<OpenApiSyncItem> predicate = row -> true; // 初始为恒成立的条件
            if (!StringUtils.isEmpty(categoryName)) {
                predicate = predicate.and(row -> Objects.equals(row.getItemCategoryName(), categoryName));
            }
            if (!StringUtils.isEmpty(itemName)) {
                predicate = predicate.and(row -> Objects.equals(row.getItemName(), itemName));
            }
            List<OpenApiSyncItem> items = itemList.stream().filter(predicate).collect(Collectors.toList());
            if (!StringUtils.isEmpty(kouKuan.getSkssqz())) {
                String yearMonth = kouKuan.getSkssqz().substring(0, 7);
                if (DateUtils.yearMonthToPeriod(yearMonth) >= prePeriod) {
                    if (!ObjectUtils.isEmpty(items)) {
                        items.forEach(row -> {
                            row.setIsPaid("是");
                            row.setActualPayTaxAmount(kouKuan.getSjje());
                            row.setTaxPeriodStart(kouKuan.getSkssqq());
                            row.setTaxPeriodEnd(kouKuan.getSkssqz());
                            row.setPayDate(kouKuan.getJkrq());
                        });
                    } else {
                        itemList.add(new OpenApiSyncItem().setSycRecordId(openApiSyncItem.getSycRecordId())
                                .setSyncCustomerId(openApiSyncItem.getSyncCustomerId())
                                .setCustomerName(openApiSyncItem.getCustomerName())
                                .setTaxNumber(openApiSyncItem.getTaxNumber())
                                .setItemCategoryName(kouKuan.getZsxm())
                                .setItemName(kouKuan.getZspm())
                                .setActualPayTaxAmount(kouKuan.getSjje())
                                .setIsReport("是")
                                .setIsPaid("是")
                                .setReportType("次")
                                .setReportPeriod(nowDate)
                                .setTaxPeriodStart(kouKuan.getSkssqq())
                                .setTaxPeriodEnd(kouKuan.getSkssqz())
                                .setPayDate(kouKuan.getJkrq()));
                    }
                }
            }
        }
        return itemList;
    }

    @Override
    public List<OpenApiSyncItem> dealNotDeductionList(List<KouKuanDTO> weiKouKuanList, List<OpenApiSyncItem> itemList) {
        if (ObjectUtils.isEmpty(weiKouKuanList)) {
            return itemList;
        }
        OpenApiSyncItem openApiSyncItem = itemList.get(0);
        Integer prePeriod = DateUtils.getPrePeriod();
        String nowDate = DateUtils.periodToYeaMonth(DateUtils.getNowPeriod());
        for (KouKuanDTO weiKouKuan : weiKouKuanList) {
            String categoryName = weiKouKuan.getZsxm();
            String itemName = weiKouKuan.getZspm();
            if (StringUtils.isEmpty(categoryName) && StringUtils.isEmpty(itemName)) {
                continue;
            }
            Predicate<OpenApiSyncItem> predicate = row -> true; // 初始为恒成立的条件
            if (!StringUtils.isEmpty(categoryName)) {
                predicate = predicate.and(row -> Objects.equals(row.getItemCategoryName(), categoryName));
            }
            if (!StringUtils.isEmpty(itemName)) {
                predicate = predicate.and(row -> Objects.equals(row.getItemName(), itemName));
            }
            List<OpenApiSyncItem> items = itemList.stream().filter(predicate).collect(Collectors.toList());
            if (!StringUtils.isEmpty(weiKouKuan.getSkssqz())) {
                String yearMonth = weiKouKuan.getSkssqz().substring(0, 7);
                if (DateUtils.yearMonthToPeriod(yearMonth) >= prePeriod) {
                    if (!ObjectUtils.isEmpty(items)) {
                        items.forEach(row -> {
                            row.setIsPaid("否");
                            row.setActualPayTaxAmount(weiKouKuan.getSjje());
                            row.setTaxPeriodStart(weiKouKuan.getSkssqq());
                            row.setTaxPeriodEnd(weiKouKuan.getSkssqz());
                            row.setPayDate(weiKouKuan.getJkrq());
                        });
                    } else {
                        itemList.add(new OpenApiSyncItem().setSycRecordId(openApiSyncItem.getSycRecordId())
                                .setSyncCustomerId(openApiSyncItem.getSyncCustomerId())
                                .setCustomerName(openApiSyncItem.getCustomerName())
                                .setTaxNumber(openApiSyncItem.getTaxNumber())
                                .setItemCategoryName(weiKouKuan.getZsxm())
                                .setItemName(weiKouKuan.getZspm())
                                .setActualPayTaxAmount(weiKouKuan.getSjje())
                                .setIsReport("是")
                                .setIsPaid("否")
                                .setReportPeriod(nowDate)
                                .setReportType("次")
                                .setTaxPeriodStart(weiKouKuan.getSkssqq())
                                .setTaxPeriodEnd(weiKouKuan.getSkssqz()));
                    }
                }
            }
        }
        return itemList;
    }
}
