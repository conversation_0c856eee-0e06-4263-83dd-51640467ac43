package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 新户流转银行账号对象 c_new_customer_bank_account
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("新户流转指定流转组织表")
@Accessors(chain = true)
@TableName("c_new_customer_transfer_dept_config")
public class NewCustomerTransferDeptConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 发起组织id */
    @Excel(name = "发起组织id")
    @ApiModelProperty(value = "发起组织id")
    @TableField("create_dept_id")
    private Long createDeptId;

    /** 发起组织级别，1-集团，2-业务公司 */
    @Excel(name = "发起组织级别，1-集团，2-业务公司")
    @ApiModelProperty(value = "发起组织级别，1-集团，2-业务公司")
    @TableField("create_dept_level")
    private Long createDeptLevel;

    /** 流转目标组织id */
    @Excel(name = "流转目标组织id")
    @ApiModelProperty(value = "流转目标组织id")
    @TableField("target_dept_id")
    private Long targetDeptId;
}
