package com.bxm.customer.domain.dto.businessTask;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierSimpleDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:47
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessTaskDetailDTO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "标题")
    private String title;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "截止日期")
    private LocalDate deadline;

    @ApiModelProperty(value = "监管人ID")
    private Long adminUserId;

    @ApiModelProperty(value = "监管人名称")
    private String adminUserName;

    @ApiModelProperty(value = "执行人ID")
    private Long executeUserId;

    @ApiModelProperty(value = "执行人名称")
    private String executeUserName;

    @ApiModelProperty(value = "任务状态：1-待完成、2-待审核、3-已完结、4-已关闭、5-异常")
    private Integer status;

    @ApiModelProperty(value = "任务状态 文案")
    private String statusStr;

    @ApiModelProperty(value = "完成结果：1-正常完成、2-已开户无流水、3-未开户、4-银行部分缺、5-无需交付、6-无法完成")
    private Integer finishResult;

    @ApiModelProperty(value = "完成结果 文案")
    private String finishResultStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime executeTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty("介质")
    private List<Integer> medium;

    @ApiModelProperty("介质文案")
    private String mediumStr;

    @ApiModelProperty("银行材料数量")
    private Long bankMaterialFileCount;

    @ApiModelProperty("事项备忘")
    private String mattersNotes;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    private Integer materialIntegrity;

    @ApiModelProperty("对账单余额（格式化后的）")
    private String statementBalanceStr;

    @ApiModelProperty("同账期同客户同银行的流水交付单列表")
    private List<AccountingCashierSimpleDTO> flowCashierList;
}
