package com.bxm.customer.mapper;

import java.util.List;

import com.bxm.customer.api.domain.dto.RemoteCustomerTagDTO;
import com.bxm.customer.domain.dto.BusinessTagDTO;
import com.bxm.customer.domain.dto.tag.TagV2DTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CBusinessTagRelation;
import org.apache.ibatis.annotations.Param;

/**
 * 客户服务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Mapper
public interface CBusinessTagRelationMapper extends BaseMapper<CBusinessTagRelation>
{
    /**
     * 查询客户服务
     * 
     * @param id 客户服务主键
     * @return 客户服务
     */
    public CBusinessTagRelation selectCBusinessTagRelationById(Long id);

    /**
     * 查询客户服务列表
     * 
     * @param cBusinessTagRelation 客户服务
     * @return 客户服务集合
     */
    public List<CBusinessTagRelation> selectCBusinessTagRelationList(CBusinessTagRelation cBusinessTagRelation);

    /**
     * 新增客户服务
     * 
     * @param cBusinessTagRelation 客户服务
     * @return 结果
     */
    public int insertCBusinessTagRelation(CBusinessTagRelation cBusinessTagRelation);

    /**
     * 修改客户服务
     * 
     * @param cBusinessTagRelation 客户服务
     * @return 结果
     */
    public int updateCBusinessTagRelation(CBusinessTagRelation cBusinessTagRelation);

    /**
     * 删除客户服务
     * 
     * @param id 客户服务主键
     * @return 结果
     */
    public int deleteCBusinessTagRelationById(Long id);

    /**
     * 批量删除客户服务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCBusinessTagRelationByIds(Long[] ids);

    void saveNewPeriodTagRelation(@Param("nowPeriod") Integer nowPeriod);

    void saveNewPeriodTagRelationByCustomerServiceIdsAndPeriod(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                              @Param("startPeriod") Integer startPeriod,
                                                              @Param("endPeriod") Integer endPeriod);

    List<BusinessTagDTO> selectTagsByBusinessTypeAndBusinessIds(@Param("businessIds") List<Long> businessIds, @Param("businessType") Integer businessType);

    void removeNoIncomeTagByBusinessIdsAndBusinessType(@Param("businessIds") List<Long> businessIds, @Param("businessType") Integer businessType);

    List<TagV2DTO> selectTagByBusinessTypeAndBusinessId(@Param("businessType") Integer businessType,
                                                        @Param("businessId") Long businessId);
}
