package com.bxm.customer.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 客户账务对象 c_customer_service_cashier_accounting
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Data
@ApiModel("客户账务对象")
@Accessors(chain = true)
@TableName("c_customer_service_cashier_accounting")
public class CustomerServiceCashierAccounting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 账期id */
    @Excel(name = "账期id")
    @TableField("customer_service_period_month_id")
    @ApiModelProperty(value = "账期id")
    private Long customerServicePeriodMonthId;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 标题 */
    @Excel(name = "标题")
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /** 账务类型，1-入账，2-流水，3-改账 */
    @Excel(name = "账务类型，1-入账，2-流水，3-改账")
    @TableField("type")
    @ApiModelProperty(value = "账务类型，1-入账，2-流水，3-改账")
    private Integer type;

    /** 是否凭票入账，0-否，1-是 */
    @Excel(name = "是否凭票入账，0-否，1-是")
    @TableField("has_ticket")
    @ApiModelProperty(value = "是否凭票入账，0-否，1-是")
    private Boolean hasTicket;

    /** 银行名称 */
    @Excel(name = "银行名称")
    @TableField("bank_name")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /** 银行账号 */
    @Excel(name = "银行账号")
    @TableField("bank_account_number")
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNumber;

    /** 是否有银行流水，0-否，1-是 */
    @Excel(name = "是否有银行流水，0-否，1-是")
    @TableField("has_bank_payment")
    @ApiModelProperty(value = "是否有银行流水，0-否，1-是")
    private Boolean hasBankPayment;

    /** 交付要求 */
    @Excel(name = "交付要求")
    @TableField("deliver_require")
    @ApiModelProperty(value = "交付要求")
    private String deliverRequire;

    /** 材料介质，1-电子，2-纸质，3-无，4-其他 */
    @Excel(name = "材料介质，1-电子，2-纸质，3-无，4-其他")
    @TableField("material_media")
    @ApiModelProperty(value = "材料介质，1-电子，2-纸质，3-无，4-其他")
    private Integer materialMedia;

    /** 交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，5-关闭交付 */
    @Excel(name = "交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，5-关闭交付")
    @TableField("deliver_status")
    @ApiModelProperty(value = "交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，5-关闭交付")
    private Integer deliverStatus;

    /** 交付结果，1-正常，2-无账务，3-无需交付，4-异常 */
    @Excel(name = "交付结果，1-正常，2-无账务，3-无需交付，4-异常")
    @TableField("deliver_result")
    @ApiModelProperty(value = "交付结果，1-正常，2-无账务，3-无需交付，4-异常")
    private Integer deliverResult;

    /** 是否交付变更，0-否，1-是 */
    @Excel(name = "是否交付变更，0-否，1-是")
    @TableField("has_changed")
    @ApiModelProperty(value = "是否交付变更，0-否，1-是")
    private Boolean hasChanged;

    /** 完成时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("complete_time")
    @ApiModelProperty(value = "完成时间")
    private LocalDateTime completeTime;

    @Excel(name = "完成用户id")
    @TableField("complete_user_id")
    @ApiModelProperty(value = "完成人用户id")
    private Long completeUserId;

    @Excel(name = "完成用户昵称")
    @TableField("complete_user_name")
    @ApiModelProperty(value = "完成人用户昵称")
    private String completeUserName;

    @Excel(name = "是否助理完成")
    @TableField("is_assistant_finish")
    @ApiModelProperty(value = "是否助理完成")
    private Boolean isAssistantFinish;

    @Excel(name = "入账时间")
    @TableField("in_time")
    @ApiModelProperty(value = "入账时间")
    private LocalDate inTime;

    @Excel(name = "结账时间")
    @TableField("end_time")
    @ApiModelProperty(value = "结账时间")
    private LocalDate endTime;

    @Excel(name = "首次结账用户id")
    @TableField("end_user_id")
    @ApiModelProperty(value = "首次结账用户id")
    private Long endUserId;

    @Excel(name = "银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成")
    @TableField("bank_payment_result")
    @ApiModelProperty(value = "银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成")
    private Integer bankPaymentResult;

    @Excel(name = "结账状态，1未入账、2已入账未结账、3已入账已结账")
    @TableField("settle_account_status")
    @ApiModelProperty(value = "结账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer settleAccountStatus;

    /** 交付备注 */
    @Excel(name = "交付备注")
    @TableField("deliver_remark")
    @ApiModelProperty(value = "交付备注")
    private String deliverRemark;

    @Excel(name = "利润取数更新时间")
    @TableField("profit_get_time")
    @ApiModelProperty(value = "利润取数更新时间")
    private LocalDateTime profitGetTime;

    /** 本年累计主营收入 */
    @Excel(name = "本年累计主营收入")
    @TableField("major_income_total")
    @ApiModelProperty(value = "本年累计主营收入")
    private BigDecimal majorIncomeTotal;

    /** 本年累计主营成本 */
    @Excel(name = "本年累计主营成本")
    @TableField("major_cost_total")
    @ApiModelProperty(value = "本年累计主营成本")
    private BigDecimal majorCostTotal;

    /** 本年累计会计利润 */
    @Excel(name = "本年累计会计利润")
    @TableField("profit_total")
    @ApiModelProperty(value = "本年累计会计利润")
    private BigDecimal profitTotal;

    /** 本年费用调增 */
    @Excel(name = "本年费用调增")
    @TableField("prior_year_expense_increase")
    @ApiModelProperty(value = "本年费用调增")
    private String priorYearExpenseIncrease;

    /** 个税申报人数 */
    @Excel(name = "个税申报人数")
    @TableField("tax_report_count")
    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    /** 本年个税申报工资总额 */
    @Excel(name = "本年个税申报工资总额")
    @TableField("tax_report_salary_total")
    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    /** 利润 **/
    @Excel(name = "利润")
    @TableField("profit")
    @ApiModelProperty(value = "利润")
    private String profit;

    /** 暂估 **/
    @Excel(name = "暂估")
    @TableField("tempesti")
    @ApiModelProperty(value = "暂估")
    private String tempesti;

    /** 福利费 **/
    @Excel(name = "福利费")
    @TableField("welfare")
    @ApiModelProperty(value = "福利费")
    private String welfare;

    /** 招待费 **/
    @Excel(name = "招待费")
    @TableField("enterain")
    @ApiModelProperty(value = "招待费")
    private String enterain;

    /** 原材料 **/
    @Excel(name = "原材料")
    @TableField("raw_material")
    @ApiModelProperty(value = "原材料")
    private String rawMaterial;

    /** 人工工资 **/
    @Excel(name = "人工工资")
    @TableField("labor_wages")
    @ApiModelProperty(value = "人工工资")
    private String laborWages;

    /** 费用 **/
    @Excel(name = "费用")
    @TableField("cost")
    @ApiModelProperty(value = "费用")
    private String cost;

    /** 期间费用 **/
    @Excel(name = "期间费用")
    @TableField("period_cost")
    @ApiModelProperty(value = "期间费用")
    private String periodCost;

    /** RPA执行结果：1-成功、0-失败 */
    @Excel(name = "RPA执行结果：1-成功、0-失败")
    @TableField("rpa_exe_result")
    @ApiModelProperty(value = "RPA执行结果：1-成功、0-失败")
    private Integer rpaExeResult;

    /** 报表状态是否平衡，0-否，1-是 */
    @Excel(name = "报表状态是否平衡")
    @TableField("table_status_balance")
    @ApiModelProperty(value = "报表状态是否平衡")
    private String tableStatusBalance;

    /** RPA查询时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "RPA查询时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("rpa_search_time")
    @ApiModelProperty(value = "RPA查询时间")
    private LocalDateTime rpaSearchTime;

    /** RPA备注 */
    @Excel(name = "RPA备注")
    @TableField("rpa_remark")
    @ApiModelProperty(value = "RPA备注")
    private String rpaRemark;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    @TableField("material_integrity")
    private Integer materialIntegrity;

    @ApiModelProperty("材料补充状态，1-无需处理，2-待核对，3-已核对")
    @TableField("material_supplement_status")
    private Integer materialSupplementStatus;

    @ApiModelProperty("最后操作类型")
    @TableField("last_oper_type")
    private String lastOperType;

    @ApiModelProperty("最后操作人")
    @TableField("last_oper_name")
    private String lastOperName;

    @ApiModelProperty("最后操作时间")
    @TableField("last_oper_time")
    private LocalDateTime lastOperTime;

    @ApiModelProperty("最后操作备注")
    @TableField("last_oper_remark")
    private String lastOperRemark;

    @ApiModelProperty("创建的操作类型")
    @TableField("create_oper_type")
    private String createOperType;

    @ApiModelProperty("最后入账时间")
    @TableField("last_in_time")
    private LocalDate lastInTime;

    @ApiModelProperty("最后完成用户id")
    @TableField("last_complete_user_id")
    private Long lastCompleteUserId;

    @ApiModelProperty("最后结账时间")
    @TableField("last_end_time")
    private LocalDate lastEndTime;

    @ApiModelProperty("最后结账用户id")
    @TableField("last_end_user_id")
    private Long lastEndUserId;

    @ApiModelProperty("DDL")
    @TableField("ddl")
    private LocalDate ddl;

    @ApiModelProperty("是否关闭交付，0-否，1-是")
    @TableField("is_close")
    private Integer isClose;

    /** 对账单余额 */
    @Excel(name = "对账单余额")
    @TableField("statement_balance")
    @ApiModelProperty(value = "对账单余额")
    private BigDecimal statementBalance;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    @TableField(exist = false)
    private Integer serviceType;

    @TableField(exist = false)
    private LocalDate periodCreateDate;

    @TableField(exist = false)
    private Long periodBusinessTopDeptId;

    @TableField(exist = false)
    private Long periodBusinessDeptId;
}
