package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 客户账务附件对象 c_customer_service_cashier_accounting_file
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Data
@ApiModel("客户账务附件对象")
@Accessors(chain = true)
@TableName("c_customer_service_cashier_accounting_file")
public class CustomerServiceCashierAccountingFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 账务id */
    @Excel(name = "账务id")
    @TableField("customer_service_cashier_accounting_id")
    @ApiModelProperty(value = "账务id")
    private Long customerServiceCashierAccountingId;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    @TableField("file_size")
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /** 文件类型，1-介质材料附件，2-交付附件，3-rpa附件 */
    @Excel(name = "文件类型，1-介质材料附件，2-交付附件，3-rpa附件")
    @TableField("file_type")
    @ApiModelProperty(value = "文件类型，1-介质材料附件，2-交付附件，3-rpa附件")
    private Integer fileType;

    /** 子文件类型，1-对账单，2-回单 */
    @Excel(name = "子文件类型，1-对账单，2-回单")
    @TableField("sub_file_type")
    @ApiModelProperty(value = "子文件类型，1-对账单，2-回单")
    private Integer subFileType;

    @Excel(name = "文件序号")
    @TableField("file_no")
    @ApiModelProperty(value = "文件序号")
    private Integer fileNo;

    @Excel(name = "文件备注")
    @TableField("file_remark")
    @ApiModelProperty(value = "文件备注")
    private String fileRemark;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    @Excel(name = "税局下载的报表名称")
    @TableField("offical_filename")
    @ApiModelProperty(value = "税局下载的报表名称")
    private String officalFilename;

    @TableField(exist = false)
    private Long customerServiceId;

    @TableField(exist = false)
    private Long customerServicePeriodMonthId;

    @TableField(exist = false)
    private String bankAccountNumber;
}
