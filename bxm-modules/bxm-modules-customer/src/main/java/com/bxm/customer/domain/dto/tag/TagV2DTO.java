package com.bxm.customer.domain.dto.tag;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TagV2DTO {

    @ApiModelProperty("标签id")
    private Long id;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("完整标签名称")
    private String fullTagName;

    @ApiModelProperty("标签是否含参")
    private Boolean hasParam;

    @ApiModelProperty("是否自定义标签")
    private Boolean isCustomize;

    @ApiModelProperty("是否选中")
    private Boolean isSelected;

    @ApiModelProperty("标签参数值")
    private String paramValue;
}
