package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.NewCustomerTransferDeptConfig;
import com.bxm.customer.mapper.NewCustomerTransferDeptConfigMapper;
import com.bxm.customer.service.INewCustomerTransferDeptConfigService;
import org.springframework.stereotype.Service;

@Service
public class NewCustomerTransferDeptConfigServiceImpl extends ServiceImpl<NewCustomerTransferDeptConfigMapper, NewCustomerTransferDeptConfig> implements INewCustomerTransferDeptConfigService {
    @Override
    public NewCustomerTransferDeptConfig selectByCreateDeptIdAndCreateDeptLevel(Long createDeptId, Integer createDeptLevel) {
        return getOne(new LambdaQueryWrapper<NewCustomerTransferDeptConfig>()
                .eq(NewCustomerTransferDeptConfig::getCreateDeptId, createDeptId)
                .eq(NewCustomerTransferDeptConfig::getCreateDeptLevel, createDeptLevel), false);
    }
}
