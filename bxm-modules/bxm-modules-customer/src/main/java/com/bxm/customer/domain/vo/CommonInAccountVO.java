package com.bxm.customer.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.file.api.domain.RemoteThirdpartFileVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonInAccountVO {

    private String employees;

    private String totalTax;

    private String netProfit;

    private String operatingCosts;

    private String operatingRevenue;

    private String profit;

    private String tempesti;

    private String welfare;

    private String enterain;

    private String rawMaterial;

    private String laborWages;

    private String cost;

    private String periodCost;

    private List<RemoteThirdpartFileVO> files;
}
