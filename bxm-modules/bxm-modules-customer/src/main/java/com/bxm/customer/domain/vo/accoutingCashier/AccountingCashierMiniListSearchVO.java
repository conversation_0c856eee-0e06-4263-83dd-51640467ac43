package com.bxm.customer.domain.vo.accoutingCashier;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierMiniListSearchVO extends BaseVO {

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("批量查询的批次号")
    private String batchNo;

    @ApiModelProperty("服务标签是否包含，0-不包含，1-包含")
    private Integer customerServiceTagIncludeFlag;

    @ApiModelProperty("服务标签名称")
    private String customerServiceTagName;

    @ApiModelProperty("账期标签是否包含，0-不包含，1-包含")
    private Integer periodTagIncludeFlag;

    @ApiModelProperty("账期标签名称")
    private String periodTagName;

    @ApiModelProperty("客户纳税人性质，1-小规模，2-一般纳税人")
    private Integer customerServiceTaxType;

    @ApiModelProperty("账期纳税人性质，1-小规模，2-一般纳税人")
    private Integer periodTaxType;

    @ApiModelProperty("账期开始，yyyyMM")
    private Integer periodMin;

    @ApiModelProperty("账期结束，yyyyMM")
    private Integer periodMax;

    @ApiModelProperty("银行账号搜索")
    private String bankAccountNumber;

    @ApiModelProperty("服务顾问小组id")
    private Long customerServiceAdvisorDeptId;

    @ApiModelProperty("服务会计小组id")
    private Long customerServiceAccountingDeptId;

    @ApiModelProperty("账期顾问小组id")
    private Long periodAdvisorDeptId;

    @ApiModelProperty("账期会计小组id")
    private Long periodAccountingDeptId;

    @ApiModelProperty("组织范围")
    private Long queryDeptId;

    private String deptIds;

    @ApiModelProperty("账务交付单类型，1-入账，2-流水，3-改账")
    private Integer accountingCashierType;

    @ApiModelProperty("统计类型，0-未开户,1-银行部分缺，2-待创建，3-待重提，4-待交付，5-异常，6-有变更，7-缺材料，8-交付待提交，9-待顾问创建，10-待回单中心创建，11-银企待创建")
    private Integer statisticType;

    @ApiModelProperty("交付单状态，多个用逗号隔开，交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，6-交付待提交")
    private String deliverStatusList;
}
