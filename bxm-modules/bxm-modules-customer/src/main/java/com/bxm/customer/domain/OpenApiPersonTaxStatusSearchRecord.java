package com.bxm.customer.domain;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 个税状态查询定时轮询对象 c_open_api_person_tax_status_search_record
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@ApiModel("个税状态查询定时轮询对象")
@Accessors(chain = true)
@TableName("c_open_api_person_tax_status_search_record")
public class OpenApiPersonTaxStatusSearchRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 交付单id */
    @Excel(name = "交付单id")
    @TableField("deliver_id")
    @ApiModelProperty(value = "交付单id")
    private Long deliverId;

    /** 参数 */
    @Excel(name = "参数")
    @TableField("search_status_param")
    @ApiModelProperty(value = "参数")
    private String searchStatusParam;

    /** 应查询时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "应查询时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("search_time")
    @ApiModelProperty(value = "应查询时间")
    private LocalDateTime searchTime;

    /** 状态，1-待查询，2-查询到结果关闭，3-查询6次后关闭 */
    @Excel(name = "状态，1-待查询，2-查询到结果关闭，3-查询6次后关闭")
    @TableField("status")
    @ApiModelProperty(value = "状态，1-待查询，2-查询到结果关闭，3-查询6次后关闭,4-查询中,5-接收变更关闭")
    private Integer status;

}
