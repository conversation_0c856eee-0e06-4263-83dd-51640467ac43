package com.bxm.file.factory;

import com.bxm.file.bean.dto.batchDeliverV2.EnterpriseV2Data;
import com.bxm.file.parser.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ExcelParserV2Factory {

    private final Map<String, ExcelV2Parser<? extends EnterpriseV2Data>> parserMap = new HashMap<>();

    @Autowired
    public ExcelParserV2Factory(MedicalSocialSecurityCreateParser medicalSocialSecurityCreateParser,
                                MedicalSocialSecurityReportParser medicalSocialSecurityReportParser,
                                MedicalSocialSecurityDeductionParser medicalSocialSecurityDeductionParser,
                                MedicalSocialSecurityConfirmParser medicalSocialSecurityConfirmParser,
                                MedicalSocialSecurityRejectParser medicalSocialSecurityRejectParser,
                                MedicalSocialSecurityDealReportExceptionParser medicalSocialSecurityDealReportExceptionParser,
                                MedicalSocialSecurityDealDeductionExceptionParser medicalSocialSecurityDealDeductionExceptionParser,
                                MedicalSocialSecurityUpdateFilesParser medicalSocialSecurityUpdateFilesParser,
                                BusinessTaskBankCreateParser businessTaskBankCreateParser,
                                BusinessTaskBankFinishParser businessTaskBankFinishParser,
                                InAccountRpaParser inAccountRpaParser,
                                CustomerBankAccountParser customerBankAccountParser, CustomerSysAccountParser customerSysAccountParser, CustomerTagParser customerTagParser,
                                SettleAccountsCreateParser settleAccountsCreateParser,
                                SettleAccountsReportParser settleAccountsReportParser, SettleAccountsReportExceptionParser settleAccountsReportExceptionParser,
                                SettleAccountsDeductionParser settleAccountsDeductionParser, SettleAccountsDeductionExceptionParser settleAccountsDeductionExceptionParser,
                                SettleAnnualConfirmParser settleAnnualConfirmParser, SettleAnnualSupplementReportFilesParser settleAnnualSupplementReportFilesParser,
                                AnnualReportCreateParser annualReportCreateParser,
                                AnnualReportReportParser annualReportReportParser, AnnualReportReportExceptionParser annualReportReportExceptionParser,
                                AccountingCashierFlowCreateV2Parser accountingCashierFlowCreateV2Parser, AccountingCashierFlowDeliverParser accountingCashierFlowDeliverParser, AccountingCashierFlowDealExceptionParser accountingCashierFlowDealExceptionParser, AccountingCashierFlowSupplementFileParser accountingCashierFlowSupplementFileParser, AccountingCashierFlowCheckFileParser accountingCashierFlowCheckFileParser,
                                AccountingCashierIncomeCreateParser accountingCashierIncomeCreateParser, AccountingCashierIncomeDeliverParser accountingCashierIncomeDeliverParser, AccountingCashierIncomeDealExceptionParser accountingCashierIncomeDealExceptionParser, AccountingCashierIncomeRpaUpdateParser accountingCashierIncomeRpaUpdateParser, AccountingCashierIncomeUpdateProfitParser accountingCashierIncomeUpdateProfitParser, AccountingCashierIncomeGetProfitParser accountingCashierIncomeGetProfitParser, AccountingCashierIncomeSupplementFileParser accountingCashierIncomeSupplementFileParser, AccountingCashierIncomeCheckFileParser accountingCashierIncomeCheckFileParser,
                                AccountingCashierChangeCreateParser accountingCashierChangeCreateParser, AccountingCashierChangeDeliverParser accountingCashierChangeDeliverParser, AccountingCashierChangeDealExceptionParser accountingCashierChangeDealExceptionParser,
                                MaterialDeliverBankCreateV2Parser materialDeliverBankCreateParser, MaterialDeliverNormalInAccountCreateV2Parser materialDeliverNormalInAccountCreateParser, MaterialDeliverTicketInAccountCreateV2Parser materialDeliverTicketInAccountCreateParser,
                                CommonNoticePeriodParser commonNoticePeriodParser, CommonNoticePeriodBankParser commonNoticePeriodBankParser, CommonNoticePeriodBankMediaParser commonNoticePeriodBankMediaParser,
                                TimesReportCreateParser timesReportCreateParser, TimesReportReportParser timesReportReportParser, TimesReportReportExceptionParser timesReportReportExceptionParser, TimesReportDeductionParser timesReportDeductionParser, TimesReportDeductionExceptionParser timesReportDeductionExceptionParser, TimesReportConfirmParser timesReportConfirmParser, TimesReportSupplementReportFileParser timesReportSupplementReportFileParser,
                                AccountUserCreateParser accountUserCreateParser,
                                QualityCheckingCreateParser qualityCheckingCreateParser
                              ) {
        parserMap.put("51-1", medicalSocialSecurityCreateParser);
        parserMap.put("51-2", medicalSocialSecurityReportParser);
        parserMap.put("51-3", medicalSocialSecurityDealReportExceptionParser);
        parserMap.put("51-4", medicalSocialSecurityDeductionParser);
        parserMap.put("51-5", medicalSocialSecurityDealDeductionExceptionParser);
        parserMap.put("51-6", medicalSocialSecurityConfirmParser);
        parserMap.put("51-7", medicalSocialSecurityUpdateFilesParser);
        parserMap.put("51-8", medicalSocialSecurityRejectParser);
        parserMap.put("52-9", businessTaskBankCreateParser);
        parserMap.put("52-10", businessTaskBankFinishParser);
        parserMap.put("53-11", inAccountRpaParser);
        parserMap.put("54-12", customerBankAccountParser);
        parserMap.put("54-21", customerSysAccountParser);
        parserMap.put("54-22", customerTagParser);
        parserMap.put("55-1", settleAccountsCreateParser);
        parserMap.put("55-2", settleAccountsReportParser);
        parserMap.put("55-3", settleAccountsReportExceptionParser);
        parserMap.put("55-4", settleAccountsDeductionParser);
        parserMap.put("55-5", settleAccountsDeductionExceptionParser);
        parserMap.put("55-6", settleAnnualConfirmParser);
        parserMap.put("55-7", settleAnnualSupplementReportFilesParser);
        parserMap.put("63-1", settleAccountsCreateParser);
        parserMap.put("63-2", settleAccountsReportParser);
        parserMap.put("63-3", settleAccountsReportExceptionParser);
        parserMap.put("63-4", settleAccountsDeductionParser);
        parserMap.put("63-5", settleAccountsDeductionExceptionParser);
        parserMap.put("63-6", settleAnnualConfirmParser);
        parserMap.put("63-7", settleAnnualSupplementReportFilesParser);
        parserMap.put("66-1", timesReportCreateParser);
        parserMap.put("66-2", timesReportReportParser);
        parserMap.put("66-3", timesReportReportExceptionParser);
        parserMap.put("66-4", timesReportDeductionParser);
        parserMap.put("66-5", timesReportDeductionExceptionParser);
        parserMap.put("66-6", timesReportConfirmParser);
        parserMap.put("66-7", timesReportSupplementReportFileParser);
        parserMap.put("56-1", annualReportCreateParser);
        parserMap.put("56-2", annualReportReportParser);
        parserMap.put("56-3", annualReportReportExceptionParser);
        parserMap.put("56-7", settleAnnualSupplementReportFilesParser);
        parserMap.put("57-13", accountingCashierFlowCreateV2Parser);
        parserMap.put("57-14", accountingCashierFlowDeliverParser);
        parserMap.put("57-15", accountingCashierFlowDealExceptionParser);
        parserMap.put("57-19", accountingCashierFlowSupplementFileParser);
        parserMap.put("57-23", accountingCashierFlowCheckFileParser);
        parserMap.put("58-13", accountingCashierIncomeCreateParser);
        parserMap.put("58-14", accountingCashierIncomeDeliverParser);
        parserMap.put("58-15", accountingCashierIncomeDealExceptionParser);
        parserMap.put("58-16", accountingCashierIncomeRpaUpdateParser);
        parserMap.put("58-17", accountingCashierIncomeUpdateProfitParser);
        parserMap.put("58-18", accountingCashierIncomeGetProfitParser);
        parserMap.put("58-19", accountingCashierIncomeSupplementFileParser);
        parserMap.put("58-23", accountingCashierIncomeCheckFileParser);
        parserMap.put("59-13", accountingCashierChangeCreateParser);
        parserMap.put("59-14", accountingCashierChangeDeliverParser);
        parserMap.put("59-15", accountingCashierChangeDealExceptionParser);
        parserMap.put("60-20", materialDeliverBankCreateParser);
        parserMap.put("61-20", materialDeliverNormalInAccountCreateParser);
        parserMap.put("62-20", materialDeliverTicketInAccountCreateParser);
        parserMap.put("64-24", commonNoticePeriodParser);
        parserMap.put("64-25", commonNoticePeriodParser);
        parserMap.put("64-26", commonNoticePeriodBankParser);
        parserMap.put("64-27", commonNoticePeriodBankParser);
        parserMap.put("64-28", commonNoticePeriodBankParser);
        parserMap.put("64-29", commonNoticePeriodBankParser);
        parserMap.put("64-30", commonNoticePeriodBankMediaParser);
        parserMap.put("64-38", commonNoticePeriodParser);
        parserMap.put("65-31", commonNoticePeriodParser);
        parserMap.put("65-32", commonNoticePeriodParser);
        parserMap.put("65-33", commonNoticePeriodParser);
        parserMap.put("65-34", commonNoticePeriodParser);
        parserMap.put("65-35", commonNoticePeriodParser);
        parserMap.put("65-36", commonNoticePeriodParser);
        parserMap.put("65-37", commonNoticePeriodParser);
        parserMap.put("67-1", accountUserCreateParser);
        parserMap.put("68-1", qualityCheckingCreateParser);
    }

    public ExcelV2Parser<? extends EnterpriseV2Data> getParser(int deliverType, int operType) {
        return parserMap.get(deliverType + "-" + operType);
    }
}
