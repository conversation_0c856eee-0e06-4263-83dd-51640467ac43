package com.bxm.file.service;

import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.RemoteCustomerDeliverService;
import com.bxm.customer.api.RemoteCustomerInAccountService;
import com.bxm.file.bean.dto.CheckV2Result;
import com.bxm.file.bean.dto.ConfirmResult;
import com.bxm.file.bean.dto.batchDeliverV2.*;
import com.bxm.file.factory.ExcelParserV2Factory;
import com.bxm.file.parser.ExcelV2Parser;
import com.bxm.file.util.ExcelUtils;
import com.bxm.system.api.RemoteDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeliverBatchV2Service {

    private static final Integer THREAD_POOL_SIZE = 20;

    @Autowired
    private ExcelParserV2Factory excelParserV2Factory;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteCustomerDeliverService remoteCustomerDeliverService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteCustomerInAccountService remoteCustomerInAccountService;

    @Autowired
    private InAccountConcurrentService inAccountConcurrentService;

    @Value("${templateFileVersion}")
    private String templateFileVersion;

    private final Executor taskExecutor;

    // 存储任务标识和线程引用
    private final ConcurrentHashMap<String, Thread> runningThreads = new ConcurrentHashMap<>();

    // 正在执行的解析任务map
    private final ConcurrentHashMap<String, Thread> runningAnalysisThreads = new ConcurrentHashMap<>();

    public DeliverBatchV2Service(@Qualifier("customTaskExecutor") Executor taskExecutor) {
        this.taskExecutor = taskExecutor;
    }

    public String uploadFiles(MultipartFile excelFile, MultipartFile medicalZipFile, MultipartFile socialZipFile, Integer deliverType, Integer operType, Integer period, Long deptId, Long adminUserId, Long commitDeptId, String excelFileUrl, String excelFileName, String medicalZipFileUrl, String medicalZipFileName, MultipartFile zipFile) throws Exception {
        // 判断excelFile是否大于3.5M
        if (null != excelFile && excelFile.getSize() > 7 * 1024 * 512) {
            throw new ServiceException("excel文件大小不能超过3.5M");
        }
        Boolean medicalFileIsZip = checkArchiveType(medicalZipFile);
        Boolean socialFileIsZip = checkArchiveType(socialZipFile);
        Boolean zipIsZip = checkArchiveType(zipFile);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        ExcelV2Parser<? extends EnterpriseV2Data> parser = excelParserV2Factory.getParser(deliverType, operType);
        List<? extends EnterpriseV2Data> dataList = parser.parse(excelFile);
        if (!ObjectUtils.isEmpty(dataList)) {
            dataList.forEach(d -> {
                d.setCheckError("");
                d.setCreditCode(StringUtils.trim(d.getCreditCode()));
            });
        }

        // 将 zip 文件转换为 byte 数组
        byte[] medicalZipBytes = convertToByteArray(medicalZipFile);
        byte[] socialZipBytes = convertToByteArray(socialZipFile);
        byte[] zipBytes = convertToByteArray(zipFile);
        Long userId = SecurityUtils.getUserId();

        // 异步校验和上传数据
        if (deliverType == 51) {
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadDataV2(uuid, dataList, medicalZipBytes, socialZipBytes, period, deptId, userId, operType, deliverType, medicalFileIsZip, socialFileIsZip);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 52) {
            // 银行流水账期任务 为了用V2的流程
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadBankTaskData(uuid, dataList, deptId, userId, operType, deliverType, adminUserId);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 53) {
            // 入账利润取数更新
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadInAccountData(uuid, dataList, deptId, userId, operType, deliverType);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 54) {
            // 客户银行账号更新
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadCustomerBankAccountData(uuid, dataList, deptId, userId, operType, deliverType);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 55 || deliverType == 56 || deliverType == 63) {
            // 汇算和年报的批量交付
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadSettleAnnualData(uuid, dataList, medicalZipBytes, period, deptId, userId, operType, deliverType, medicalFileIsZip);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 57 || deliverType == 58 || deliverType == 59) {
            // 账务批量交付
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadAccountingCashierData(uuid, dataList, zipBytes, deptId, userId, operType, deliverType, zipIsZip);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 60 || deliverType == 61 || deliverType == 62) {
            // 材料交接批量交付
            CompletableFuture.runAsync(() -> {
//                String taskId = UUID.randomUUID().toString().replace("-", "");
//                Thread currentThread = Thread.currentThread();
//                // 将当前线程存储到Map中
//                runningThreads.put(taskId, currentThread);
                try {
                    fileUploadService.validateAndUploadMaterialDeliverData(uuid, dataList, medicalZipBytes, deptId, userId, operType, deliverType, medicalFileIsZip, commitDeptId, excelFileUrl, excelFileName, medicalZipFileUrl, medicalZipFileName);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        }

        return uuid;
    }

    private Path cacheInputStreamToDisk(MultipartFile file) throws IOException {
        if (null == file || file.isEmpty()) {
            return null;
        }
        Path tempFile = Files.createTempFile("upload-", ".tmp");
        try (InputStream inputStream = file.getInputStream();
             OutputStream outputStream = Files.newOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        return tempFile;
    }

    public static void main(String[] args) {
        String str = "111  22 3333   ";
        System.out.println(StringUtils.trim(str));
    }

    public String uploadFilesV2(MultipartFile excelFile, MultipartFile medicalZipFile, MultipartFile socialZipFile, Integer deliverType, Integer operType, Integer period, Long deptId, Long adminUserId, Long commitDeptId, String excelFileUrl, String excelFileName, String medicalZipFileUrl, String medicalZipFileName, MultipartFile zipFile, String title, String qualityCheckingItemIds) throws Exception {
        // 判断excelFile是否大于3.5M
        if (null != excelFile && excelFile.getSize() > 7 * 1024 * 512) {
            throw new ServiceException("excel文件大小不能超过3.5M");
        }
        if (deliverType == 68 && period >= DateUtils.getNowPeriod()) {
            throw new ServiceException("只能选择上月及上月之前的账期");
        }
        Boolean medicalFileIsZip = checkArchiveType(medicalZipFile);
        Boolean socialFileIsZip = checkArchiveType(socialZipFile);
        Boolean zipIsZip = checkArchiveType(zipFile);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        ExcelV2Parser<? extends EnterpriseV2Data> parser = excelParserV2Factory.getParser(deliverType, operType);
        List<? extends EnterpriseV2Data> dataList = parser.parse(excelFile);
        if (!ObjectUtils.isEmpty(dataList)) {
            dataList.forEach(d -> {
                d.setCheckError("");
                if (!StringUtils.isEmpty(d.getCreditCode())) {
                    d.setCreditCode(d.getCreditCode().replaceAll(" ", ""));
                }
                String enterpriseName = invokeGetMethod(d, "getEnterpriseName");
                if (!StringUtils.isEmpty(enterpriseName)) {
                    invokeSetMethod(d, "setEnterpriseName", enterpriseName.replaceAll(" ", ""));
                }
                String bankAccountNumber = invokeGetMethod(d, "getBankAccountNumber");
                if (!StringUtils.isEmpty(bankAccountNumber)) {
                    invokeSetMethod(d, "setBankAccountNumber", bankAccountNumber.replaceAll(" ", ""));
                }
            });
        }

        Path medicalTempFile = cacheInputStreamToDisk(medicalZipFile);
        Path socialTempFile = cacheInputStreamToDisk(socialZipFile);
        Path tempFile = cacheInputStreamToDisk(zipFile);

        Long userId = SecurityUtils.getUserId();

        // 异步校验和上传数据
        if (deliverType == 51) {
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadDataV3(uuid, dataList, medicalTempFile, socialTempFile, period, deptId, userId, operType, deliverType, medicalFileIsZip, socialFileIsZip);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 52) {
            // 银行流水账期任务 为了用V2的流程
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadBankTaskData(uuid, dataList, deptId, userId, operType, deliverType, adminUserId);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 53) {
            // 入账利润取数更新
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadInAccountData(uuid, dataList, deptId, userId, operType, deliverType);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 54) {
            // 客户银行账号更新/系统账号/标签
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadCustomerBankAccountData(uuid, dataList, deptId, userId, operType, deliverType);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 55 || deliverType == 56 || deliverType == 63 || deliverType == 66) {
            // 汇算/年报/残保金/次报 批量交付
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadSettleAnnualDataV2(uuid, dataList, tempFile, period, deptId, userId, operType, deliverType, zipIsZip);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 57 || deliverType == 58 || deliverType == 59) {
            // 账务批量交付
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadAccountingCashierDataV2(uuid, dataList, tempFile, deptId, userId, operType, deliverType, zipIsZip);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 60 || deliverType == 61 || deliverType == 62) {
            // 材料交接批量交付
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadMaterialDeliverDataV2(uuid, dataList, medicalTempFile, deptId, userId, operType, deliverType, medicalFileIsZip, commitDeptId, excelFileUrl, excelFileName, medicalZipFileUrl, medicalZipFileName, title);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 64 || deliverType == 65) {
            // RPA
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadRpaDeliverData(uuid, dataList, deptId, userId, operType, deliverType);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 67) {
            // 账号
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.validateAndUploadAccountUserData(uuid, dataList, deptId, userId, operType, deliverType);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (deliverType == 68) {
            // 质检
            CompletableFuture.runAsync(() -> {
                try {
//                    fileUploadService.validateAndUploadQualityCheckingData(uuid, dataList, deptId, userId, operType, deliverType, period, qualityCheckingItemIds);
                    fileUploadService.validateAndUploadQualityCheckingDataV2(uuid, dataList, deptId, userId, operType, deliverType, period);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        }

        return uuid;
    }

    private String invokeGetMethod(EnterpriseV2Data data, String methodName) {
        try {
            Method method = data.getClass().getMethod(methodName);
            return (String) method.invoke(data);
        } catch (Exception e) {
            return null;
        }
    }

    private void invokeSetMethod(EnterpriseV2Data data, String methodName, String value) {
        try {
            Method method = data.getClass().getMethod(methodName, String.class);
            method.invoke(data, value);
        } catch (Exception e) {
        }
    }

    private Boolean checkArchiveType(MultipartFile zipFile) {
        Boolean isZip = null;
        if (null != zipFile) {
            String fileName = zipFile.getOriginalFilename();
            if (fileName.endsWith(".zip")) {
                isZip = true;
            } else if (fileName.endsWith(".rar")) {
                isZip = false;
            } else {
                throw new ServiceException("附件只支持zip或rar格式");
            }
        }
        return isZip;
    }

    public CheckV2Result getProgress(String batchNo) {
        CheckV2Result checkResult = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_CHECK_RESULT + batchNo);
        if (Objects.isNull(checkResult)) {
            checkResult = new CheckV2Result();
            checkResult.setIsComplete(false);
            Object obj = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_COMPLETE_FILE_COUNT + batchNo);
            Long completeCount = Objects.isNull(obj) ? 0L : Long.parseLong(obj.toString());
            Long totalCount = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_TOTAL_FILE_COUNT + batchNo);
            checkResult.setCompleteFileCount(completeCount);
            checkResult.setTotalFileCount(Objects.isNull(totalCount) ? 0L : totalCount);
            checkResult.setBatchNo(batchNo);
        } else {
            List<? extends EnterpriseV2Data> dataList = redisService.getLargeCacheList(CacheConstants.BATCH_DELIVER_CHECK_LIST_RESULT + batchNo, 2000);
            checkResult.setDataList(dataList);
            checkResult.setHasException(!ObjectUtils.isEmpty(dataList) && dataList.stream().anyMatch(row -> !StringUtils.isEmpty(row.getCheckError())));
        }
        return checkResult;
    }

    public void confirmData(String batchNo, Long deptId) {
        CheckV2Result checkResult = getProgress(batchNo);
        if (!checkResult.getIsComplete()) {
            throw new ServiceException("数据未完成解析");
        }
        List<? extends EnterpriseV2Data> successDatas;
        if (checkResult.getDeliverType() == 51) {
            successDatas = checkResult.getDataList().stream().filter(row -> row.getDoMedical() || row.getDoSocial()).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(successDatas)) {
                ConfirmResult confirmResult = new ConfirmResult(0L, 0L, false, true, 0L, 0L);
                redisService.setCacheObject(CacheConstants.BATCH_DELIVER_CONFIRM_RESULT + checkResult.getBatchNo(), confirmResult, 60 * 60L, TimeUnit.SECONDS);
                return;
            }
        } else {
            successDatas = checkResult.getDataList().stream().filter(row -> StringUtils.isEmpty(row.getCheckError())).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(successDatas)) {
                ConfirmResult confirmResult = new ConfirmResult(0L, 0L, false, true, 0L, 0L);
                redisService.setCacheObject(CacheConstants.BATCH_DELIVER_CONFIRM_RESULT + checkResult.getBatchNo(), confirmResult, 60 * 60L, TimeUnit.SECONDS);
                return;
            }
        }
        Long userId = SecurityUtils.getUserId();
        long totalCount = 0L;
        if (checkResult.getDeliverType() == 51) {
            totalCount += successDatas.stream().filter(EnterpriseV2Data::getDoMedical).count();
            totalCount += successDatas.stream().filter(EnterpriseV2Data::getDoSocial).count();
            // 异步处理数据
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealMedicalSocialSecurity(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    throw new ServiceException("异步处理数据失败，请稍后重试");
                }
            });
        } else if (checkResult.getDeliverType() == 52) {
            // 任务处理
            totalCount = successDatas.size();
            // 异步处理数据
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealBusinessTask(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    throw new ServiceException("异步处理数据失败，请稍后重试");
                }
            });
        } else if (checkResult.getDeliverType() == 53) {
            totalCount = successDatas.size();
            // 异步处理数据
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealInAccountData(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    throw new ServiceException("异步处理数据失败，请稍后重试");
                }
            });
        } else if (checkResult.getDeliverType() == 54) {
            totalCount = successDatas.size();
            // 异步处理数据
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealCustomerBankAccount(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    throw new ServiceException("异步处理数据失败，请稍后重试");
                }
            });
        } else if (checkResult.getDeliverType() == 55 || checkResult.getDeliverType() == 56 || checkResult.getDeliverType() == 63 || checkResult.getDeliverType() == 66) {
            totalCount = successDatas.size();
            // 异步处理数据
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealSettleAnnualData(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    throw new ServiceException("异步处理数据失败，请稍后重试");
                }
            });
        } else if (checkResult.getDeliverType() == 57 || checkResult.getDeliverType() == 58 || checkResult.getDeliverType() == 59) {
            totalCount = successDatas.size();
            // 账务批量交付
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealAccountingCashier(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (checkResult.getDeliverType() == 60 || checkResult.getDeliverType() == 61 || checkResult.getDeliverType() == 62) {
            totalCount = successDatas.size();
            // 材料交接批量交付
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealMaterialDeliver(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (checkResult.getDeliverType() == 64 || checkResult.getDeliverType() == 65) {
            totalCount = successDatas.size();
            // RPA提交
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealRpaDeliver(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (checkResult.getDeliverType() == 67) {
            totalCount = successDatas.size();
            // RPA提交
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealAccountUser(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        } else if (checkResult.getDeliverType() == 68) {
            totalCount = successDatas.size();
            // 质检
            CompletableFuture.runAsync(() -> {
                try {
                    fileUploadService.dealQualityCheckingV2(successDatas, checkResult, deptId, userId);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            });
        }

        ConfirmResult confirmResult = new ConfirmResult(totalCount, 0L, false, false, 0L, 0L);
        redisService.setCacheObject(CacheConstants.BATCH_DELIVER_CONFIRM_RESULT + checkResult.getBatchNo(), confirmResult, 60 * 60L, TimeUnit.SECONDS);
    }

    public void downloadErrorFile(String batchNo, HttpServletResponse response) {
        CheckV2Result checkResult = getProgress(batchNo);
        if (!checkResult.getIsComplete()) {
            throw new ServiceException("数据未完成解析");
        }
        if (!checkResult.getHasException()) {
            throw new ServiceException("暂无数据可下载");
        }
        List<? extends EnterpriseV2Data> errorDatas = checkResult.getDataList().stream().filter(row -> !StringUtils.isEmpty(row.getCheckError())).collect(Collectors.toList());
        String dataType = checkResult.getDeliverType() + "-" + checkResult.getOperType();
        String operTypeName = "";
        if (checkResult.getOperType() == 1) {
            operTypeName = "新建";
        } else if (checkResult.getOperType() == 2) {
            operTypeName = "申报";
        } else if (checkResult.getOperType() == 3) {
            operTypeName = "解除申报异常";
        } else if (checkResult.getOperType() == 4) {
            operTypeName = "扣款";
        } else if (checkResult.getOperType() == 5) {
            operTypeName = "解除扣款异常";
        } else if (checkResult.getOperType() == 6) {
            operTypeName = "确认";
        } else if (checkResult.getOperType() == 7) {
            operTypeName = "更新附件";
        } else if (checkResult.getOperType() == 8) {
            operTypeName = "驳回";
        } else if (checkResult.getOperType() == 9) {
            operTypeName = "创建";
        } else if (checkResult.getOperType() == 10) {
            operTypeName = "完成";
        } else if (checkResult.getOperType() == 11) {
            operTypeName = "利润取数";
        } else if (checkResult.getOperType() == 12) {
            operTypeName = "银行账号";
        } else if (checkResult.getOperType() == 13) {
            operTypeName = "创建";
        } else if (checkResult.getOperType() == 14) {
            operTypeName = "交付";
        } else if (checkResult.getOperType() == 15) {
            operTypeName = "处理异常";
        } else if (checkResult.getOperType() == 16) {
            operTypeName = "RPA更新";
        } else if (checkResult.getOperType() == 17) {
            operTypeName = "利润更新";
        } else if (checkResult.getOperType() == 18) {
            operTypeName = "利润取数";
        } else if (checkResult.getOperType() == 19) {
            operTypeName = "补充材料";
        } else if (checkResult.getOperType() == 20) {
            operTypeName = "新建";
        } else if (checkResult.getOperType() == 21) {
            operTypeName = "系统账号";
        } else if (checkResult.getOperType() == 22) {
            operTypeName = "标签";
        } else if (checkResult.getOperType() == 23) {
            operTypeName = "核对材料";
        } else if (checkResult.getOperType() == 24) {
            operTypeName = "利润取数";
        } else if (checkResult.getOperType() == 25) {
            operTypeName = "发票入账";
        } else if (checkResult.getOperType() == 26) {
            operTypeName = "银行流水银企提取";
        } else if (checkResult.getOperType() == 27) {
            operTypeName = "银行流水纸质上传";
        } else if (checkResult.getOperType() == 28) {
            operTypeName = "银行流水文件检验";
        } else if (checkResult.getOperType() == 29) {
            operTypeName = "银行流水生成凭证";
        } else if (checkResult.getOperType() == 30) {
            operTypeName = "材料入账通知";
        } else if (checkResult.getOperType() == 31) {
            operTypeName = "个税申报";
        } else if (checkResult.getOperType() == 32) {
            operTypeName = "个税扣款";
        } else if (checkResult.getOperType() == 33) {
            operTypeName = "个税检查";
        } else if (checkResult.getOperType() == 34) {
            operTypeName = "个税申报表下载";
        } else if (checkResult.getOperType() == 35) {
            operTypeName = "个税申报结果查询";
        } else if (checkResult.getOperType() == 36) {
            operTypeName = "个税扣款结果查询";
        } else if (checkResult.getOperType() == 37) {
            operTypeName = "个税检查结果查询";
        } else if (checkResult.getOperType() == 38) {
            operTypeName = "自动结账";
        }
        String targetName = "";
        if (checkResult.getDeliverType() == 51) {
            targetName = "医社保";
        } else if (checkResult.getDeliverType() == 52) {
            targetName = "任务";
        } else if (checkResult.getDeliverType() == 53) {
            targetName = "入账";
        } else if (checkResult.getDeliverType() == 54) {
            targetName = "客户";
        } else if (checkResult.getDeliverType() == 55) {
            targetName = "汇算";
        } else if (checkResult.getDeliverType() == 56) {
            targetName = "年报";
        } else if (checkResult.getDeliverType() == 57) {
            targetName = "流水";
        } else if (checkResult.getDeliverType() == 58) {
            targetName = "入账";
        } else if (checkResult.getDeliverType() == 59) {
            targetName = "改账";
        } else if (checkResult.getDeliverType() == 60) {
            targetName = "材料交接（银行流水）";
        } else if (checkResult.getDeliverType() == 61) {
            targetName = "材料交接（普通入账）";
        } else if (checkResult.getDeliverType() == 62) {
            targetName = "材料交接（凭票入账）";
        } else if (checkResult.getDeliverType() == 63) {
            targetName = "残保金";
        } else if (checkResult.getDeliverType() == 64) {
            targetName = "账务";
        } else if (checkResult.getDeliverType() == 65) {
            targetName = "税务";
        } else if (checkResult.getDeliverType() == 66) {
            targetName = "次报";
        } else if (checkResult.getDeliverType() == 67) {
            targetName = "账号";
        } else if (checkResult.getDeliverType() == 68) {
            targetName = "质检";
        }
        String fileName = "异常数据_" + targetName + "_" + operTypeName + (Objects.isNull(checkResult.getPeriod()) ? "" : ("_" + checkResult.getPeriod()));
        switch (dataType) {
            case "51-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalSocialSecurityCreateData) row).collect(Collectors.toList()), MedicalSocialSecurityCreateData.class, fileName);
                break;
            case "51-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalSocialSecurityReportData) row).collect(Collectors.toList()), MedicalSocialSecurityReportData.class, fileName);
                break;
            case "51-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalSocialSecurityDealReportExceptionData) row).collect(Collectors.toList()), MedicalSocialSecurityDealReportExceptionData.class, fileName);
                break;
            case "51-4":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalSocialSecurityDeductionData) row).collect(Collectors.toList()), MedicalSocialSecurityDeductionData.class, fileName);
                break;
            case "51-5":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalSocialSecurityDealDeductionExceptionData) row).collect(Collectors.toList()), MedicalSocialSecurityDealDeductionExceptionData.class, fileName);
                break;
            case "51-6":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalSocialSecurityConfirmData) row).collect(Collectors.toList()), MedicalSocialSecurityConfirmData.class, fileName);
                break;
            case "51-7":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalSocialSecurityUpdateFilesData) row).collect(Collectors.toList()), MedicalSocialSecurityUpdateFilesData.class, fileName);
                break;
            case "51-8":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalSocialSecurityRejectData) row).collect(Collectors.toList()), MedicalSocialSecurityRejectData.class, fileName);
                break;
            case "52-9":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (BusinessTaskBankCreateData) row).collect(Collectors.toList()), BusinessTaskBankCreateData.class, fileName);
                break;
            case "52-10":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (BusinessTaskBankFinishData) row).collect(Collectors.toList()), BusinessTaskBankFinishData.class, fileName);
                break;
            case "53-11":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (InAccountRpaData) row).collect(Collectors.toList()), InAccountRpaData.class, fileName);
                break;
            case "54-12":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CustomerBankAccountData) row).collect(Collectors.toList()), CustomerBankAccountData.class, fileName);
                break;
            case "54-21":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CustomerSysAccountData) row).collect(Collectors.toList()), CustomerSysAccountData.class, fileName);
                break;
            case "54-22":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CustomerTagData) row).collect(Collectors.toList()), CustomerTagData.class, fileName);
                break;
            case "55-1":
            case "63-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SettleAccountsCreateData) row).collect(Collectors.toList()), SettleAccountsCreateData.class, fileName);
                break;
            case "66-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (TimesReportCreateData) row).collect(Collectors.toList()), TimesReportCreateData.class, fileName);
                break;
            case "55-2":
            case "63-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SettleAccountsReportData) row).collect(Collectors.toList()), SettleAccountsReportData.class, fileName);
                break;
            case "66-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (TimesReportReportData) row).collect(Collectors.toList()), TimesReportReportData.class, fileName);
                break;
            case "55-3":
            case "63-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SettleAccountsReportExceptionData) row).collect(Collectors.toList()), SettleAccountsReportExceptionData.class, fileName);
                break;
            case "66-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (TimesReportReportExceptionData) row).collect(Collectors.toList()), TimesReportReportExceptionData.class, fileName);
                break;
            case "55-4":
            case "63-4":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SettleAccountsDeductionData) row).collect(Collectors.toList()), SettleAccountsDeductionData.class, fileName);
                break;
            case "66-4":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (TimesReportDeductionData) row).collect(Collectors.toList()), TimesReportDeductionData.class, fileName);
                break;
            case "55-5":
            case "63-5":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SettleAccountsDeductionExceptionData) row).collect(Collectors.toList()), SettleAccountsDeductionExceptionData.class, fileName);
                break;
            case "66-5":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (TimesReportDeductionExceptionData) row).collect(Collectors.toList()), TimesReportDeductionExceptionData.class, fileName);
                break;
            case "55-6":
            case "63-6":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SettleAccountsConfirmData) row).collect(Collectors.toList()), SettleAccountsConfirmData.class, fileName);
                break;
            case "66-6":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (TimesReportConfirmData) row).collect(Collectors.toList()), TimesReportConfirmData.class, fileName);
                break;
            case "55-7":
            case "56-7":
            case "63-7":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SettleAnnualSupplementReportFileData) row).collect(Collectors.toList()), SettleAnnualSupplementReportFileData.class, fileName);
                break;
            case "66-7":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (TimesReportSupplementReportFileData) row).collect(Collectors.toList()), TimesReportSupplementReportFileData.class, fileName);
                break;
            case "56-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AnnualReportCreateData) row).collect(Collectors.toList()), AnnualReportCreateData.class, fileName);
                break;
            case "56-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AnnualReportReportData) row).collect(Collectors.toList()), AnnualReportReportData.class, fileName);
                break;
            case "56-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AnnualReportReportExceptionData) row).collect(Collectors.toList()), AnnualReportReportExceptionData.class, fileName);
                break;
            case "57-13":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierFlowCreateV2Data) row).collect(Collectors.toList()), AccountingCashierFlowCreateV2Data.class, fileName);
                break;
            case "57-14":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierFlowDeliverData) row).collect(Collectors.toList()), AccountingCashierFlowDeliverData.class, fileName);
                break;
            case "57-15":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierFlowDealExceptionData) row).collect(Collectors.toList()), AccountingCashierFlowDealExceptionData.class, fileName);
                break;
            case "57-19":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierFlowSupplementFileData) row).collect(Collectors.toList()), AccountingCashierFlowSupplementFileData.class, fileName);
                break;
            case "57-23":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierFlowCheckFileData) row).collect(Collectors.toList()), AccountingCashierFlowCheckFileData.class, fileName);
                break;
            case "58-13":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierIncomeCreateData) row).collect(Collectors.toList()), AccountingCashierIncomeCreateData.class, fileName);
                break;
            case "58-14":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierIncomeDeliverData) row).collect(Collectors.toList()), AccountingCashierIncomeDeliverData.class, fileName);
                break;
            case "58-15":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierIncomeDealExceptionData) row).collect(Collectors.toList()), AccountingCashierIncomeDealExceptionData.class, fileName);
                break;
            case "58-16":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierIncomeRpaUpdateData) row).collect(Collectors.toList()), AccountingCashierIncomeRpaUpdateData.class, fileName);
                break;
            case "58-17":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierIncomeUpdateProfitData) row).collect(Collectors.toList()), AccountingCashierIncomeUpdateProfitData.class, fileName);
                break;
            case "58-18":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierIncomeGetProfitData) row).collect(Collectors.toList()), AccountingCashierIncomeGetProfitData.class, fileName);
                break;
            case "58-19":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierIncomeSupplementFileData) row).collect(Collectors.toList()), AccountingCashierIncomeSupplementFileData.class, fileName);
                break;
            case "58-23":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierIncomeCheckFileData) row).collect(Collectors.toList()), AccountingCashierIncomeCheckFileData.class, fileName);
                break;
            case "59-13":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierChangeCreateData) row).collect(Collectors.toList()), AccountingCashierChangeCreateData.class, fileName);
                break;
            case "59-14":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierChangeDeliverData) row).collect(Collectors.toList()), AccountingCashierChangeDeliverData.class, fileName);
                break;
            case "59-15":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountingCashierChangeDealExceptionData) row).collect(Collectors.toList()), AccountingCashierChangeDealExceptionData.class, fileName);
                break;
            case "60-20":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MaterialDeliverBankCreateV2Data) row).collect(Collectors.toList()), MaterialDeliverBankCreateV2Data.class, fileName);
                break;
            case "61-20":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MaterialDeliverNormalInAccountCreateV2Data) row).collect(Collectors.toList()), MaterialDeliverNormalInAccountCreateV2Data.class, fileName);
                break;
            case "62-20":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MaterialDeliverTicketInAccountCreateV2Data) row).collect(Collectors.toList()), MaterialDeliverTicketInAccountCreateV2Data.class, fileName);
                break;
            case "64-24":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "64-25":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "64-26":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodBankData) row).collect(Collectors.toList()), CommonNoticePeriodBankData.class, fileName);
                break;
            case "64-27":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodBankData) row).collect(Collectors.toList()), CommonNoticePeriodBankData.class, fileName);
                break;
            case "64-28":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodBankData) row).collect(Collectors.toList()), CommonNoticePeriodBankData.class, fileName);
                break;
            case "64-29":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodBankData) row).collect(Collectors.toList()), CommonNoticePeriodBankData.class, fileName);
                break;
            case "64-30":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodBankMediaData) row).collect(Collectors.toList()), CommonNoticePeriodBankMediaData.class, fileName);
                break;
            case "64-38":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "65-31":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "65-32":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "65-33":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "65-34":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "65-35":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "65-36":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "65-37":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonNoticePeriodData) row).collect(Collectors.toList()), CommonNoticePeriodData.class, fileName);
                break;
            case "67-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (AccountUserCreateData) row).collect(Collectors.toList()), AccountUserCreateData.class, fileName);
                break;
            case "68-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (QualityCheckingCreateV2Data) row).collect(Collectors.toList()), QualityCheckingCreateV2Data.class, fileName);
                break;
            default:
                throw new ServiceException("不支持的类型");
        }
    }

    private byte[] convertToByteArray(MultipartFile file) throws IOException {
        if (null == file || file.isEmpty()) {
            return null;
        }
        try (InputStream inputStream = file.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }

    public ConfirmResult getConfirmResult(String batchNo) {
        ConfirmResult confirmResult = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_CONFIRM_RESULT + batchNo);
        if (Objects.isNull(confirmResult)) {
            throw new ServiceException("解析未完成");
        }
        if (!confirmResult.getIsComplete()) {
            confirmResult.setIsComplete(false);
            Object obj = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_CONFIRM_DEAL_COUNT + batchNo);
            Long dealCount = Objects.isNull(obj) ? 0L : Long.parseLong(obj.toString());
            confirmResult.setCompleteDataCount(dealCount);
        } else {
            Object successObj = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_CONFIRM_SUCCESS_COUNT + batchNo);
            Object failObj = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_CONFIRM_FAIL_COUNT + batchNo);
            confirmResult.setSuccessDataCount(Objects.isNull(successObj) ? 0L : Long.parseLong(successObj.toString()));
            confirmResult.setFailDataCount(Objects.isNull(failObj) ? 0L : Long.parseLong(failObj.toString()));
            confirmResult.setHasException(confirmResult.getFailDataCount() > 0L);
        }
        return confirmResult;
    }

    public void startThread(String taskId) {
        CompletableFuture.runAsync(() -> {
            Thread currentThread = Thread.currentThread();
            // 将当前线程存储到Map中
            runningThreads.put(taskId, currentThread);

            try {
                while (!Thread.currentThread().isInterrupted()) {
                    System.out.println("任务 " + taskId + " 正在运行...");
                    Thread.sleep(1000); // 模拟长时间任务
                }
            } catch (InterruptedException e) {
                System.out.println("任务 " + taskId + " 被中断！");
                Thread.currentThread().interrupt(); // 恢复中断状态
            } finally {
                runningThreads.remove(taskId); // 任务完成后移除记录
            }
        }, taskExecutor);
    }

    public void stopThread(String taskId) {
        Thread targetThread = runningThreads.get(taskId);
        if (targetThread != null) {
            targetThread.interrupt();
            System.out.println("任务 " + taskId + " 的终止请求已发送！");
        } else {
            System.out.println("未找到任务 " + taskId + "，可能已完成或未启动！");
        }
    }

    public void stopAnalysisTask(String taskId) {
        Thread targetThread = runningAnalysisThreads.get(taskId);
        if (null == targetThread) {
            throw new ServiceException("任务不存在或已完成");
        } else {
            targetThread.interrupt();
        }
    }
}
